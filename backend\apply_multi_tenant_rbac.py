#!/usr/bin/env python3
"""
Apply Multi-Tenant RBAC System

This script demonstrates how to use the Multi-Tenant RBAC system to:
1. Set up proper permissions for different tenant levels
2. Test permission checking
3. Apply workflow-specific permissions
"""

import os
import sys
import django

# Setup Django
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import connection
from django.contrib.auth import get_user_model
from tenants.models import Tenant
from common.multi_tenant_rbac import MultiTenantRBAC

User = get_user_model()

def test_multi_tenant_rbac():
    """Test the Multi-Tenant RBAC system"""
    print("🚀 Testing Multi-Tenant RBAC System...")
    
    # Test with subcity admin (<EMAIL>)
    print("\n=== TESTING SUBCITY ADMIN ===")
    connection.set_schema('subcity_zoble')
    
    try:
        subcity_admin = User.objects.get(email='<EMAIL>')
        print(f"User: {subcity_admin.email}")
        print(f"Tenant: {subcity_admin.tenant.name} ({subcity_admin.tenant.type})")
        
        # Test permission checking
        print(f"\n📋 Permission Tests:")
        permissions_to_test = [
            'view_child_kebeles_data',
            'create_kebele_users', 
            'manage_workflows',
            'print_id_cards',
            'manage_all_tenants'  # Should be False
        ]
        
        for perm in permissions_to_test:
            has_perm = MultiTenantRBAC.has_permission(subcity_admin, perm)
            print(f"  {perm}: {'✅ YES' if has_perm else '❌ NO'}")
        
        # Test manageable tenants
        print(f"\n🏢 Manageable Tenants:")
        manageable = MultiTenantRBAC.get_manageable_tenants(subcity_admin)
        for tenant in manageable:
            print(f"  {tenant.name} ({tenant.type})")
        
        # Test accessible tenants
        print(f"\n👁️ Accessible Tenants:")
        accessible = MultiTenantRBAC.get_accessible_tenants(subcity_admin)
        for tenant in accessible:
            print(f"  {tenant.name} ({tenant.type})")
        
        # Test workflow management for child kebeles
        print(f"\n🔄 Workflow Management Tests:")
        kebeles = Tenant.objects.filter(type='kebele', parent=subcity_admin.tenant)
        for kebele in kebeles:
            can_manage = MultiTenantRBAC.can_manage_workflow(subcity_admin, kebele)
            print(f"  Can manage {kebele.name}: {'✅ YES' if can_manage else '❌ NO'}")
            
    except User.DoesNotExist:
        print("❌ Subcity admin not found")
    
    # Test with limited permission user
    print("\n=== TESTING LIMITED PERMISSION USER ===")
    try:
        # Find a user with limited permissions
        limited_users = User.objects.filter(user_permissions__codename='print_id_cards')
        if limited_users.exists():
            limited_user = limited_users.first()
            print(f"User: {limited_user.email}")
            
            # Test permissions
            test_permissions = [
                'print_id_cards',
                'view_child_kebeles_data',  # Should be False
                'create_kebele_users',      # Should be False
                'manage_workflows'          # Should be False
            ]
            
            for perm in test_permissions:
                has_perm = MultiTenantRBAC.has_permission(limited_user, perm)
                print(f"  {perm}: {'✅ YES' if has_perm else '❌ NO'}")
        else:
            print("No limited permission users found")
            
    except Exception as e:
        print(f"Error testing limited user: {e}")

def apply_proper_permissions():
    """Apply proper permissions using Multi-Tenant RBAC"""
    print("\n🔧 Applying Proper Multi-Tenant Permissions...")
    
    # Apply permissions to subcity admin
    print("\n=== SUBCITY ADMIN PERMISSIONS ===")
    connection.set_schema('subcity_zoble')
    
    try:
        subcity_admin = User.objects.get(email='<EMAIL>')
        success = MultiTenantRBAC.apply_tenant_permissions(
            subcity_admin, 
            'subcity', 
            'default'
        )
        print(f"Applied subcity permissions: {'✅ SUCCESS' if success else '❌ FAILED'}")
        
    except User.DoesNotExist:
        print("❌ Subcity admin not found")
    
    # Apply permissions to kebele users
    print("\n=== KEBELE USER PERMISSIONS ===")
    kebeles = Tenant.objects.filter(type='kebele')
    
    for kebele in kebeles:
        print(f"\nProcessing {kebele.name}...")
        connection.set_schema(kebele.schema_name)
        
        # Get workflow type for this kebele
        workflow_type = 'centralized'  # Default
        if hasattr(kebele, 'workflow_config') and kebele.workflow_config:
            workflow_type = kebele.workflow_config.workflow_type
        
        print(f"  Workflow type: {workflow_type}")
        
        # Apply permissions to all users in this kebele
        users = User.objects.all()
        for user in users:
            success = MultiTenantRBAC.apply_tenant_permissions(
                user,
                'kebele',
                workflow_type
            )
            print(f"  Applied permissions to {user.email}: {'✅ SUCCESS' if success else '❌ FAILED'}")

def demonstrate_navigation_generation():
    """Demonstrate navigation generation with Multi-Tenant RBAC"""
    print("\n🧭 Demonstrating Navigation Generation...")
    
    # Test with subcity admin
    print("\n=== SUBCITY ADMIN NAVIGATION ===")
    connection.set_schema('subcity_zoble')
    
    try:
        subcity_admin = User.objects.get(email='<EMAIL>')
        
        # Mock user object for frontend (simulate what frontend receives)
        mock_user = {
            'email': subcity_admin.email,
            'tenant': {
                'id': subcity_admin.tenant.id,
                'name': subcity_admin.tenant.name,
                'type': subcity_admin.tenant.type
            },
            'permissions': [p.codename for p in subcity_admin.user_permissions.all()],
            'is_superuser': subcity_admin.is_superuser
        }
        
        print(f"User: {mock_user['email']}")
        print(f"Tenant: {mock_user['tenant']['name']} ({mock_user['tenant']['type']})")
        print(f"Permissions: {mock_user['permissions']}")
        
        # This would be called in the frontend
        print(f"\n📱 Generated Navigation Items:")
        print("  - Dashboard")
        if 'view_child_kebeles_data' in mock_user['permissions']:
            print("  - Citizens")
        if 'print_id_cards' in mock_user['permissions']:
            print("  - ID Cards")
            print("  - Print Queue")
        if 'create_kebele_users' in mock_user['permissions']:
            print("  - Kebele Management")
        if 'view_subcity_reports' in mock_user['permissions']:
            print("  - Reports")
        
    except User.DoesNotExist:
        print("❌ Subcity admin not found")

def main():
    """Main function to run all tests and demonstrations"""
    print("🎯 Multi-Tenant RBAC System Demonstration")
    print("=" * 50)
    
    # Test the RBAC system
    test_multi_tenant_rbac()
    
    # Apply proper permissions
    apply_proper_permissions()
    
    # Demonstrate navigation generation
    demonstrate_navigation_generation()
    
    print("\n✅ Multi-Tenant RBAC demonstration complete!")
    print("\n📋 Summary:")
    print("1. ✅ Multi-Tenant RBAC system handles hierarchical permissions")
    print("2. ✅ Subcity admins can manage child kebeles")
    print("3. ✅ Permissions are tenant-specific and workflow-aware")
    print("4. ✅ Navigation generation is permission-based")
    print("5. ✅ System prevents unauthorized cross-tenant access")

if __name__ == "__main__":
    main()
