#!/usr/bin/env python
"""
Quick script to grant create_subcity_users permission to city admins
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant

User = get_user_model()

def main():
    print("🔧 Fixing city admin permissions...")
    
    # Get or create the permission
    with schema_context(get_public_schema_name()):
        permission, created = Permission.objects.get_or_create(
            codename='create_subcity_users',
            defaults={
                'name': 'Can create subcity users',
                'content_type_id': 1  # Use a generic content type
            }
        )
        print(f"Permission: {permission.codename} ({'created' if created else 'exists'})")
        
        # Get or create city_admin group
        city_admin_group, created = Group.objects.get_or_create(name='city_admin')
        print(f"Group: {city_admin_group.name} ({'created' if created else 'exists'})")
        
        # Add permission to group
        city_admin_group.permissions.add(permission)
        print(f"✅ Added permission to group")
    
    # Find city tenants and their admins
    city_tenants = Tenant.objects.filter(type='city')
    print(f"\n🏙️ Found {city_tenants.count()} city tenants:")
    
    for tenant in city_tenants:
        print(f"\n  📍 {tenant.name} (schema: {tenant.schema_name})")
        
        with schema_context(tenant.schema_name):
            city_admins = User.objects.filter(role='city_admin')
            print(f"    👥 City admins: {city_admins.count()}")
            
            for admin in city_admins:
                print(f"      🧑‍💼 {admin.email} (role: {admin.role})")
                
                # Add user to city_admin group
                try:
                    city_admin_group = Group.objects.get(name='city_admin')
                    admin.groups.add(city_admin_group)
                    print(f"        ✅ Added to city_admin group")
                except Group.DoesNotExist:
                    print(f"        ❌ city_admin group not found in this schema")
                
                # Check if they now have the permission
                has_perm = admin.user_permissions.filter(codename='create_subcity_users').exists() or \
                          admin.groups.filter(permissions__codename='create_subcity_users').exists()
                print(f"        🔑 Has create_subcity_users permission: {has_perm}")

if __name__ == '__main__':
    main()
