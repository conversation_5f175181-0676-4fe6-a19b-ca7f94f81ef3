#!/usr/bin/env python3
"""
Test script to verify city admin reports functionality.
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant
from users.models import User

def test_city_admin_reports():
    """Test city admin reports functionality."""
    print("🏙️ Testing City Admin Reports Functionality")
    print("=" * 50)
    
    # Step 1: Find city tenant
    print("\n1. Finding city tenant...")
    try:
        city_tenant = Tenant.objects.filter(type='city').first()
        if not city_tenant:
            print("❌ No city tenant found!")
            return False
        print(f"✅ Found city tenant: {city_tenant.name} (ID: {city_tenant.id})")
    except Exception as e:
        print(f"❌ Error finding city tenant: {e}")
        return False
    
    # Step 2: Find city admin user
    print("\n2. Finding city admin user...")
    test_email = "<EMAIL>"
    
    with schema_context(city_tenant.schema_name):
        try:
            city_admin_user = User.objects.get(email=test_email)
            print(f"✅ Found city admin: {city_admin_user.email} (Role: {city_admin_user.role})")
        except User.DoesNotExist:
            print(f"❌ City admin user {test_email} not found!")
            print("   Creating city admin user...")
            city_admin_user = User.objects.create_user(
                email=test_email,
                username="test_city_admin",
                password="testpass123",
                role="city_admin",
                tenant=city_tenant,
                first_name="Test",
                last_name="CityAdmin"
            )
            print(f"✅ Created city admin user: {city_admin_user.email}")
    
    # Step 3: Test login
    print("\n3. Testing city admin login...")
    login_response = requests.post('http://localhost:8000/api/auth/token/', json={
        'email': test_email,
        'password': 'testpass123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
        return False
    
    login_data = login_response.json()
    access_token = login_data.get('access')
    print(f"✅ Login successful, got access token")
    
    # Step 4: Test city dashboard reports endpoint
    print("\n4. Testing city dashboard reports endpoint...")
    headers = {'Authorization': f'Bearer {access_token}'}
    
    reports_response = requests.get(
        f'http://localhost:8000/api/tenants/{city_tenant.id}/citizens/city-dashboard/reports/',
        headers=headers
    )
    
    if reports_response.status_code != 200:
        print(f"❌ City dashboard reports failed: {reports_response.status_code} - {reports_response.text}")
        return False
    
    reports_data = reports_response.json()
    print(f"✅ City dashboard reports successful")
    print(f"   📊 Total citizens 18+: {reports_data.get('total_citizens_18_plus', 0)}")
    print(f"   🏢 Child subcities: {len(reports_data.get('population_by_subcity', []))}")
    
    # Step 5: Test city anonymized citizens endpoint
    print("\n5. Testing city anonymized citizens endpoint...")
    
    anonymized_response = requests.get(
        f'http://localhost:8000/api/tenants/{city_tenant.id}/citizens/city-anonymized-citizens/',
        headers=headers
    )
    
    if anonymized_response.status_code != 200:
        print(f"❌ City anonymized citizens failed: {anonymized_response.status_code} - {anonymized_response.text}")
        return False
    
    anonymized_data = anonymized_response.json()
    print(f"✅ City anonymized citizens successful")
    print(f"   📋 Anonymized records: {len(anonymized_data)}")
    
    # Step 6: Test export endpoints
    print("\n6. Testing export endpoints...")
    
    # Test PDF export
    pdf_response = requests.post(
        f'http://localhost:8000/api/tenants/{city_tenant.id}/citizens/export-anonymized-pdf/',
        headers=headers
    )
    
    if pdf_response.status_code == 200:
        print(f"✅ PDF export successful (Content-Type: {pdf_response.headers.get('content-type')})")
    else:
        print(f"❌ PDF export failed: {pdf_response.status_code} - {pdf_response.text}")
    
    # Test Excel export
    excel_response = requests.post(
        f'http://localhost:8000/api/tenants/{city_tenant.id}/citizens/export-anonymized-excel/',
        headers=headers
    )
    
    if excel_response.status_code == 200:
        print(f"✅ Excel export successful (Content-Type: {excel_response.headers.get('content-type')})")
    else:
        print(f"❌ Excel export failed: {excel_response.status_code} - {excel_response.text}")
    
    print("\n🎉 City admin reports testing completed!")
    print(f"\n📋 Test Results Summary:")
    print(f"   🏛️  City Tenant: {city_tenant.name}")
    print(f"   👤 City Admin: {test_email}")
    print(f"   📊 Dashboard Reports: {'✅ Working' if reports_response.status_code == 200 else '❌ Failed'}")
    print(f"   📋 Anonymized Data: {'✅ Working' if anonymized_response.status_code == 200 else '❌ Failed'}")
    print(f"   📄 PDF Export: {'✅ Working' if pdf_response.status_code == 200 else '❌ Failed'}")
    print(f"   📊 Excel Export: {'✅ Working' if excel_response.status_code == 200 else '❌ Failed'}")
    
    return True

if __name__ == '__main__':
    test_city_admin_reports()
