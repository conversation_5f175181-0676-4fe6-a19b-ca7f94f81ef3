#!/usr/bin/env python3
"""
Debug script to identify the 500 error in printing queue endpoint.
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from tenants.views.tenant_idcard_views import TenantIDCardViewSet
from django.test import RequestFactory
import traceback

User = get_user_model()

def debug_printing_queue_500():
    """Debug the 500 error in printing queue."""
    
    print("🔍 Debugging Printing Queue 500 Error")
    print("="*50)
    
    try:
        # Find kebele and designated_printer user
        kebele = Tenant.objects.filter(type='kebele', name__icontains='kebele15').first()
        if not kebele:
            kebele = Tenant.objects.filter(type='kebele').first()
        
        if not kebele:
            print("❌ No kebele found!")
            return
        
        print(f"📋 Testing with kebele: {kebele.name} (ID: {kebele.id})")
        
        # Find designated_printer user
        user = User.objects.filter(role='designated_printer').first()
        if not user:
            print("❌ No designated_printer user found!")
            return
        
        print(f"👤 Testing with user: {user.email} (Role: {user.role})")
        
        # Create mock request
        factory = RequestFactory()
        request = factory.get(f'/api/tenants/{kebele.id}/idcards/printing_queue/')
        request.user = user
        
        # Test the viewset
        viewset = TenantIDCardViewSet()
        viewset.request = request
        viewset.kwargs = {'tenant_id': kebele.id}
        
        print("\n🧪 Testing printing_queue method...")
        
        try:
            response = viewset.printing_queue(request, tenant_id=kebele.id)
            print(f"✅ Success! Status: {response.status_code}")
            print(f"Response data: {response.data}")
            
        except Exception as e:
            print(f"❌ Error in printing_queue method: {e}")
            print("\n📋 Full traceback:")
            traceback.print_exc()
            
            # Try to identify the specific issue
            print(f"\n🔍 Debugging specific issues:")
            
            # Check if kebele has workflow config
            try:
                workflow_config = kebele.workflow_config
                print(f"✅ Workflow config exists: {workflow_config.workflow_type}")
            except Exception as wf_error:
                print(f"❌ Workflow config error: {wf_error}")
            
            # Check schema context
            try:
                from django_tenants.utils import schema_context
                with schema_context(kebele.schema_name):
                    print(f"✅ Schema context works: {kebele.schema_name}")
            except Exception as schema_error:
                print(f"❌ Schema context error: {schema_error}")
            
            # Check IDCard model import
            try:
                from idcards.models import IDCard, IDCardStatus
                print(f"✅ IDCard model imported successfully")
                
                # Test query in schema context
                with schema_context(kebele.schema_name):
                    count = IDCard.objects.count()
                    print(f"✅ IDCard query works: {count} cards found")
                    
            except Exception as model_error:
                print(f"❌ IDCard model error: {model_error}")
            
            # Check Citizen model import
            try:
                with schema_context(kebele.schema_name):
                    from citizens.models import Citizen
                    citizen_count = Citizen.objects.count()
                    print(f"✅ Citizen model works: {citizen_count} citizens found")
                    
            except Exception as citizen_error:
                print(f"❌ Citizen model error: {citizen_error}")
        
    except Exception as e:
        print(f"❌ Setup error: {e}")
        traceback.print_exc()

def test_simple_printing_queue():
    """Test a simplified version of the printing queue logic."""
    print("\n🧪 Testing Simplified Printing Queue Logic")
    print("-" * 40)
    
    try:
        kebele = Tenant.objects.filter(type='kebele').first()
        if not kebele:
            print("❌ No kebele found")
            return
        
        print(f"Testing with: {kebele.name}")
        
        # Test workflow config
        try:
            workflow_type = kebele.workflow_config.workflow_type
            print(f"✅ Workflow type: {workflow_type}")
        except:
            print("❌ No workflow config - creating one...")
            from tenants.models_workflow import TenantWorkflowConfig
            config = TenantWorkflowConfig.objects.create(
                tenant=kebele,
                workflow_type='autonomous'
            )
            print(f"✅ Created workflow config: {config.workflow_type}")
        
        # Test schema and models
        from django_tenants.utils import schema_context
        with schema_context(kebele.schema_name):
            from idcards.models import IDCard, IDCardStatus
            from citizens.models import Citizen
            
            citizen_count = Citizen.objects.count()
            idcard_count = IDCard.objects.count()
            approved_count = IDCard.objects.filter(status=IDCardStatus.APPROVED).count()
            
            print(f"✅ Citizens: {citizen_count}")
            print(f"✅ ID Cards: {idcard_count}")
            print(f"✅ Approved Cards: {approved_count}")
            
            # Test the actual query
            active_citizen_ids = Citizen.objects.filter(is_active=True).values_list('id', flat=True)
            approved_cards = IDCard.objects.filter(
                status=IDCardStatus.APPROVED,
                citizen_id__in=active_citizen_ids
            ).select_related('citizen', 'approved_by')
            
            print(f"✅ Printable cards query: {approved_cards.count()}")
            
            for card in approved_cards[:3]:  # Show first 3
                print(f"  • Card {card.card_number} for {card.citizen.first_name} {card.citizen.last_name}")
        
    except Exception as e:
        print(f"❌ Simplified test error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_printing_queue_500()
    test_simple_printing_queue()
