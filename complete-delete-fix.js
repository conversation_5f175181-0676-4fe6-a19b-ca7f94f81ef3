// Complete Delete Dialog Fix for Both Kebele and Subcity Management
// This script fixes both the dialog appearance and the automatic refresh issue

console.log('🔍 Applying Complete Delete Dialog Fix...');

// Store the original confirm function
const originalConfirm = window.confirm;

// Override window.confirm
window.confirm = function(message) {
    console.log('🔍 Confirm intercepted:', message);
    
    // Check if this is a delete user confirmation
    if (message.includes('delete the user') || message.includes('Are you sure you want to delete')) {
        console.log('🔍 Showing custom delete dialog');
        
        // Extract user info from the message if possible
        const userMatch = message.match(/"([^"]+)"/);
        const userName = userMatch ? userMatch[1] : 'Selected User';
        const emailMatch = message.match(/\(([^)]+)\)/);
        const userEmail = emailMatch ? emailMatch[1] : '<EMAIL>';
        
        // Create the custom dialog
        return new Promise((resolve) => {
            // Create overlay
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
            `;
            
            // Create dialog
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background-color: white;
                border-radius: 8px;
                max-width: 500px;
                width: 90%;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                overflow: hidden;
                border-top: 4px solid #f44336;
            `;
            
            dialog.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px; padding: 16px 24px; color: #f44336; border-bottom: 1px solid #eee;">
                    <span style="font-size: 24px;">🗑️</span>
                    <span style="font-weight: bold; font-size: 18px;">Confirm User Deletion</span>
                </div>
                <div style="padding: 16px 24px;">
                    <p style="margin: 0 0 16px 0;">Are you sure you want to delete the following user?</p>
                    
                    <div style="background-color: #f5f5f5; border-radius: 8px; padding: 16px; margin: 16px 0;">
                        <div style="font-weight: bold; margin-bottom: 8px;">${userName}</div>
                        <div style="color: #666; margin-bottom: 4px;">Email: ${userEmail}</div>
                        <div style="color: #666;">Role: User</div>
                    </div>
                    
                    <p style="color: #f44336; margin: 16px 0 0 0; font-weight: 500;">
                        This action cannot be undone. The user will be permanently removed from the system.
                    </p>
                </div>
                <div style="display: flex; justify-content: flex-end; padding: 16px 24px; gap: 8px;">
                    <button id="cancelBtn" style="
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: 500;
                        cursor: pointer;
                        background-color: white;
                        border: 1px solid #ccc;
                        color: #333;
                    ">Cancel</button>
                    <button id="deleteBtn" style="
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: 500;
                        cursor: pointer;
                        background-color: #f44336;
                        color: white;
                        border: none;
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    ">
                        <span style="font-size: 16px;">🗑️</span>
                        Delete User
                    </button>
                </div>
            `;
            
            overlay.appendChild(dialog);
            document.body.appendChild(overlay);
            
            // Add event listeners
            const cancelBtn = dialog.querySelector('#cancelBtn');
            const deleteBtn = dialog.querySelector('#deleteBtn');
            
            cancelBtn.addEventListener('click', () => {
                overlay.remove();
                resolve(false);
            });
            
            deleteBtn.addEventListener('click', () => {
                overlay.remove();
                
                // After confirming deletion, set up a listener to refresh the page
                // when the deletion is complete
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const result = originalFetch.apply(this, args);
                    
                    // Check if this is a delete user request
                    if (args[0] && args[0].includes('/delete_user/') && args[1] && args[1].method === 'DELETE') {
                        console.log('🔍 Delete user request detected, setting up refresh...');
                        
                        result.then(response => {
                            if (response.ok) {
                                console.log('🔍 Delete successful, refreshing page in 1 second...');
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        }).catch(error => {
                            console.error('Delete request failed:', error);
                        });
                    }
                    
                    return result;
                };
                
                resolve(true);
            });
            
            // Close on overlay click
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    overlay.remove();
                    resolve(false);
                }
            });
        });
    }
    
    // For other confirm dialogs, use the original function
    return originalConfirm(message);
};

console.log('✅ Complete Delete Dialog Fix Applied!');
console.log('✅ Custom dialog will show for delete confirmations');
console.log('✅ Page will auto-refresh after successful deletion');
