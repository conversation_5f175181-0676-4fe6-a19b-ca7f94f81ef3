#!/usr/bin/env python3
"""
Quick fix script to ensure print_id_cards role groups are properly created and configured.

This script will:
1. Create missing print_id_cards Django Group
2. Create missing print_id_cards TenantGroup
3. Set proper permissions and configurations
4. Test the API endpoint
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from users.models_groups import TenantGroup
from django.db import transaction

User = get_user_model()

def fix_print_role_groups():
    """Fix print_id_cards role groups and configurations."""
    
    print("🔧 Fixing Print ID Cards Role Groups")
    print("="*50)
    
    try:
        with transaction.atomic():
            # Step 1: Create Django Group
            print("\n1️⃣ Creating Django Group...")
            django_group, created = Group.objects.get_or_create(name='print_id_cards')
            
            if created:
                print("✅ Created Django Group 'print_id_cards'")
            else:
                print("⏭️  Django Group 'print_id_cards' already exists")
            
            # Step 2: Create print_id_cards permission
            print("\n2️⃣ Creating print_id_cards permission...")
            user_ct = ContentType.objects.get_for_model(User)
            
            permission, created = Permission.objects.get_or_create(
                codename='print_id_cards',
                defaults={
                    'name': 'Can print ID cards',
                    'content_type': user_ct
                }
            )
            
            if created:
                print("✅ Created permission 'print_id_cards'")
            else:
                print("⏭️  Permission 'print_id_cards' already exists")
            
            # Add permission to group
            django_group.permissions.add(permission)
            print("✅ Added print_id_cards permission to group")
            
            # Step 3: Create TenantGroup
            print("\n3️⃣ Creating TenantGroup...")
            tenant_group, created = TenantGroup.objects.get_or_create(
                group=django_group,
                tenant=None,  # Global group
                defaults={
                    'description': 'Print ID cards role for autonomous kebeles and subcity printing',
                    'group_type': 'operational',
                    'level': 10,
                    'is_active': True,
                    'allowed_tenant_types': ['kebele', 'subcity']  # Allow for both kebele and subcity
                }
            )
            
            if created:
                print("✅ Created TenantGroup 'print_id_cards'")
            else:
                print("⏭️  TenantGroup 'print_id_cards' already exists")
                # Update configuration if needed
                if not tenant_group.is_active:
                    tenant_group.is_active = True
                    tenant_group.save()
                    print("✅ Activated TenantGroup 'print_id_cards'")
                
                # Ensure allowed tenant types include kebele and subcity
                if 'kebele' not in tenant_group.allowed_tenant_types:
                    tenant_group.allowed_tenant_types.append('kebele')
                if 'subcity' not in tenant_group.allowed_tenant_types:
                    tenant_group.allowed_tenant_types.append('subcity')
                tenant_group.save()
                print("✅ Updated allowed tenant types for TenantGroup")
            
            # Step 4: Create other required groups
            print("\n4️⃣ Creating other required groups...")
            
            required_groups = [
                ('clerk', 'Clerk role for citizen registration and ID card generation', 'operational', 5),
                ('kebele_leader', 'Kebele leader role for approvals and management', 'administrative', 20),
                ('subcity_admin', 'Subcity administrator role', 'administrative', 30),
                ('subcity_system_admin', 'Subcity system administrator role', 'administrative', 35),
                ('city_admin', 'City administrator role', 'administrative', 40),
                ('city_system_admin', 'City system administrator role', 'administrative', 45),
            ]
            
            for group_name, description, group_type, level in required_groups:
                # Create Django Group
                django_group, created = Group.objects.get_or_create(name=group_name)
                
                # Create TenantGroup
                tenant_group, created = TenantGroup.objects.get_or_create(
                    group=django_group,
                    tenant=None,
                    defaults={
                        'description': description,
                        'group_type': group_type,
                        'level': level,
                        'is_active': True,
                        'allowed_tenant_types': []  # No restrictions for these groups
                    }
                )
                
                if created:
                    print(f"✅ Created TenantGroup '{group_name}'")
                else:
                    print(f"⏭️  TenantGroup '{group_name}' already exists")
            
            # Step 5: Test with a kebele
            print("\n5️⃣ Testing with kebele...")
            
            # Find or create test kebele
            kebele = Tenant.objects.filter(type='kebele').first()
            if kebele:
                print(f"Testing with kebele: {kebele.name}")
                
                # Ensure it has autonomous workflow
                workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                    tenant=kebele,
                    defaults={'workflow_type': 'autonomous'}
                )
                
                if workflow_config.workflow_type != 'autonomous':
                    workflow_config.workflow_type = 'autonomous'
                    workflow_config.save()
                    print(f"✅ Set {kebele.name} to autonomous workflow")
                
                # Test the filtering logic
                from django.db.models import Q
                
                allowed_roles = ['clerk', 'kebele_leader', 'print_id_cards']  # For autonomous
                
                queryset = TenantGroup.objects.filter(
                    Q(tenant__isnull=True) & Q(group__name__in=allowed_roles),
                    is_active=True
                )
                
                print(f"Query results for autonomous kebele:")
                for group in queryset:
                    print(f"  • {group.group.name}")
                
                if queryset.filter(group__name='print_id_cards').exists():
                    print("✅ print_id_cards group found in query!")
                else:
                    print("❌ print_id_cards group NOT found in query!")
            
            print("\n✅ Print ID Cards Role Groups Fix Completed!")
            print("\n🎯 Next Steps:")
            print("1. Restart Django server: python manage.py runserver")
            print("2. Clear browser cache and refresh page")
            print("3. Try creating a user in an autonomous kebele")
            print("4. Check that print_id_cards appears in dropdown")
            
    except Exception as e:
        print(f"❌ Error fixing print role groups: {e}")
        import traceback
        traceback.print_exc()

def test_api_endpoint():
    """Test the API endpoint to see what groups are returned."""
    print("\n🧪 Testing API Endpoint...")
    
    from users.views_groups import GroupManagementViewSet
    from django.test import RequestFactory
    
    # Find a kebele
    kebele = Tenant.objects.filter(type='kebele').first()
    if not kebele:
        print("❌ No kebele found for testing")
        return
    
    # Create test user
    user = User.objects.filter(is_superuser=True).first()
    if not user:
        print("❌ No superuser found for testing")
        return
    
    # Create mock request
    factory = RequestFactory()
    request = factory.get(f'/api/auth/group-management/tenant_groups/?tenant_id={kebele.id}')
    request.user = user
    
    # Call API
    viewset = GroupManagementViewSet()
    viewset.request = request
    
    try:
        response = viewset.tenant_groups(request)
        print(f"API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            groups = response.data.get('groups', [])
            print(f"Groups returned for {kebele.name}:")
            for group in groups:
                print(f"  • {group.get('name')}")
            
            if any(g.get('name') == 'print_id_cards' for g in groups):
                print("✅ print_id_cards found in API response!")
            else:
                print("❌ print_id_cards NOT found in API response!")
        else:
            print(f"❌ API error: {response.data}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

if __name__ == "__main__":
    fix_print_role_groups()
    test_api_endpoint()
