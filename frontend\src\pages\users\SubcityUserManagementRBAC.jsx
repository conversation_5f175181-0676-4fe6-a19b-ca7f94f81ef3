import React from 'react';
import { useLocalization } from '../../contexts/LocalizationContext';
import {
  Box,
  Typography,
  Paper,
  Alert,
  Chip
} from '@mui/material';
import {
  LocationCity as LocationCityIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import RBACUserManagement from '../../components/UserManagement/RBACUserManagement';
import { usePermissions } from '../../hooks/usePermissions';
import { hasPermission } from '../../utils/multiTenantRBAC';

const SubcityUserManagementRBAC = () => {
  const { t } = useLocalization();
  const { user, dynamicRole } = usePermissions();

  // Check if user has permission to manage kebele users
  const canManageKebeleUsers = hasPermission(user, 'create_kebele_users');

  if (!user) {
    return <div>Loading...</div>;
  }

  if (!canManageKebeleUsers) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <SecurityIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            You don't have permission to manage kebele users.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Required permission: <Chip label="create_kebele_users" size="small" />
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Your current role: <Chip label={dynamicRole} size="small" color="primary" />
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocationCityIcon />
          {t('subcity_user_management', 'Subcity User Management')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('manage_users_for_kebeles_under_subcity', 'Manage users for kebeles under your subcity administration')}
        </Typography>
      </Box>

      {/* Permission Info */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>{t('subcity_admin_permissions', 'Subcity Admin Permissions')}:</strong> {t('can_create_manage_users_kebeles', 'You can create and manage users for kebeles that belong to your subcity.')}
          {' '}{t('users_created_access_respective_tenants', 'Users created will have access to their respective kebele tenants.')}
        </Typography>
      </Alert>

      {/* User Management Component */}
      <RBACUserManagement 
        tenantType="kebele"
        currentUser={user}
      />

      {/* Help Section */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Available Roles for Kebele Users
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          <Chip 
            label="Kebele Leader" 
            color="primary" 
            variant="outlined"
            size="small"
          />
          <Chip 
            label="Registration Clerk" 
            color="secondary" 
            variant="outlined"
            size="small"
          />
          <Chip 
            label="Print Operator" 
            color="warning" 
            variant="outlined"
            size="small"
          />
        </Box>
        <Typography variant="body2" color="text.secondary">
          • <strong>Kebele Leader:</strong> Can approve ID cards and verify documents locally<br/>
          • <strong>Registration Clerk:</strong> Can register citizens and generate ID cards<br/>
          • <strong>Print Operator:</strong> Can print ID cards and manage print queue
        </Typography>
      </Paper>

      {/* Workflow Information */}
      <Paper sx={{ p: 3, mt: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          {t('workflow_management', 'Workflow Management')}
        </Typography>
        <Typography variant="body2">
          {t('subcity_admin_can_manage_workflow_types', 'As a subcity admin, you can also manage workflow types for your kebeles:')}
        </Typography>
        <Box sx={{ mt: 1 }}>
          <Typography variant="body2">
            • <strong>{t('centralized_workflow', 'Centralized Workflow')}:</strong> {t('id_cards_require_subcity_approval', 'ID cards require subcity approval')}
          </Typography>
          <Typography variant="body2">
            • <strong>{t('autonomous_workflow', 'Autonomous Workflow')}:</strong> {t('kebeles_can_approve_print_locally', 'Kebeles can approve and print locally')}
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default SubcityUserManagementRBAC;
