#!/usr/bin/env python3
"""
Debug script to identify why print_id_cards role is not showing in the dropdown.

This script will:
1. Check if print_id_cards group exists
2. Verify workflow configuration
3. Test the API endpoint directly
4. Check group filtering logic
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from users.models_groups import TenantGroup
from users.views_groups import GroupManagementViewSet
from django.test import RequestFactory
import json

User = get_user_model()

def debug_print_role_issue():
    """Debug why print_id_cards role is not appearing."""
    
    print("🔍 Debugging Print ID Cards Role Issue")
    print("="*60)
    
    # Find Kebele15 (the one in the screenshot)
    kebele = Tenant.objects.filter(name__icontains='kebele15').first()
    if not kebele:
        kebele = Tenant.objects.filter(type='kebele').first()
    
    if not kebele:
        print("❌ No kebele found!")
        return
    
    print(f"📋 Debugging kebele: {kebele.name} (ID: {kebele.id})")
    
    # Check workflow configuration
    print("\n1️⃣ WORKFLOW CONFIGURATION")
    print("-" * 30)
    
    try:
        workflow_config = kebele.workflow_config
        print(f"Workflow type: {workflow_config.workflow_type}")
        print(f"Workflow config exists: ✅")
    except:
        print("❌ No workflow configuration found!")
        # Create one
        workflow_config = TenantWorkflowConfig.objects.create(
            tenant=kebele,
            workflow_type='autonomous'
        )
        print(f"✅ Created autonomous workflow config")
    
    # Check if print_id_cards group exists
    print("\n2️⃣ GROUP EXISTENCE CHECK")
    print("-" * 30)
    
    # Check Django Group
    django_group = Group.objects.filter(name='print_id_cards').first()
    if django_group:
        print(f"✅ Django Group 'print_id_cards' exists (ID: {django_group.id})")
    else:
        print("❌ Django Group 'print_id_cards' does not exist!")
        # Create it
        django_group = Group.objects.create(name='print_id_cards')
        print(f"✅ Created Django Group 'print_id_cards'")
    
    # Check TenantGroup
    tenant_group = TenantGroup.objects.filter(group__name='print_id_cards').first()
    if tenant_group:
        print(f"✅ TenantGroup 'print_id_cards' exists (ID: {tenant_group.id})")
        print(f"   Tenant: {tenant_group.tenant}")
        print(f"   Active: {tenant_group.is_active}")
        print(f"   Allowed tenant types: {tenant_group.allowed_tenant_types}")
    else:
        print("❌ TenantGroup 'print_id_cards' does not exist!")
        # Create it
        tenant_group = TenantGroup.objects.create(
            group=django_group,
            tenant=None,  # Global group
            description="Print ID cards role for autonomous kebeles",
            group_type='operational',
            level=10,
            is_active=True,
            allowed_tenant_types=['kebele']
        )
        print(f"✅ Created TenantGroup 'print_id_cards'")
    
    # Test API endpoint directly
    print("\n3️⃣ API ENDPOINT TEST")
    print("-" * 30)
    
    # Create test user
    user = User.objects.filter(is_superuser=True).first()
    if not user:
        user = User.objects.create_superuser(
            username='debug_admin',
            email='<EMAIL>',
            password='debugpass123'
        )
    
    # Create mock request
    factory = RequestFactory()
    request = factory.get(f'/api/auth/group-management/tenant_groups/?tenant_id={kebele.id}')
    request.user = user
    
    # Call API
    viewset = GroupManagementViewSet()
    viewset.request = request
    
    try:
        response = viewset.tenant_groups(request)
        print(f"API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.data
            groups = data.get('groups', [])
            print(f"Number of groups returned: {len(groups)}")
            
            print("\nGroups returned:")
            for group in groups:
                print(f"  • {group.get('name', 'Unknown')} (ID: {group.get('id', 'Unknown')})")
                print(f"    Type: {group.get('group_type', 'Unknown')}")
                print(f"    Level: {group.get('level', 'Unknown')}")
                print(f"    Active: {group.get('is_active', 'Unknown')}")
            
            # Check if print_id_cards is in the list
            print_role_found = any(group.get('name') == 'print_id_cards' for group in groups)
            if print_role_found:
                print("\n✅ print_id_cards role found in API response!")
            else:
                print("\n❌ print_id_cards role NOT found in API response!")
        else:
            print(f"❌ API call failed: {response.data}")
            
    except Exception as e:
        print(f"❌ API call error: {e}")
        import traceback
        traceback.print_exc()
    
    # Check filtering logic manually
    print("\n4️⃣ MANUAL FILTERING TEST")
    print("-" * 30)
    
    workflow_type = workflow_config.workflow_type
    print(f"Workflow type: {workflow_type}")
    
    if kebele.type == 'kebele':
        allowed_roles = ['clerk', 'kebele_leader']
        
        if workflow_type == 'autonomous':
            allowed_roles.append('print_id_cards')
            print(f"✅ Added print_id_cards for autonomous workflow")
        else:
            print(f"❌ print_id_cards not added (workflow is {workflow_type})")
        
        print(f"Expected roles: {allowed_roles}")
    
    # Check database query
    print("\n5️⃣ DATABASE QUERY TEST")
    print("-" * 30)
    
    from django.db.models import Q
    
    # Simulate the query from the API
    allowed_roles = ['clerk', 'kebele_leader']
    if workflow_type == 'autonomous':
        allowed_roles.append('print_id_cards')
    
    queryset = TenantGroup.objects.filter(
        Q(tenant__isnull=True) & Q(group__name__in=allowed_roles),
        is_active=True
    ).select_related('tenant', 'created_by').prefetch_related('group__permissions')
    
    print(f"Query filter: tenant__isnull=True & group__name__in={allowed_roles}")
    print(f"Results count: {queryset.count()}")
    
    for group in queryset:
        print(f"  • {group.group.name} (TenantGroup ID: {group.id})")
    
    # Check if print_id_cards group matches the query
    if workflow_type == 'autonomous':
        print_group_query = TenantGroup.objects.filter(
            group__name='print_id_cards',
            tenant__isnull=True,
            is_active=True
        )
        print(f"\nSpecific print_id_cards query count: {print_group_query.count()}")
        
        if print_group_query.exists():
            group = print_group_query.first()
            print(f"✅ print_id_cards group found:")
            print(f"   Name: {group.group.name}")
            print(f"   Tenant: {group.tenant}")
            print(f"   Active: {group.is_active}")
            print(f"   Allowed types: {group.allowed_tenant_types}")
        else:
            print("❌ print_id_cards group not found in query!")
    
    # Recommendations
    print("\n6️⃣ RECOMMENDATIONS")
    print("-" * 30)
    
    if workflow_type != 'autonomous':
        print("1. Switch kebele to autonomous workflow")
    
    if not TenantGroup.objects.filter(group__name='print_id_cards', tenant__isnull=True).exists():
        print("2. Create global print_id_cards TenantGroup")
    
    print("3. Clear browser cache and refresh page")
    print("4. Check browser console for API errors")
    print("5. Restart Django server")

if __name__ == "__main__":
    debug_print_role_issue()
