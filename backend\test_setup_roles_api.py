#!/usr/bin/env python
"""
Test script to call the setup_predefined_roles API endpoint
"""
import os
import sys
import django
import requests

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context, get_public_schema_name

def test_setup_predefined_roles():
    """Test the setup_predefined_roles API endpoint"""
    print("🧪 Testing setup_predefined_roles API endpoint...")
    
    # Create a test client
    client = Client()
    
    # Test the setup endpoint
    response = client.post('/api/auth/group-management/setup_predefined_roles/')
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Success: {data}")
    else:
        print(f"❌ Error: {response.content}")
        
    return response.status_code == 200

def check_roles_after_setup():
    """Check what roles exist after setup"""
    print("\n🔍 Checking roles after setup...")
    
    from django.contrib.auth.models import Group
    from users.models_groups import TenantGroup
    
    with schema_context(get_public_schema_name()):
        predefined_roles = ['clerk', 'kebele_leader', 'subcity_admin', 'subcity_system_admin', 'city_admin', 'city_system_admin', 'print_id_cards']
        
        for role_name in predefined_roles:
            try:
                group = Group.objects.get(name=role_name)
                tenant_group = TenantGroup.objects.get(group=group)
                print(f"✅ {role_name}: Level {tenant_group.level}, Types: {tenant_group.allowed_tenant_types}")
            except Group.DoesNotExist:
                print(f"❌ {role_name}: Group not found")
            except TenantGroup.DoesNotExist:
                print(f"⚠️ {role_name}: Group exists but no TenantGroup")

def test_tenant_groups_api():
    """Test the tenant_groups API endpoint with different tenant types"""
    print("\n🧪 Testing tenant_groups API endpoint...")
    
    from tenants.models import Tenant
    
    # Create a test client
    client = Client()
    
    # Test with different tenant types
    with schema_context(get_public_schema_name()):
        # Find tenants of different types
        kebele_tenant = Tenant.objects.filter(type='kebele').first()
        subcity_tenant = Tenant.objects.filter(type='subcity').first()
        city_tenant = Tenant.objects.filter(type='city').first()
        
        for tenant, tenant_type in [(kebele_tenant, 'kebele'), (subcity_tenant, 'subcity'), (city_tenant, 'city')]:
            if tenant:
                print(f"\n--- Testing {tenant_type} tenant: {tenant.name} ---")
                response = client.get(f'/api/auth/group-management/tenant_groups/?tenant_id={tenant.id}')
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    groups = data.get('groups', [])
                    print(f"Groups returned: {len(groups)}")
                    for group in groups:
                        print(f"  - {group['name']} (Level: {group['level']})")
                else:
                    print(f"❌ Error: {response.content}")
            else:
                print(f"⚠️ No {tenant_type} tenant found")

if __name__ == '__main__':
    print("🚀 Testing predefined roles setup...")
    
    # Test the setup API
    success = test_setup_predefined_roles()
    
    if success:
        # Check the results
        check_roles_after_setup()
        
        # Test the tenant groups API
        test_tenant_groups_api()
    
    print("\n✅ Test completed!")
