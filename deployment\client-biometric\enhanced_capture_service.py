#!/usr/bin/env python3
"""
Enhanced GoID Client Biometric Service
Production-ready fingerprint capture service for client machines
"""

import json
import logging
import os
import sys
import time
import base64
import subprocess
from datetime import datetime
from pathlib import Path

from flask import Flask, jsonify, request, make_response
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import requests

# Configuration management
class BiometricConfig:
    def __init__(self, config_file='config.json'):
        self.config_file = config_file
        self.config = self.load_config()
        self.setup_logging()
    
    def load_config(self):
        """Load configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Configuration file {self.config_file} not found. Using defaults.")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"Invalid JSON in {self.config_file}: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """Return default configuration"""
        return {
            "server": {"url": "http://localhost:8000", "timeout": 30},
            "service": {"host": "0.0.0.0", "port": 8002, "log_level": "INFO"},
            "device": {"timeout": 30, "capture_timeout": 15, "max_retries": 3},
            "security": {"allowed_origins": ["*"], "rate_limit": {"requests_per_minute": 60}},
            "client_info": {"client_id": "unknown", "kebele_name": "Unknown Kebele"}
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config['service'].get('log_level', 'INFO'))
        log_file = self.config['service'].get('log_file', 'biometric_service.log')
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )

# Initialize configuration
config = BiometricConfig()
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app, origins=config.config['security']['allowed_origins'])

# Rate limiting
limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=[f"{config.config['security']['rate_limit']['requests_per_minute']} per minute"]
)

class EnhancedCaptureDevice:
    def __init__(self):
        self.config = config.config['device']
        self.working_dir = Path(self.config.get('working_directory', 'fingerPrint'))
        self.jar_path = Path(self.config.get('jar_path', 'fingerPrint/GonderFingerPrint.jar'))
        self.initialized = False
        self.device_info = {
            'model': self.config.get('model', 'Futronic FS88H'),
            'serial': 'FS88H-8002',
            'interface': 'java_bridge',
            'real_device': True,
            'client_id': config.config['client_info']['client_id']
        }
    
    def initialize(self):
        """Initialize capture device"""
        try:
            if not self.jar_path.exists():
                logger.error(f"JAR not found: {self.jar_path}")
                return False
            
            # Test Java availability
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            logger.info("Java runtime available")
            
            # Test device connection
            if self.test_device_connection():
                self.initialized = True
                logger.info("✅ Biometric device initialized successfully")
                return True
            else:
                logger.error("❌ Device connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"Device initialization failed: {e}")
            return False
    
    def test_device_connection(self):
        """Test if device is connected and responsive"""
        try:
            # Quick device test - attempt to get device status
            result = subprocess.run(
                ['java', '-jar', str(self.jar_path), 'test'],
                cwd=str(self.working_dir),
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def capture_fingerprint(self, thumb_type='right'):
        """Capture fingerprint with enhanced error handling"""
        if not self.initialized:
            if not self.initialize():
                return {'success': False, 'error': 'Device not initialized'}
        
        try:
            logger.info(f"🔍 Starting fingerprint capture for {thumb_type} thumb")
            
            # Execute capture command
            result = subprocess.run(
                ['java', '-jar', str(self.jar_path), thumb_type],
                cwd=str(self.working_dir),
                capture_output=True,
                text=True,
                timeout=self.config['capture_timeout']
            )
            
            if result.returncode == 0:
                return self.process_capture_result(thumb_type)
            else:
                logger.error(f"Capture failed: {result.stderr}")
                return {'success': False, 'error': f'Capture failed: {result.stderr}'}
                
        except subprocess.TimeoutExpired:
            logger.error("Capture timeout")
            return {'success': False, 'error': 'Capture timeout'}
        except Exception as e:
            logger.error(f"Capture error: {e}")
            return {'success': False, 'error': str(e)}
    
    def process_capture_result(self, thumb_type):
        """Process captured fingerprint data"""
        try:
            template_data_binary = None
            image_data_binary = None
            
            # Read template file
            iso_path = self.working_dir / '.iso'
            if iso_path.exists():
                with open(iso_path, 'rb') as f:
                    template_data_binary = base64.b64encode(f.read()).decode('utf-8')
            
            # Read image file
            jpg_path = self.working_dir / '.jpg'
            if jpg_path.exists():
                with open(jpg_path, 'rb') as f:
                    image_data_binary = base64.b64encode(f.read()).decode('utf-8')
            
            # Calculate quality score
            quality_score = self.calculate_quality_score(template_data_binary)
            
            # Create response data
            template_data = {
                'version': '2.0',
                'thumb_type': thumb_type,
                'capture_method': 'java_bridge',
                'capture_time': datetime.now().isoformat(),
                'ansi_iso_template': template_data_binary,
                'fingerprint_image': image_data_binary,
                'quality_metrics': {
                    'clarity': quality_score,
                    'completeness': min(100, quality_score + 10),
                    'uniqueness': min(100, quality_score + 5)
                },
                'device_info': self.device_info,
                'client_info': config.config['client_info']
            }
            
            return {
                'success': True,
                'data': {
                    'thumb_type': thumb_type,
                    'template_data': json.dumps(template_data),
                    'quality_score': quality_score,
                    'capture_time': template_data['capture_time'],
                    'device_info': self.device_info,
                    'ansi_iso_template': template_data_binary,
                    'has_template': bool(template_data_binary),
                    'has_image': bool(image_data_binary)
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing capture result: {e}")
            return {'success': False, 'error': f'Processing error: {str(e)}'}
    
    def calculate_quality_score(self, template_data):
        """Calculate fingerprint quality score"""
        if not template_data:
            return 40
        
        template_size = len(template_data)
        if template_size > 1000:
            return min(95, 60 + (template_size // 50))
        elif template_size > 500:
            return min(85, 50 + (template_size // 25))
        else:
            return max(40, template_size // 10)

# Global device instance
device = EnhancedCaptureDevice()

# API Routes
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device.initialized,
        'service_version': '2.0.0',
        'client_info': config.config['client_info']
    })

@app.route('/api/device/status', methods=['GET'])
def device_status():
    """Device status endpoint"""
    return jsonify({
        'connected': device.initialized,
        'device_info': device.device_info,
        'last_check': datetime.now().isoformat(),
        'capabilities': ['fingerprint_capture', 'template_generation', 'image_capture']
    })

@app.route('/api/capture/fingerprint', methods=['POST'])
@limiter.limit("10 per minute")
def capture_fingerprint():
    """Capture fingerprint endpoint"""
    try:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'right')
        
        if thumb_type not in ['left', 'right']:
            return jsonify({
                'success': False,
                'error': 'thumb_type must be "left" or "right"'
            }), 400
        
        result = device.capture_fingerprint(thumb_type)
        
        if result['success']:
            logger.info(f"✅ Successfully captured {thumb_type} thumb fingerprint")
            return jsonify(result)
        else:
            logger.error(f"❌ Failed to capture fingerprint: {result['error']}")
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Capture endpoint error: {e}")
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.route('/api/device/initialize', methods=['POST'])
def initialize_device():
    """Initialize device endpoint"""
    try:
        success = device.initialize()
        return jsonify({
            'success': success,
            'message': 'Device initialized successfully' if success else 'Device initialization failed',
            'device_info': device.device_info if success else None
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def main():
    """Main entry point"""
    logger.info("🚀 Starting Enhanced GoID Client Biometric Service")
    logger.info(f"📍 Client: {config.config['client_info']['kebele_name']}")
    logger.info(f"🔧 Server: {config.config['server']['url']}")
    logger.info(f"🌐 Service URL: http://localhost:{config.config['service']['port']}")

    # Initialize device on startup
    if device.initialize():
        logger.info("✅ Device initialization successful")
    else:
        logger.warning("⚠️  Device initialization failed - will retry on first capture")

    try:
        # Start Flask server
        app.run(
            host=config.config['service']['host'],
            port=config.config['service']['port'],
            debug=config.config['service'].get('debug', False)
        )
    except KeyboardInterrupt:
        logger.info("🛑 Service stopped by user")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
