#!/usr/bin/env python3
"""
Demonstration script showing how print_id_cards role availability changes with workflow type.

This script demonstrates:
1. Centralized workflow: print_id_cards role NOT available for kebele
2. Autonomous workflow: print_id_cards role IS available for kebele
3. Subcity tenants: print_id_cards role ALWAYS available
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from users.views_groups import GroupManagementViewSet
from django.test import RequestFactory

User = get_user_model()

def demonstrate_role_filtering():
    """Demonstrate how print_id_cards role filtering works."""
    
    print("🎭 Print ID Cards Role Filtering Demonstration")
    print("="*60)
    
    # Get or create test kebele
    kebele, created = Tenant.objects.get_or_create(
        name="De<PERSON> Kebele",
        defaults={
            'type': 'kebele',
            'schema_name': 'demo_kebele'
        }
    )
    
    if created:
        print(f"📋 Created demo kebele: {kebele.name}")
    else:
        print(f"📋 Using existing kebele: {kebele.name}")
    
    # Create mock request and user
    factory = RequestFactory()
    user = User.objects.filter(is_superuser=True).first()
    if not user:
        user = User.objects.create_superuser(
            username='demo_admin',
            email='<EMAIL>',
            password='demopass123'
        )
    
    def get_available_roles(tenant_id, workflow_type):
        """Get available roles for a tenant with specific workflow."""
        # Set workflow configuration
        workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
            tenant_id=tenant_id,
            defaults={'workflow_type': workflow_type}
        )
        workflow_config.workflow_type = workflow_type
        workflow_config.save()
        
        # Create mock request
        request = factory.get(f'/api/auth/group-management/tenant_groups/?tenant_id={tenant_id}')
        request.user = user
        
        # Get roles using the API logic
        viewset = GroupManagementViewSet()
        viewset.request = request
        
        try:
            response = viewset.tenant_groups(request)
            if response.status_code == 200:
                groups = response.data.get('groups', [])
                return [group['name'] for group in groups]
            else:
                return []
        except Exception as e:
            print(f"Error getting roles: {e}")
            return []
    
    # Demonstrate centralized workflow
    print("\n🏢 CENTRALIZED WORKFLOW")
    print("-" * 30)
    centralized_roles = get_available_roles(kebele.id, 'centralized')
    print(f"Available roles: {centralized_roles}")
    
    if 'print_id_cards' in centralized_roles:
        print("❌ ERROR: print_id_cards should NOT be available for centralized workflow")
    else:
        print("✅ CORRECT: print_id_cards role is not available (printing managed by subcity)")
    
    print("\nExplanation:")
    print("• Centralized workflow means ID cards are sent to subcity for approval and printing")
    print("• Kebele users cannot print ID cards locally")
    print("• Only clerk and kebele_leader roles are available")
    
    # Demonstrate autonomous workflow
    print("\n🏠 AUTONOMOUS WORKFLOW")
    print("-" * 30)
    autonomous_roles = get_available_roles(kebele.id, 'autonomous')
    print(f"Available roles: {autonomous_roles}")
    
    if 'print_id_cards' in autonomous_roles:
        print("✅ CORRECT: print_id_cards role is available for local printing")
    else:
        print("❌ ERROR: print_id_cards should be available for autonomous workflow")
    
    print("\nExplanation:")
    print("• Autonomous workflow means kebele handles full ID card process locally")
    print("• Kebele users can print ID cards after kebele leader approval")
    print("• clerk, kebele_leader, and print_id_cards roles are available")
    
    # Demonstrate subcity roles
    subcity = Tenant.objects.filter(type='subcity').first()
    if subcity:
        print("\n🏛️ SUBCITY TENANT")
        print("-" * 30)
        subcity_roles = get_available_roles(subcity.id, 'centralized')  # Workflow type doesn't matter for subcity
        print(f"Available roles: {subcity_roles}")
        
        if 'print_id_cards' in subcity_roles:
            print("✅ CORRECT: print_id_cards role is always available for subcity")
        else:
            print("❌ ERROR: print_id_cards should always be available for subcity")
        
        print("\nExplanation:")
        print("• Subcity tenants manage centralized printing for child kebeles")
        print("• print_id_cards role is always available regardless of workflow")
        print("• subcity_admin, subcity_system_admin, and print_id_cards roles are available")
    
    # Summary
    print("\n📊 SUMMARY")
    print("="*60)
    print("Role Availability Matrix:")
    print()
    print("| Tenant Type | Workflow    | print_id_cards Available |")
    print("|-------------|-------------|---------------------------|")
    print("| Kebele      | Centralized | ❌ NO                     |")
    print("| Kebele      | Autonomous  | ✅ YES                    |")
    print("| Subcity     | Any         | ✅ YES                    |")
    print("| City        | Any         | ❌ NO                     |")
    print()
    
    print("🎯 Key Points:")
    print("1. Kebele print_id_cards role depends on workflow type")
    print("2. Autonomous kebeles can print locally")
    print("3. Centralized kebeles send to subcity for printing")
    print("4. Subcity always has printing capability")
    print("5. City level doesn't handle direct printing")
    
    print("\n🔧 Frontend Impact:")
    print("• User creation dropdown will show/hide print_id_cards based on workflow")
    print("• Workflow switching will update available roles immediately")
    print("• Clear indicators show when print_id_cards is available")

if __name__ == "__main__":
    demonstrate_role_filtering()
