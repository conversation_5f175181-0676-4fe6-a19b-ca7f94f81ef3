#!/usr/bin/env python3
"""
Fix print_id_cards users group assignment.
This script fixes users with role 'print_id_cards' who are incorrectly assigned to 'clerk' group.
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group
from django.db import transaction
from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant
from users.models import User

def fix_print_id_cards_group_assignment():
    """Fix group assignment for print_id_cards users."""
    print("🔧 Fixing print_id_cards users group assignment...")
    
    try:
        with schema_context(get_public_schema_name()):
            # Get the print_id_cards group
            try:
                print_group = Group.objects.get(name='print_id_cards')
                print(f"✅ Found print_id_cards group (ID: {print_group.id})")
            except Group.DoesNotExist:
                print("❌ print_id_cards group does not exist!")
                return False
            
            # Get the clerk group
            try:
                clerk_group = Group.objects.get(name='clerk')
                print(f"✅ Found clerk group (ID: {clerk_group.id})")
            except Group.DoesNotExist:
                print("❌ clerk group does not exist!")
                return False
        
        # Find all users with print_id_cards role
        all_print_users = []
        
        with schema_context(get_public_schema_name()):
            tenants = Tenant.objects.all()
            
            for tenant in tenants:
                with schema_context(tenant.schema_name):
                    users = User.objects.filter(role='print_id_cards')
                    for user in users:
                        all_print_users.append((user, tenant))
        
        print(f"\n📊 Found {len(all_print_users)} users with print_id_cards role")
        
        if not all_print_users:
            print("ℹ️  No users with print_id_cards role found")
            return True
        
        # Fix each user's group assignment
        fixed_count = 0
        
        with schema_context(get_public_schema_name()):
            for user, tenant in all_print_users:
                try:
                    print(f"\n🔍 Processing user: {user.email} in {tenant.name}")
                    print(f"   Role: {user.role}")
                    print(f"   Tenant type: {user.tenant_type}")
                    
                    # Check current groups
                    current_groups = list(user.groups.values_list('name', flat=True))
                    print(f"   Current groups: {current_groups}")
                    
                    # Remove from clerk group if present
                    if 'clerk' in current_groups:
                        user.groups.remove(clerk_group)
                        print(f"   ❌ Removed from: clerk group")
                    
                    # Add to print_id_cards group if not present
                    if 'print_id_cards' not in current_groups:
                        user.groups.add(print_group)
                        print(f"   ✅ Added to: print_id_cards group")
                        fixed_count += 1
                    else:
                        print(f"   ℹ️  Already in print_id_cards group")
                    
                    # Verify final groups
                    final_groups = list(user.groups.values_list('name', flat=True))
                    print(f"   Final groups: {final_groups}")
                    
                except Exception as e:
                    print(f"   ❌ Error fixing {user.email}: {e}")
        
        print(f"\n🎉 Fixed {fixed_count} users' group assignments")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Starting print_id_cards group assignment fix...")
    success = fix_print_id_cards_group_assignment()
    
    if success:
        print("\n✅ Group assignment fix completed successfully!")
        print("ℹ️  Users with print_id_cards role should now be in print_id_cards group")
        print("ℹ️  Please restart the application for changes to take effect")
    else:
        print("\n❌ Group assignment fix failed!")
        sys.exit(1)
