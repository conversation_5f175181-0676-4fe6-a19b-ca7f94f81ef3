<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inject Script Instructions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .steps {
            margin-top: 20px;
        }
        .step {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 3px solid #007bff;
        }
        .note {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>How to Inject the Custom Delete Dialog Script</h1>
    
    <div class="note">
        <strong>Note:</strong> This is a temporary solution to override the browser's confirm dialog with a custom Material-UI style dialog until the React application can be properly rebuilt.
    </div>
    
    <div class="steps">
        <div class="step">
            <h3>Step 1: Open the KebeleUserManagement page</h3>
            <p>Navigate to <a href="http://localhost:3000/users/kebele-management" target="_blank">http://localhost:3000/users/kebele-management</a></p>
        </div>
        
        <div class="step">
            <h3>Step 2: Open the browser's Developer Tools</h3>
            <p>Press <strong>F12</strong> or right-click and select "Inspect" to open the developer tools.</p>
        </div>
        
        <div class="step">
            <h3>Step 3: Go to the Console tab</h3>
            <p>Click on the "Console" tab in the developer tools.</p>
        </div>
        
        <div class="step">
            <h3>Step 4: Paste and execute the script</h3>
            <p>Copy the following code and paste it into the console, then press Enter:</p>
            <pre>
// Override the window.confirm function
const originalConfirm = window.confirm;
window.confirm = function(message) {
    console.log('🔍 Confirm dialog intercepted with message:', message);
    
    // Check if this is a delete user confirmation
    if (message.includes('delete the user')) {
        console.log('🔍 This is a delete user confirmation, showing custom dialog instead');
        
        // Extract user info from the message
        const userMatch = message.match(/"([^"]+)"/);
        const userName = userMatch ? userMatch[1] : 'Unknown User';
        const emailMatch = message.match(/\(([^)]+)\)/);
        const userEmail = emailMatch ? emailMatch[1] : '<EMAIL>';
        
        // Create custom dialog
        const dialogOverlay = document.createElement('div');
        dialogOverlay.style.position = 'fixed';
        dialogOverlay.style.top = '0';
        dialogOverlay.style.left = '0';
        dialogOverlay.style.right = '0';
        dialogOverlay.style.bottom = '0';
        dialogOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        dialogOverlay.style.display = 'flex';
        dialogOverlay.style.alignItems = 'center';
        dialogOverlay.style.justifyContent = 'center';
        dialogOverlay.style.zIndex = '10000';
        
        const dialog = document.createElement('div');
        dialog.style.backgroundColor = 'white';
        dialog.style.borderRadius = '8px';
        dialog.style.maxWidth = '500px';
        dialog.style.width = '100%';
        dialog.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
        dialog.style.overflow = 'hidden';
        dialog.style.borderTop = '4px solid #f44336';
        
        dialog.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px; padding: 16px 24px; color: #f44336; border-bottom: 1px solid #eee;">
                <span class="material-icons">delete</span>
                <span style="font-weight: bold; font-size: 18px;">Confirm User Deletion</span>
            </div>
            <div style="padding: 16px 24px;">
                <p>Are you sure you want to delete the following user?</p>
                
                <div style="background-color: #f5f5f5; border-radius: 8px; padding: 16px; margin-top: 16px;">
                    <div style="font-weight: bold; margin-bottom: 8px;">${userName}</div>
                    <div style="color: #666; margin-bottom: 4px;">Email: ${userEmail}</div>
                </div>
                
                <p style="color: #f44336; margin-top: 16px; font-weight: 500;">This action cannot be undone. The user will be permanently removed from the system.</p>
            </div>
            <div style="display: flex; justify-content: flex-end; padding: 16px 24px; gap: 8px;">
                <button id="cancelDelete" style="padding: 8px 16px; border-radius: 4px; font-weight: 500; cursor: pointer; background-color: white; border: 1px solid #ccc;">Cancel</button>
                <button id="confirmDelete" style="padding: 8px 16px; border-radius: 4px; font-weight: 500; cursor: pointer; background-color: #f44336; color: white; border: none;">
                    <span class="material-icons" style="font-size: 16px; margin-right: 4px;">delete</span>
                    Delete User
                </button>
            </div>
        `;
        
        dialogOverlay.appendChild(dialog);
        document.body.appendChild(dialogOverlay);
        
        // Add Material Icons
        if (!document.querySelector('link[href*="Material+Icons"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/icon?family=Material+Icons';
            document.head.appendChild(link);
        }
        
        return new Promise(resolve => {
            document.getElementById('cancelDelete').addEventListener('click', function() {
                dialogOverlay.remove();
                resolve(false);
            });
            
            document.getElementById('confirmDelete').addEventListener('click', function() {
                dialogOverlay.remove();
                resolve(true);
            });
        });
    }
    
    // For other confirm dialogs, use the original function
    return originalConfirm(message);
};

console.log('🔍 Window.confirm has been overridden');
            </pre>
        </div>
        
        <div class="step">
            <h3>Step 5: Test the delete functionality</h3>
            <p>Click on the three dots menu for a user and select "Delete User". You should now see the custom Material-UI style dialog instead of the browser's default confirm dialog.</p>
        </div>
    </div>
    
    <div class="note">
        <strong>Important:</strong> This script will need to be re-applied each time you refresh the page. For a permanent solution, the React application should be properly rebuilt with the changes we made to KebeleUserManagement.jsx.
    </div>
</body>
</html>
