// Override the window.confirm function
const originalConfirm = window.confirm;
window.confirm = function(message) {
    console.log('🔍 Confirm dialog intercepted with message:', message);
    
    // Check if this is a delete user confirmation
    if (message.includes('delete the user')) {
        console.log('🔍 This is a delete user confirmation, showing custom dialog instead');
        
        // Extract user info from the message
        const userMatch = message.match(/"([^"]+)"/);
        const userName = userMatch ? userMatch[1] : 'Unknown User';
        const emailMatch = message.match(/\(([^)]+)\)/);
        const userEmail = emailMatch ? emailMatch[1] : '<EMAIL>';
        
        // Create custom dialog
        const dialogOverlay = document.createElement('div');
        dialogOverlay.style.position = 'fixed';
        dialogOverlay.style.top = '0';
        dialogOverlay.style.left = '0';
        dialogOverlay.style.right = '0';
        dialogOverlay.style.bottom = '0';
        dialogOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        dialogOverlay.style.display = 'flex';
        dialogOverlay.style.alignItems = 'center';
        dialogOverlay.style.justifyContent = 'center';
        dialogOverlay.style.zIndex = '10000';
        
        const dialog = document.createElement('div');
        dialog.style.backgroundColor = 'white';
        dialog.style.borderRadius = '8px';
        dialog.style.maxWidth = '500px';
        dialog.style.width = '100%';
        dialog.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
        dialog.style.overflow = 'hidden';
        dialog.style.borderTop = '4px solid #f44336';
        
        dialog.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px; padding: 16px 24px; color: #f44336; border-bottom: 1px solid #eee;">
                <span class="material-icons">delete</span>
                <span style="font-weight: bold; font-size: 18px;">Confirm User Deletion</span>
            </div>
            <div style="padding: 16px 24px;">
                <p>Are you sure you want to delete the following user?</p>
                
                <div style="background-color: #f5f5f5; border-radius: 8px; padding: 16px; margin-top: 16px;">
                    <div style="font-weight: bold; margin-bottom: 8px;">${userName}</div>
                    <div style="color: #666; margin-bottom: 4px;">Email: ${userEmail}</div>
                </div>
                
                <p style="color: #f44336; margin-top: 16px; font-weight: 500;">This action cannot be undone. The user will be permanently removed from the system.</p>
            </div>
            <div style="display: flex; justify-content: flex-end; padding: 16px 24px; gap: 8px;">
                <button id="cancelDelete" style="padding: 8px 16px; border-radius: 4px; font-weight: 500; cursor: pointer; background-color: white; border: 1px solid #ccc;">Cancel</button>
                <button id="confirmDelete" style="padding: 8px 16px; border-radius: 4px; font-weight: 500; cursor: pointer; background-color: #f44336; color: white; border: none;">
                    <span class="material-icons" style="font-size: 16px; margin-right: 4px;">delete</span>
                    Delete User
                </button>
            </div>
        `;
        
        dialogOverlay.appendChild(dialog);
        document.body.appendChild(dialogOverlay);
        
        // Add Material Icons
        if (!document.querySelector('link[href*="Material+Icons"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/icon?family=Material+Icons';
            document.head.appendChild(link);
        }
        
        return new Promise(resolve => {
            document.getElementById('cancelDelete').addEventListener('click', function() {
                dialogOverlay.remove();
                resolve(false);
            });
            
            document.getElementById('confirmDelete').addEventListener('click', function() {
                dialogOverlay.remove();
                resolve(true);
            });
        });
    }
    
    // For other confirm dialogs, use the original function
    return originalConfirm(message);
};

console.log('🔍 Window.confirm has been overridden');
