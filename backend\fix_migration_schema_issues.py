#!/usr/bin/env python3
"""
Fix migration schema issues after tenant deletion.
This script cleans up orphaned schemas and fixes migration state.
"""

import os
import sys
import django
from django.db import connection

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

def fix_migration_issues():
    """Fix migration schema issues."""
    print("🔧 Fixing migration schema issues...")
    
    try:
        from tenants.models.tenant import Tenant
        
        # Get all existing tenants
        existing_tenants = Tenant.objects.all()
        existing_schemas = [tenant.schema_name for tenant in existing_tenants]
        existing_schemas.append('public')  # Always keep public schema
        
        print(f"📋 Found {len(existing_tenants)} existing tenants:")
        for tenant in existing_tenants:
            print(f"   - {tenant.name} (schema: {tenant.schema_name})")
        
        # Get all schemas in the database
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
                ORDER BY schema_name;
            """)
            
            all_schemas = [row[0] for row in cursor.fetchall()]
            print(f"\n📋 Found {len(all_schemas)} schemas in database:")
            for schema in all_schemas:
                print(f"   - {schema}")
        
        # Find orphaned schemas
        orphaned_schemas = []
        for schema in all_schemas:
            if schema not in existing_schemas and schema != 'public':
                orphaned_schemas.append(schema)
        
        if orphaned_schemas:
            print(f"\n🗑️ Found {len(orphaned_schemas)} orphaned schemas:")
            for schema in orphaned_schemas:
                print(f"   - {schema}")
            
            # Ask for confirmation
            response = input("\nDo you want to delete these orphaned schemas? (y/N): ")
            if response.lower() == 'y':
                with connection.cursor() as cursor:
                    for schema in orphaned_schemas:
                        try:
                            print(f"   🗑️ Dropping schema: {schema}")
                            cursor.execute(f'DROP SCHEMA IF EXISTS "{schema}" CASCADE;')
                            print(f"   ✅ Dropped schema: {schema}")
                        except Exception as e:
                            print(f"   ❌ Error dropping schema {schema}: {e}")
            else:
                print("   ℹ️ Skipped orphaned schema cleanup")
        else:
            print("\n✅ No orphaned schemas found")
        
        # Fix migration table issues
        print(f"\n🔧 Checking migration tables in existing schemas...")
        
        with connection.cursor() as cursor:
            for tenant in existing_tenants:
                schema = tenant.schema_name
                try:
                    print(f"   🔍 Checking schema: {schema}")
                    
                    # Set search path to the tenant schema
                    cursor.execute(f'SET search_path TO "{schema}";')
                    
                    # Check if django_migrations table exists
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = %s 
                            AND table_name = 'django_migrations'
                        );
                    """, [schema])
                    
                    migrations_table_exists = cursor.fetchone()[0]
                    
                    if migrations_table_exists:
                        # Check if table is corrupted or has issues
                        try:
                            cursor.execute("SELECT COUNT(*) FROM django_migrations;")
                            count = cursor.fetchone()[0]
                            print(f"     ✅ Migration table OK: {count} migrations")
                        except Exception as e:
                            print(f"     ❌ Migration table corrupted: {e}")
                            print(f"     🔧 Recreating migration table...")
                            cursor.execute("DROP TABLE IF EXISTS django_migrations;")
                            print(f"     ✅ Dropped corrupted migration table")
                    else:
                        print(f"     ℹ️ No migration table found (will be created)")
                        
                except Exception as e:
                    print(f"     ❌ Error checking schema {schema}: {e}")
            
            # Reset search path
            cursor.execute('SET search_path TO public;')
        
        print(f"\n🎉 Migration schema cleanup completed!")
        print(f"ℹ️ You can now run migrations safely:")
        print(f"   docker-compose -f docker-compose.production.yml exec backend python manage.py migrate_schemas")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_migration_issues()
