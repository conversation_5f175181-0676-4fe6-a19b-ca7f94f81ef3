#!/usr/bin/env python
"""
Complete fix for predefined roles system - Start from scratch
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django_tenants.utils import schema_context, get_public_schema_name
from users.models_groups import TenantGroup, GroupMembership
from users.models import User

def create_all_permissions():
    """Create all required permissions"""
    print("🏗️ Creating all required permissions...")
    
    with schema_context(get_public_schema_name()):
        content_type, _ = ContentType.objects.get_or_create(
            app_label='users',
            model='user'
        )
        
        # Define all permissions needed
        all_permissions = [
            # Basic navigation and dashboard
            ('view_kebele_dashboard', 'Can view kebele dashboard'),
            ('view_subcity_dashboard', 'Can view subcity dashboard'),
            ('view_city_dashboard', 'Can view city dashboard'),
            
            # Citizens permissions
            ('register_citizens', 'Can register citizens'),
            ('view_citizens_list', 'Can view citizens list'),
            ('view_citizen_details', 'Can view citizen details'),
            ('view_own_kebele_data', 'Can view own kebele data'),
            ('view_child_kebeles_data', 'Can view child kebeles data'),
            ('view_child_subcities_data', 'Can view child subcities data'),
            
            # ID Cards permissions
            ('generate_id_cards', 'Can generate ID cards'),
            ('view_id_cards_list', 'Can view ID cards list'),
            ('approve_id_cards', 'Can approve ID cards'),
            ('print_id_cards', 'Can print ID cards'),
            ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
            ('verify_documents', 'Can verify documents'),
            
            # User management permissions
            ('create_kebele_users', 'Can create kebele users'),
            ('create_subcity_users', 'Can create subcity users'),
            
            # Reports permissions
            ('view_kebele_reports', 'Can view kebele reports'),
            ('view_subcity_reports', 'Can view subcity reports'),
            ('view_city_reports', 'Can view city reports'),
            ('view_all_reports', 'Can view all reports'),
            
            # Transfer and Clearance permissions
            ('create_transfers', 'Can create transfer requests'),
            ('approve_transfer_requests', 'Can approve transfer requests'),
            ('create_clearances', 'Can create clearance requests'),
            ('view_clearances', 'Can view clearances'),
            
            # System permissions
            ('manage_tenants', 'Can manage tenants'),
        ]
        
        created_count = 0
        for codename, name in all_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=content_type,
                defaults={'name': name}
            )
            if created:
                print(f"  ✅ Created: {codename}")
                created_count += 1
            else:
                print(f"  ℹ️ Exists: {codename}")
        
        print(f"📊 Created {created_count} new permissions")
        return True

def create_predefined_roles():
    """Create predefined role groups with exact permissions"""
    print("\n🏗️ Creating predefined role groups...")
    
    # Define roles with exact permissions as specified
    roles_config = {
        'clerk': {
            'description': 'Clerk role for kebele level tenants',
            'tenant_types': ['kebele'],
            'level': 10,
            'group_type': 'operational',
            'permissions': [
                'register_citizens',
                'view_citizens_list', 
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list',
                'view_kebele_dashboard'
            ]
        },
        'kebele_leader': {
            'description': 'Kebele leader role for kebele level tenants',
            'tenant_types': ['kebele'],
            'level': 20,
            'group_type': 'administrative',
            'permissions': [
                'view_own_kebele_data',
                'view_kebele_dashboard',
                'view_kebele_reports',
                'view_citizens_list',
                'view_citizen_details',
                'view_id_cards_list',
                'approve_id_cards',
                'verify_documents',
                'create_transfers',
                'approve_transfer_requests',
                'create_clearances',
                'view_clearances'
            ]
        },
        'subcity_admin': {
            'description': 'Subcity admin role for subcity level tenants',
            'tenant_types': ['subcity'],
            'level': 30,
            'group_type': 'administrative',
            'permissions': [
                'view_child_kebeles_data',
                'view_subcity_dashboard',
                'view_subcity_reports',
                'approve_id_cards',
                'print_id_cards',
                'send_id_cards_to_higher_level',
                'verify_documents',
                'create_kebele_users'
            ]
        },
        'subcity_system_admin': {
            'description': 'Subcity system admin for managing kebele users',
            'tenant_types': ['subcity'],
            'level': 35,
            'group_type': 'administrative',
            'permissions': [
                'create_kebele_users'
            ]
        },
        'city_admin': {
            'description': 'City admin role for city level tenants',
            'tenant_types': ['city'],
            'level': 40,
            'group_type': 'administrative',
            'permissions': [
                'view_child_subcities_data',
                'view_city_dashboard',
                'view_city_reports',
                'manage_tenants',
                'view_all_reports',
                'create_subcity_users'
            ]
        },
        'city_system_admin': {
            'description': 'City system admin for managing subcity users',
            'tenant_types': ['city'],
            'level': 45,
            'group_type': 'administrative',
            'permissions': [
                'create_subcity_users'
            ]
        },
        'print_id_cards': {
            'description': 'ID card printing role for autonomous kebeles or centralized subcities',
            'tenant_types': ['kebele', 'subcity'],
            'level': 15,
            'group_type': 'operational',
            'permissions': [
                'print_id_cards'
            ]
        }
    }
    
    with schema_context(get_public_schema_name()):
        created_count = 0
        updated_count = 0
        
        for role_name, config in roles_config.items():
            print(f"\n🔧 Processing role: {role_name}")
            
            # Create Django group
            group, created = Group.objects.get_or_create(name=role_name)
            if created:
                created_count += 1
                print(f"  ✅ Created group: {role_name}")
            else:
                print(f"  ℹ️ Group exists: {role_name}")
            
            # Clear existing permissions and add new ones
            group.permissions.clear()
            permissions_added = 0
            
            for perm_codename in config['permissions']:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    group.permissions.add(permission)
                    permissions_added += 1
                    print(f"    ✅ Added permission: {perm_codename}")
                except Permission.DoesNotExist:
                    print(f"    ❌ Permission not found: {perm_codename}")
            
            print(f"  📊 Added {permissions_added} permissions to {role_name}")
            
            # Create or update TenantGroup
            tenant_group, tg_created = TenantGroup.objects.get_or_create(
                group=group,
                defaults={
                    'name': role_name,
                    'description': config['description'],
                    'group_type': config['group_type'],
                    'level': config['level'],
                    'allowed_tenant_types': config['tenant_types'],
                    'is_active': True,
                    'tenant': None
                }
            )
            
            if not tg_created:
                # Update existing TenantGroup
                tenant_group.allowed_tenant_types = config['tenant_types']
                tenant_group.level = config['level']
                tenant_group.description = config['description']
                tenant_group.group_type = config['group_type']
                tenant_group.save()
                updated_count += 1
                print(f"  🔄 Updated TenantGroup: {role_name}")
        
        print(f"\n📊 Summary: {created_count} groups created, {updated_count} groups updated")
        return True

def reassign_existing_users():
    """Reassign existing users to their correct role groups"""
    print("\n👥 Reassigning existing users to role groups...")
    
    # Get all users with roles
    all_users = []
    from tenants.models import Tenant
    
    with schema_context(get_public_schema_name()):
        tenants = Tenant.objects.all()
        
        for tenant in tenants:
            with schema_context(tenant.schema_name):
                users = User.objects.filter(role__isnull=False).select_related('tenant')
                for user in users:
                    all_users.append((user, tenant))
    
    print(f"  Found {len(all_users)} users with roles")
    
    role_mapping = {
        'clerk': 'clerk',
        'kebele_leader': 'kebele_leader',
        'subcity_admin': 'subcity_admin',
        'subcity_system_admin': 'subcity_system_admin',
        'city_admin': 'city_admin',
        'city_system_admin': 'city_system_admin'
    }
    
    assigned_count = 0
    with schema_context(get_public_schema_name()):
        for user, user_tenant in all_users:
            group_name = role_mapping.get(user.role)
            if not group_name:
                print(f"    ⚠️ No mapping for role: {user.role} (user: {user.email})")
                continue
            
            try:
                group = Group.objects.get(name=group_name)
                tenant_group = TenantGroup.objects.get(group=group)
                
                # Check tenant type compatibility
                if user_tenant.type not in tenant_group.allowed_tenant_types:
                    print(f"    ⚠️ User {user.email} role {user.role} not compatible with {user_tenant.type} tenant")
                    continue
                
                # Clear existing group memberships and add to correct group
                user.groups.clear()
                group.user_set.add(user)
                
                # Update GroupMembership
                GroupMembership.objects.filter(user_email=user.email).delete()
                GroupMembership.objects.create(
                    user_email=user.email,
                    user_tenant_id=user_tenant.id,
                    user_tenant_schema=user_tenant.schema_name,
                    group=tenant_group,
                    assigned_by_email='system',
                    reason=f'Automatic reassignment for role: {user.role}',
                    is_primary=True,
                    is_active=True
                )
                
                assigned_count += 1
                print(f"    ✅ Assigned {user.email} ({user.role}) to {group_name}")
                
            except Exception as e:
                print(f"    ❌ Error assigning {user.email}: {e}")
    
    print(f"📊 Reassigned {assigned_count} users")
    return True

if __name__ == '__main__':
    print("🚀 Starting complete predefined roles fix...")
    
    try:
        # Step 1: Create all permissions
        if create_all_permissions():
            print("✅ Permissions created successfully")
        
        # Step 2: Create predefined roles
        if create_predefined_roles():
            print("✅ Predefined roles created successfully")
        
        # Step 3: Reassign existing users
        if reassign_existing_users():
            print("✅ Users reassigned successfully")
        
        print("\n🎉 Complete predefined roles fix completed successfully!")
        print("\n📋 Next steps:")
        print("1. Refresh the browser")
        print("2. Check /debug/role to verify permissions")
        print("3. Test navigation menus")
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()
