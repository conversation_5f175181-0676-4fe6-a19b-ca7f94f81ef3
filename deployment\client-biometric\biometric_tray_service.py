#!/usr/bin/env python3
"""
GoID Client Biometric Service - Windows Desktop Application with System Tray
Production-ready fingerprint capture service that runs as a Windows desktop application
"""

import json
import logging
import os
import sys
import time
import threading
import webbrowser
from datetime import datetime
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, simpledialog
import subprocess

# Third-party imports
import pystray
from PIL import Image, ImageDraw
from plyer import notification
import requests

# Import our enhanced capture service
from enhanced_capture_service import EnhancedCaptureDevice, BiometricConfig, app

class BiometricTrayService:
    def __init__(self):
        self.config = BiometricConfig()
        self.device = EnhancedCaptureDevice()
        self.flask_thread = None
        self.running = False
        self.icon = None
        self.logger = logging.getLogger(__name__)
        
        # Service status
        self.service_status = "stopped"
        self.device_status = "disconnected"
        self.last_capture_time = None
        
    def create_icon_image(self, color="green"):
        """Create system tray icon"""
        # Create a simple icon image
        width = 64
        height = 64
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # Draw fingerprint-like pattern
        if color == "green":
            fill_color = (0, 128, 0)  # Green for active
        elif color == "red":
            fill_color = (255, 0, 0)  # Red for error
        elif color == "yellow":
            fill_color = (255, 165, 0)  # Orange for warning
        else:
            fill_color = (128, 128, 128)  # Gray for inactive
            
        # Draw concentric circles to represent fingerprint
        center_x, center_y = width // 2, height // 2
        for i in range(5):
            radius = 8 + i * 6
            draw.ellipse([center_x - radius, center_y - radius, 
                         center_x + radius, center_y + radius], 
                        outline=fill_color, width=2)
        
        # Add center dot
        draw.ellipse([center_x - 3, center_y - 3, center_x + 3, center_y + 3], 
                    fill=fill_color)
        
        return image
    
    def show_notification(self, title, message, timeout=5):
        """Show Windows notification"""
        try:
            notification.notify(
                title=title,
                message=message,
                app_name="GoID Biometric Service",
                timeout=timeout
            )
        except Exception as e:
            self.logger.warning(f"Failed to show notification: {e}")
    
    def start_flask_service(self):
        """Start Flask service in background thread"""
        try:
            self.logger.info("Starting Flask service...")
            app.run(
                host=self.config.config['service']['host'],
                port=self.config.config['service']['port'],
                debug=False,
                use_reloader=False,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Flask service error: {e}")
            self.service_status = "error"
            self.update_icon()
    
    def start_service(self):
        """Start the biometric service"""
        if self.running:
            return
            
        try:
            # Initialize device
            if self.device.initialize():
                self.device_status = "connected"
                self.logger.info("✅ Device initialized successfully")
            else:
                self.device_status = "error"
                self.logger.warning("⚠️ Device initialization failed")
            
            # Start Flask service in background thread
            self.flask_thread = threading.Thread(target=self.start_flask_service, daemon=True)
            self.flask_thread.start()
            
            self.running = True
            self.service_status = "running"
            self.update_icon()
            
            self.show_notification(
                "GoID Biometric Service", 
                "Service started successfully\nListening on port 8002"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to start service: {e}")
            self.service_status = "error"
            self.update_icon()
            self.show_notification(
                "GoID Biometric Service - Error", 
                f"Failed to start service: {str(e)}"
            )
    
    def stop_service(self):
        """Stop the biometric service"""
        self.running = False
        self.service_status = "stopped"
        self.device_status = "disconnected"
        self.update_icon()
        
        self.show_notification(
            "GoID Biometric Service", 
            "Service stopped"
        )
    
    def restart_service(self):
        """Restart the biometric service"""
        self.stop_service()
        time.sleep(2)
        self.start_service()
    
    def update_icon(self):
        """Update system tray icon based on status"""
        if not self.icon:
            return
            
        if self.service_status == "running" and self.device_status == "connected":
            color = "green"
            tooltip = "GoID Biometric Service - Running"
        elif self.service_status == "running" and self.device_status == "error":
            color = "yellow"
            tooltip = "GoID Biometric Service - Device Error"
        elif self.service_status == "error":
            color = "red"
            tooltip = "GoID Biometric Service - Service Error"
        else:
            color = "gray"
            tooltip = "GoID Biometric Service - Stopped"
        
        self.icon.icon = self.create_icon_image(color)
        self.icon.title = tooltip
    
    def show_status_window(self):
        """Show status information window"""
        try:
            root = tk.Tk()
            root.title("GoID Biometric Service Status")
            root.geometry("400x300")
            root.resizable(False, False)
            
            # Status information
            status_text = f"""
GoID Biometric Service Status

Service Status: {self.service_status.title()}
Device Status: {self.device_status.title()}
Port: {self.config.config['service']['port']}
Client: {self.config.config['client_info']['kebele_name']}
Server: {self.config.config['server']['url']}

Last Capture: {self.last_capture_time or 'Never'}

Service URL: http://localhost:{self.config.config['service']['port']}
Health Check: http://localhost:{self.config.config['service']['port']}/api/health
            """
            
            text_widget = tk.Text(root, wrap=tk.WORD, padx=10, pady=10)
            text_widget.insert(tk.END, status_text.strip())
            text_widget.config(state=tk.DISABLED)
            text_widget.pack(fill=tk.BOTH, expand=True)
            
            # Buttons
            button_frame = tk.Frame(root)
            button_frame.pack(fill=tk.X, padx=10, pady=5)
            
            tk.Button(button_frame, text="Test Device", 
                     command=self.test_device).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Open Web Interface", 
                     command=self.open_web_interface).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Close", 
                     command=root.destroy).pack(side=tk.RIGHT, padx=5)
            
            root.mainloop()
            
        except Exception as e:
            self.logger.error(f"Failed to show status window: {e}")
    
    def test_device(self):
        """Test device connection"""
        try:
            if self.device.test_device_connection():
                messagebox.showinfo("Device Test", "✅ Device is connected and working properly")
            else:
                messagebox.showerror("Device Test", "❌ Device test failed\nCheck USB connection and drivers")
        except Exception as e:
            messagebox.showerror("Device Test", f"❌ Test error: {str(e)}")
    
    def open_web_interface(self):
        """Open web interface in browser"""
        url = f"http://localhost:{self.config.config['service']['port']}/api/health"
        webbrowser.open(url)
    
    def show_settings_window(self):
        """Show settings configuration window"""
        try:
            root = tk.Tk()
            root.title("GoID Biometric Service Settings")
            root.geometry("500x400")
            
            # Create notebook for tabs
            notebook = tk.Frame(root)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Server settings
            server_frame = tk.LabelFrame(notebook, text="Server Settings", padx=10, pady=10)
            server_frame.pack(fill=tk.X, pady=5)
            
            tk.Label(server_frame, text="Server URL:").grid(row=0, column=0, sticky=tk.W)
            server_url_var = tk.StringVar(value=self.config.config['server']['url'])
            tk.Entry(server_frame, textvariable=server_url_var, width=40).grid(row=0, column=1, padx=5)
            
            # Client settings
            client_frame = tk.LabelFrame(notebook, text="Client Information", padx=10, pady=10)
            client_frame.pack(fill=tk.X, pady=5)
            
            tk.Label(client_frame, text="Kebele Name:").grid(row=0, column=0, sticky=tk.W)
            kebele_var = tk.StringVar(value=self.config.config['client_info']['kebele_name'])
            tk.Entry(client_frame, textvariable=kebele_var, width=40).grid(row=0, column=1, padx=5)
            
            tk.Label(client_frame, text="Client ID:").grid(row=1, column=0, sticky=tk.W)
            client_id_var = tk.StringVar(value=self.config.config['client_info']['client_id'])
            tk.Entry(client_frame, textvariable=client_id_var, width=40).grid(row=1, column=1, padx=5)
            
            # Buttons
            button_frame = tk.Frame(notebook)
            button_frame.pack(fill=tk.X, pady=10)
            
            def save_settings():
                # Update configuration
                self.config.config['server']['url'] = server_url_var.get()
                self.config.config['client_info']['kebele_name'] = kebele_var.get()
                self.config.config['client_info']['client_id'] = client_id_var.get()
                
                # Save to file
                with open(self.config.config_file, 'w') as f:
                    json.dump(self.config.config, f, indent=2)
                
                messagebox.showinfo("Settings", "Settings saved successfully!\nRestart service to apply changes.")
                root.destroy()
            
            tk.Button(button_frame, text="Save", command=save_settings).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Cancel", command=root.destroy).pack(side=tk.RIGHT, padx=5)
            
            root.mainloop()
            
        except Exception as e:
            self.logger.error(f"Failed to show settings window: {e}")
    
    def create_menu(self):
        """Create system tray context menu"""
        return pystray.Menu(
            pystray.MenuItem("GoID Biometric Service", None, enabled=False),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Status", self.show_status_window),
            pystray.MenuItem("Settings", self.show_settings_window),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Start Service", self.start_service, 
                           enabled=lambda item: not self.running),
            pystray.MenuItem("Stop Service", self.stop_service, 
                           enabled=lambda item: self.running),
            pystray.MenuItem("Restart Service", self.restart_service, 
                           enabled=lambda item: self.running),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Open Web Interface", self.open_web_interface),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Exit", self.quit_application)
        )
    
    def quit_application(self):
        """Quit the application"""
        self.stop_service()
        self.icon.stop()
    
    def run(self):
        """Run the system tray application"""
        try:
            # Create system tray icon
            self.icon = pystray.Icon(
                "goid_biometric",
                self.create_icon_image("gray"),
                "GoID Biometric Service",
                self.create_menu()
            )
            
            # Auto-start service
            self.start_service()
            
            # Run system tray
            self.icon.run()
            
        except Exception as e:
            self.logger.error(f"Failed to run tray application: {e}")
            messagebox.showerror("Error", f"Failed to start application: {str(e)}")

def main():
    """Main entry point"""
    try:
        # Ensure single instance
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8002))
        sock.close()
        
        if result == 0:
            messagebox.showwarning(
                "GoID Biometric Service", 
                "Service is already running!\nCheck system tray for the running instance."
            )
            return
        
        # Start tray service
        service = BiometricTrayService()
        service.run()
        
    except KeyboardInterrupt:
        print("Service stopped by user")
    except Exception as e:
        logging.error(f"Application error: {e}")
        messagebox.showerror("Error", f"Application error: {str(e)}")

if __name__ == '__main__':
    main()
