import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogFooter } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Calendar, Clock, Mail, Info } from 'lucide-react';
import reportsService from '../../services/reportsService';
import { useToast } from '../../hooks/use-toast';

const ReportScheduleModal = ({ isOpen, onClose, templates }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    template: '',
    format: 'pdf',
    cron_expression: '0 9 * * 1', // Weekly on Monday at 9 AM
    timezone: 'UTC',
    email_recipients: []
  });
  
  const [emailInput, setEmailInput] = useState('');
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const cronPresets = [
    { label: 'Daily at 9 AM', value: '0 9 * * *', description: 'Every day at 9:00 AM' },
    { label: 'Weekly on Monday', value: '0 9 * * 1', description: 'Every Monday at 9:00 AM' },
    { label: 'Monthly on 1st', value: '0 9 1 * *', description: 'First day of every month at 9:00 AM' },
    { label: 'Quarterly', value: '0 9 1 */3 *', description: 'First day of every quarter at 9:00 AM' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const handleAddEmail = () => {
    if (emailInput.trim() && !formData.email_recipients.includes(emailInput.trim())) {
      setFormData(prev => ({
        ...prev,
        email_recipients: [...prev.email_recipients, emailInput.trim()]
      }));
      setEmailInput('');
    }
  };

  const handleRemoveEmail = (email) => {
    setFormData(prev => ({
      ...prev,
      email_recipients: prev.email_recipients.filter(e => e !== email)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    const validation = reportsService.validateScheduleData(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    
    setLoading(true);
    try {
      await reportsService.createReportSchedule(formData);
      
      toast({
        title: "Success",
        description: "Report schedule created successfully"
      });
      
      handleClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create report schedule",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      template: '',
      format: 'pdf',
      cron_expression: '0 9 * * 1',
      timezone: 'UTC',
      email_recipients: []
    });
    setEmailInput('');
    setErrors({});
    onClose();
  };

  const selectedTemplate = templates.find(t => t.id === formData.template);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Schedule Report
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Schedule Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter schedule name"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template">Template *</Label>
                  <Select 
                    value={formData.template} 
                    onValueChange={(value) => handleInputChange('template', value)}
                  >
                    <SelectTrigger className={errors.template ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.template && (
                    <p className="text-sm text-red-500">{errors.template}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter schedule description (optional)"
                  rows={3}
                />
              </div>

              {selectedTemplate && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-blue-900">Selected Template</div>
                      <div className="text-sm text-blue-700 mt-1">
                        {selectedTemplate.description}
                      </div>
                      <div className="flex gap-2 mt-2">
                        <Badge variant="secondary">{selectedTemplate.report_type_display}</Badge>
                        {selectedTemplate.is_system_template && (
                          <Badge variant="outline">System</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Schedule Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Schedule Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Schedule Frequency *</Label>
                  <div className="space-y-2">
                    {cronPresets.map((preset) => (
                      <div
                        key={preset.value}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          formData.cron_expression === preset.value 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handleInputChange('cron_expression', preset.value)}
                      >
                        <div className="font-medium">{preset.label}</div>
                        <div className="text-sm text-muted-foreground">{preset.description}</div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="format">Output Format</Label>
                    <Select 
                      value={formData.format} 
                      onValueChange={(value) => handleInputChange('format', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pdf">PDF</SelectItem>
                        <SelectItem value="excel">Excel</SelectItem>
                        <SelectItem value="csv">CSV</SelectItem>
                        <SelectItem value="json">JSON</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cron_expression">Custom Cron Expression</Label>
                    <Input
                      id="cron_expression"
                      value={formData.cron_expression}
                      onChange={(e) => handleInputChange('cron_expression', e.target.value)}
                      placeholder="0 9 * * 1"
                      className={errors.cron_expression ? 'border-red-500' : ''}
                    />
                    {errors.cron_expression && (
                      <p className="text-sm text-red-500">{errors.cron_expression}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Format: minute hour day month day_of_week
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Email Recipients */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Recipients
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={emailInput}
                  onChange={(e) => setEmailInput(e.target.value)}
                  placeholder="Enter email address"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddEmail())}
                />
                <Button type="button" onClick={handleAddEmail} variant="outline">
                  Add
                </Button>
              </div>

              {formData.email_recipients.length > 0 && (
                <div className="space-y-2">
                  <Label>Recipients:</Label>
                  <div className="flex flex-wrap gap-2">
                    {formData.email_recipients.map((email) => (
                      <Badge key={email} variant="secondary" className="cursor-pointer">
                        {email}
                        <button
                          type="button"
                          onClick={() => handleRemoveEmail(email)}
                          className="ml-2 text-red-500 hover:text-red-700"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {errors.email_recipients && (
                <p className="text-sm text-red-500">{errors.email_recipients}</p>
              )}
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Schedule'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ReportScheduleModal;
