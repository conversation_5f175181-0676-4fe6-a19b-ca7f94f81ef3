/**
 * Multi-Tenant Role-Based Access Control (RBAC) System
 * 
 * Handles hierarchical tenant relationships:
 * - City Admin manages Subcity Users
 * - Subcity Admin manages Kebele Users  
 * - Tenant-specific permissions
 * - Cross-tenant access control
 */

/**
 * Tenant hierarchy levels
 */
export const TENANT_LEVELS = {
  SYSTEM: 'system',
  CITY: 'city', 
  SUBCITY: 'subcity',
  KEBELE: 'kebele'
};

/**
 * Permission scopes for multi-tenant access
 */
export const PERMISSION_SCOPES = {
  // System-wide permissions (superadmin only)
  SYSTEM: {
    manage_all_tenants: 'Can manage all tenants across system',
    manage_all_users: 'Can manage all users across system',
    full_system_access: 'Full access to all system features',
    view_all_tenants_data: 'Can view data from all tenants'
  },

  // City-level permissions
  CITY: {
    manage_subcities: 'Can manage subcity tenants',
    create_subcity_users: 'Can create and manage subcity users',
    view_child_subcities_data: 'Can view data from child subcities',
    view_city_dashboard: 'Can view city-wide dashboard',
    view_city_reports: 'Can view city-wide reports'
  },

  // Subcity-level permissions  
  SUBCITY: {
    manage_kebeles: 'Can manage kebele tenants',
    create_kebele_users: 'Can create and manage kebele users',
    view_child_kebeles_data: 'Can view data from child kebeles',
    view_subcity_dashboard: 'Can view subcity dashboard',
    view_subcity_reports: 'Can view subcity reports',
    approve_id_cards: 'Can approve ID cards from kebeles',
    print_id_cards: 'Can print ID cards',
    verify_documents: 'Can verify documents'
  },

  // Kebele-level permissions
  KEBELE: {
    register_citizens: 'Can register citizens',
    view_citizens_list: 'Can view citizens in kebele',
    view_citizen_details: 'Can view citizen details',
    generate_id_cards: 'Can generate ID cards',
    view_id_cards_list: 'Can view ID cards list',
    print_idcards: 'Can print ID cards locally',
    view_kebele_dashboard: 'Can view kebele dashboard',
    view_kebele_reports: 'Can view kebele reports',
    local_document_verification: 'Can verify documents locally'
  },

  // Workflow-specific permissions
  WORKFLOW: {
    manage_workflows: 'Can manage and switch workflow types',
    view_workflows: 'Can view workflow configurations',
    autonomous_printing: 'Can print ID cards in autonomous workflow',
    centralized_approval: 'Can approve in centralized workflow'
  }
};

/**
 * Check if user has permission within their tenant context
 * @param {Object} user - User object with tenant and permissions
 * @param {string} permission - Permission to check
 * @returns {boolean} - True if user has permission
 */
export const hasPermission = (user, permission) => {
  if (!user || !user.permissions) {
    console.log(`🔍 MT-RBAC: No user or permissions for ${permission}`);
    return false;
  }

  // Superuser bypass
  if (user.is_superuser) {
    console.log(`🔍 MT-RBAC: Superuser bypass for ${permission}`);
    return true;
  }

  // Check if user has the specific permission in their tenant context
  const hasAccess = user.permissions.includes(permission);
  console.log(`🔍 MT-RBAC: ${permission} in ${user.tenant?.name || 'unknown'} = ${hasAccess ? 'ALLOWED' : 'DENIED'}`);
  
  return hasAccess;
};

/**
 * Check if user can manage users in a specific tenant
 * @param {Object} user - Current user
 * @param {Object} targetTenant - Tenant where user wants to manage users
 * @returns {boolean} - True if user can manage users in target tenant
 */
export const canManageUsersInTenant = (user, targetTenant) => {
  if (!user || !targetTenant) return false;

  // Superuser can manage anywhere
  if (user.is_superuser) return true;

  // System admin can manage anywhere
  if (hasPermission(user, 'manage_all_users')) return true;

  // City admin can manage subcity users
  if (user.tenant?.type === 'city' && targetTenant.type === 'subcity') {
    return targetTenant.parent?.id === user.tenant.id && 
           hasPermission(user, 'create_subcity_users');
  }

  // Subcity admin can manage kebele users
  if (user.tenant?.type === 'subcity' && targetTenant.type === 'kebele') {
    return targetTenant.parent?.id === user.tenant.id && 
           hasPermission(user, 'create_kebele_users');
  }

  console.log(`🔍 MT-RBAC: ${user.email} cannot manage users in ${targetTenant.name} (${targetTenant.type})`);
  return false;
};

/**
 * Check if user can access data from a specific tenant
 * @param {Object} user - Current user
 * @param {Object} targetTenant - Tenant data user wants to access
 * @returns {boolean} - True if user can access tenant data
 */
export const canAccessTenantData = (user, targetTenant) => {
  if (!user || !targetTenant) return false;

  // Superuser can access all data
  if (user.is_superuser) return true;

  // System admin can access all data
  if (hasPermission(user, 'view_all_tenants_data')) return true;

  // Users can access their own tenant data
  if (user.tenant?.id === targetTenant.id) return true;

  // City admin can access child subcity data
  if (user.tenant?.type === 'city' && targetTenant.type === 'subcity') {
    return targetTenant.parent?.id === user.tenant.id && 
           hasPermission(user, 'view_child_subcities_data');
  }

  // Subcity admin can access child kebele data
  if (user.tenant?.type === 'subcity' && targetTenant.type === 'kebele') {
    return targetTenant.parent?.id === user.tenant.id && 
           hasPermission(user, 'view_child_kebeles_data');
  }

  console.log(`🔍 MT-RBAC: ${user.email} cannot access data from ${targetTenant.name}`);
  return false;
};

/**
 * Get navigation items based on user's tenant level and permissions
 * @param {Object} user - User object with tenant and permissions
 * @returns {Array} - Array of navigation items
 */
export const generateTenantAwareNavigation = (user) => {
  console.log('🔍 MT-RBAC: Generating navigation for:', {
    user: user?.email,
    tenant: user?.tenant?.name,
    tenantType: user?.tenant?.type,
    permissions: user?.permissions?.length || 0
  });

  const navigationItems = [];

  // Dashboard - always show
  navigationItems.push({
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'Dashboard'
  });

  // System-level navigation (Superuser/System Admin)
  if (hasPermission(user, 'manage_all_tenants') || hasPermission(user, 'full_system_access')) {
    navigationItems.push(
      {
        id: 'tenants',
        label: 'Tenants',
        path: '/tenants',
        icon: 'Business'
      },
      {
        id: 'system-users',
        label: 'System Users',
        path: '/users/system',
        icon: 'SupervisorAccount'
      }
    );
    console.log('✅ MT-RBAC: Added system admin navigation');
  }

  // City-level navigation
  if (user.tenant?.type === 'city') {
    if (hasPermission(user, 'view_child_subcities_data')) {
      navigationItems.push({
        id: 'citizen-dictionary',
        label: 'Citizen Dictionary',
        path: '/citizens/citizen-book',
        icon: 'People'
      });
    }

    if (hasPermission(user, 'create_subcity_users')) {
      navigationItems.push({
        id: 'subcity-users',
        label: 'Subcity Management',
        path: '/users/city-management',
        icon: 'SupervisorAccount'
      });
    }

    if (hasPermission(user, 'view_city_reports')) {
      navigationItems.push({
        id: 'reports',
        label: 'City Reports',
        path: '/reports',
        icon: 'Assessment'
      });
    }
    console.log('✅ MT-RBAC: Added city admin navigation');
  }

  // Subcity-level navigation
  if (user.tenant?.type === 'subcity') {
    if (hasPermission(user, 'view_child_kebeles_data')) {
      navigationItems.push({
        id: 'citizens',
        label: 'Citizens',
        path: '/citizens/all-kebeles',
        icon: 'People'
      });
    }

    if (hasPermission(user, 'print_id_cards')) {
      navigationItems.push({
        id: 'idcards',
        label: 'ID Cards',
        path: '/idcards/all-kebeles',
        icon: 'Badge'
      });
    }

    if (hasPermission(user, 'print_id_cards') || hasPermission(user, 'print_idcards')) {
      navigationItems.push({
        id: 'print-queue',
        label: 'Print Queue',
        path: '/idcards/printing-queue',
        icon: 'Print'
      });
    }

    if (hasPermission(user, 'create_kebele_users')) {
      navigationItems.push({
        id: 'kebele-management',
        label: 'Kebele Management',
        path: '/users/kebele-management',
        icon: 'SupervisorAccount'
      });
    }

    if (hasPermission(user, 'view_subcity_reports')) {
      navigationItems.push({
        id: 'reports',
        label: 'Subcity Reports',
        path: '/reports',
        icon: 'Assessment'
      });
    }
    console.log('✅ MT-RBAC: Added subcity admin navigation');
  }

  // Kebele-level navigation
  if (user.tenant?.type === 'kebele') {
    if (hasPermission(user, 'register_citizens')) {
      navigationItems.push({
        id: 'register-citizen',
        label: 'Register Citizen',
        path: '/citizens/register',
        icon: 'PersonAdd'
      });
    }

    if (hasPermission(user, 'view_citizens_list')) {
      navigationItems.push({
        id: 'citizens',
        label: 'Citizens',
        path: '/citizens',
        icon: 'People'
      });
    }

    if (hasPermission(user, 'generate_id_cards') || hasPermission(user, 'view_id_cards_list')) {
      navigationItems.push({
        id: 'idcards',
        label: 'ID Cards',
        path: '/idcards',
        icon: 'Badge'
      });
    }

    if (hasPermission(user, 'print_id_cards') || hasPermission(user, 'print_idcards')) {
      navigationItems.push({
        id: 'print-queue',
        label: 'Print Queue',
        path: '/idcards/printing-queue',
        icon: 'Print'
      });
    }

    if (hasPermission(user, 'view_kebele_reports')) {
      navigationItems.push({
        id: 'reports',
        label: 'Reports',
        path: '/reports',
        icon: 'Assessment'
      });
    }
    console.log('✅ MT-RBAC: Added kebele navigation');
  }

  console.log(`🔍 MT-RBAC: Generated ${navigationItems.length} navigation items for ${user.tenant?.type} level`);
  return navigationItems;
};

/**
 * Check if user can perform workflow management
 * @param {Object} user - Current user
 * @param {Object} targetTenant - Tenant where workflow will be changed
 * @returns {boolean} - True if user can manage workflows
 */
export const canManageWorkflow = (user, targetTenant) => {
  if (!user || !targetTenant) return false;

  // Must have workflow management permission
  if (!hasPermission(user, 'manage_workflows')) return false;

  // Subcity admin can manage workflows for child kebeles
  if (user.tenant?.type === 'subcity' && targetTenant.type === 'kebele') {
    return targetTenant.parent?.id === user.tenant.id;
  }

  // City admin can manage workflows for child subcities
  if (user.tenant?.type === 'city' && targetTenant.type === 'subcity') {
    return targetTenant.parent?.id === user.tenant.id;
  }

  return false;
};

export default {
  hasPermission,
  canManageUsersInTenant,
  canAccessTenantData,
  generateTenantAwareNavigation,
  canManageWorkflow,
  TENANT_LEVELS,
  PERMISSION_SCOPES
};
