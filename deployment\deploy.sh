#!/bin/bash
# GoID Deployment Script - Deploy to production server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ACTION="deploy"
ENV_FILE="deployment/.env.production"
COMPOSE_FILE=""
SERVICES=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [ACTION] [OPTIONS]"
    echo ""
    echo "Actions:"
    echo "  deploy                  Deploy services (default)"
    echo "  stop                    Stop all services"
    echo "  status                  Show service status"
    echo "  logs                    Show service logs"
    echo "  pull                    Pull latest images"
    echo "  generate                Generate docker-compose file"
    echo ""
    echo "Options:"
    echo "  -e, --env-file FILE     Environment file [default: deployment/.env.production]"
    echo "  -f, --compose-file FILE Docker compose file to use"
    echo "  -s, --services SERVICES Specific services to deploy (space-separated)"
    echo "  --service SERVICE       Service for logs action"
    echo "  --follow               Follow logs (for logs action)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Deploy all services"
    echo "  $0 deploy -s \"backend frontend\"      # Deploy specific services"
    echo "  $0 status                             # Show service status"
    echo "  $0 logs --service backend --follow    # Follow backend logs"
    echo "  $0 stop                               # Stop all services"
}

# Parse command line arguments
if [[ $# -gt 0 && ! "$1" =~ ^- ]]; then
    ACTION="$1"
    shift
fi

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        -f|--compose-file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -s|--services)
            SERVICES="$2"
            shift 2
            ;;
        --service)
            SERVICE="$2"
            shift 2
            ;;
        --follow)
            FOLLOW=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if environment file exists for deploy action
if [[ "$ACTION" == "deploy" || "$ACTION" == "generate" ]]; then
    if [[ ! -f "$ENV_FILE" ]]; then
        print_warning "Environment file $ENV_FILE not found"
        print_status "Creating sample environment file..."
        
        # Create sample environment file
        cat > "$ENV_FILE" << EOF
# GoID Production Environment Configuration

# Docker Hub Configuration
DOCKER_HUB_USERNAME=your-dockerhub-username

# Service Versions (will be auto-populated from versions.json)
BACKEND_VERSION=latest
FRONTEND_VERSION=latest
BIOMETRIC_VERSION=latest

# Database Configuration
DB_PASSWORD=your-secure-db-password
DB_PORT=5432

# Redis Configuration
REDIS_PORT=6379

# Application Configuration
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://your-domain.com

# API URLs
REACT_APP_API_URL=http://localhost:8000
REACT_APP_BIOMETRIC_SERVICE_URL=http://localhost:8002

# Admin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=5050
EOF
        
        print_warning "Please edit $ENV_FILE with your configuration before deploying"
        print_status "Especially update: DOCKER_HUB_USERNAME, DB_PASSWORD, SECRET_KEY, ALLOWED_HOSTS"
        exit 1
    fi
fi

# Build deployment command
DEPLOY_CMD="python3 deployment/deploy.py $ACTION"

if [[ -n "$ENV_FILE" ]]; then
    DEPLOY_CMD="$DEPLOY_CMD --env-file $ENV_FILE"
fi

if [[ -n "$COMPOSE_FILE" ]]; then
    DEPLOY_CMD="$DEPLOY_CMD --compose-file $COMPOSE_FILE"
fi

if [[ -n "$SERVICES" ]]; then
    DEPLOY_CMD="$DEPLOY_CMD --services $SERVICES"
fi

if [[ -n "$SERVICE" ]]; then
    DEPLOY_CMD="$DEPLOY_CMD --service $SERVICE"
fi

if [[ "$FOLLOW" == true ]]; then
    DEPLOY_CMD="$DEPLOY_CMD --follow"
fi

# Execute action
print_status "Executing: $ACTION"

case $ACTION in
    deploy)
        print_status "Deploying GoID services..."
        if [[ -n "$SERVICES" ]]; then
            print_status "Services: $SERVICES"
        else
            print_status "Services: all"
        fi
        ;;
    stop)
        print_status "Stopping GoID services..."
        ;;
    status)
        print_status "Checking service status..."
        ;;
    logs)
        if [[ -n "$SERVICE" ]]; then
            print_status "Showing logs for: $SERVICE"
        else
            print_status "Showing logs for all services"
        fi
        ;;
    pull)
        print_status "Pulling latest images..."
        ;;
    generate)
        print_status "Generating docker-compose file..."
        ;;
esac

if eval $DEPLOY_CMD; then
    case $ACTION in
        deploy)
            print_success "Deployment completed successfully!"
            print_status "Services are starting up. Use '$0 status' to check status."
            print_status "Use '$0 logs' to view logs."
            ;;
        stop)
            print_success "Services stopped successfully!"
            ;;
        generate)
            print_success "Docker compose file generated successfully!"
            ;;
        *)
            print_success "Action completed successfully!"
            ;;
    esac
else
    print_error "Action failed!"
    exit 1
fi
