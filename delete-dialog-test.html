<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Dialog Test</title>
    <!-- Material-UI styles -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
        }
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .dialog {
            background-color: white;
            border-radius: 8px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        .dialog-title {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 16px 24px;
            color: #f44336;
            border-bottom: 1px solid #eee;
        }
        .dialog-content {
            padding: 16px 24px;
        }
        .dialog-actions {
            display: flex;
            justify-content: flex-end;
            padding: 16px 24px;
            gap: 8px;
        }
        .user-info {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .user-detail {
            color: #666;
            margin-bottom: 4px;
        }
        .warning-text {
            color: #f44336;
            margin-top: 16px;
            font-weight: 500;
        }
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            outline: none;
        }
        .btn-outlined {
            background-color: white;
            border: 1px solid #ccc;
        }
        .btn-contained {
            background-color: #f44336;
            color: white;
        }
        .dialog-border-top {
            border-top: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <h1>Delete Dialog Test</h1>
    <p>Click the button below to open the delete confirmation dialog:</p>
    <button id="openDialog" class="btn btn-contained">Delete User</button>

    <div id="deleteDialog" class="dialog-overlay" style="display: none;">
        <div class="dialog dialog-border-top">
            <div class="dialog-title">
                <span class="material-icons">delete</span>
                Confirm User Deletion
            </div>
            <div class="dialog-content">
                <div>
                    <p>Are you sure you want to delete the following user?</p>
                    
                    <div class="user-info">
                        <div class="user-name">Addisu Melese</div>
                        <div class="user-detail">Email: <EMAIL></div>
                        <div class="user-detail">Username: addisu</div>
                        <div class="user-detail">Role: clerk</div>
                    </div>
                    
                    <p class="warning-text">This action cannot be undone. The user will be permanently removed from the system.</p>
                </div>
            </div>
            <div class="dialog-actions">
                <button id="cancelDelete" class="btn btn-outlined">Cancel</button>
                <button id="confirmDelete" class="btn btn-contained">
                    <span class="material-icons" style="font-size: 16px; margin-right: 4px;">delete</span>
                    Delete User
                </button>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('openDialog').addEventListener('click', function() {
            document.getElementById('deleteDialog').style.display = 'flex';
        });

        document.getElementById('cancelDelete').addEventListener('click', function() {
            document.getElementById('deleteDialog').style.display = 'none';
        });

        document.getElementById('confirmDelete').addEventListener('click', function() {
            alert('User deleted successfully!');
            document.getElementById('deleteDialog').style.display = 'none';
        });
    </script>
</body>
</html>
