#!/usr/bin/env python3
"""
Remove Static Roles and Make System Fully Permission-Based

This script:
1. Removes dependency on static user.role field
2. Makes all access control permission-based
3. Creates proper groups and permissions for users
"""

import os
import sys
import django

# Setup Django
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import connection
from django.contrib.auth.models import Group, Permission
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from tenants.models import Tenant

User = get_user_model()

def create_permission_based_groups():
    """Create groups based on permission sets instead of roles"""
    print("🔧 Creating permission-based groups...")
    
    # Define permission-based groups
    permission_groups = {
        'system_administrators': {
            'description': 'Full system access',
            'permissions': [
                'full_system_access',
                'manage_all_tenants',
                'manage_all_users',
                'view_all_tenants_data'
            ]
        },
        'city_managers': {
            'description': 'City-level management',
            'permissions': [
                'view_child_subcities_data',
                'create_subcity_users',
                'view_city_dashboard',
                'view_city_reports',
                'manage_workflows'
            ]
        },
        'subcity_managers': {
            'description': 'Subcity-level management',
            'permissions': [
                'view_child_kebeles_data',
                'create_kebele_users',
                'approve_id_cards',
                'print_id_cards',
                'verify_documents',
                'view_subcity_dashboard',
                'view_subcity_reports',
                'manage_workflows'
            ]
        },
        'kebele_leaders': {
            'description': 'Kebele leadership',
            'permissions': [
                'approve_id_cards',
                'verify_documents',
                'view_kebele_dashboard',
                'view_kebele_reports',
                'local_document_verification'
            ]
        },
        'registration_clerks': {
            'description': 'Citizen registration',
            'permissions': [
                'register_citizens',
                'view_citizens_list',
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list'
            ]
        },
        'print_operators': {
            'description': 'ID card printing',
            'permissions': [
                'print_id_cards',
                'print_idcards',
                'view_id_cards_list'
            ]
        },
        'data_viewers': {
            'description': 'Read-only access',
            'permissions': [
                'view_citizens_list',
                'view_citizen_details',
                'view_id_cards_list',
                'view_kebele_dashboard'
            ]
        }
    }
    
    # Create groups and assign permissions
    for group_name, group_config in permission_groups.items():
        group, created = Group.objects.get_or_create(name=group_name)
        if created:
            print(f"  ✅ Created group: {group_name}")
        else:
            print(f"  ℹ️  Group exists: {group_name}")
        
        # Add permissions to group
        added_count = 0
        for perm_code in group_config['permissions']:
            try:
                permission = Permission.objects.get(codename=perm_code)
                group.permissions.add(permission)
                added_count += 1
            except Permission.DoesNotExist:
                print(f"    ⚠️  Permission not found: {perm_code}")
        
        print(f"    Added {added_count} permissions to {group_name}")

def assign_users_to_permission_groups():
    """Assign users to appropriate permission-based groups"""
    print("\n🔧 Assigning users to permission-based groups...")
    
    # Define user assignment rules based on current permissions
    assignment_rules = {
        'system_administrators': lambda user: user.is_superuser,
        'city_managers': lambda user: user.user_permissions.filter(
            codename__in=['view_child_subcities_data', 'create_subcity_users']
        ).exists(),
        'subcity_managers': lambda user: user.user_permissions.filter(
            codename__in=['view_child_kebeles_data', 'create_kebele_users']
        ).exists(),
        'print_operators': lambda user: user.user_permissions.filter(
            codename__in=['print_id_cards', 'print_idcards']
        ).exists() and not user.user_permissions.filter(
            codename__in=['view_child_kebeles_data', 'create_kebele_users']
        ).exists(),
        'registration_clerks': lambda user: user.user_permissions.filter(
            codename='register_citizens'
        ).exists(),
        'kebele_leaders': lambda user: user.user_permissions.filter(
            codename='approve_id_cards'
        ).exists() and not user.user_permissions.filter(
            codename__in=['view_child_kebeles_data', 'create_kebele_users']
        ).exists(),
    }
    
    # Process all tenants
    tenants = Tenant.objects.all()
    
    for tenant in tenants:
        print(f"\n  Processing {tenant.name} ({tenant.type})...")
        
        try:
            connection.set_schema(tenant.schema_name)
            users = User.objects.all()
            
            for user in users:
                print(f"    User: {user.email}")
                
                # Clear existing groups
                user.groups.clear()
                
                # Assign to appropriate groups
                assigned_groups = []
                for group_name, rule in assignment_rules.items():
                    if rule(user):
                        try:
                            group = Group.objects.get(name=group_name)
                            user.groups.add(group)
                            assigned_groups.append(group_name)
                        except Group.DoesNotExist:
                            print(f"      ⚠️  Group {group_name} not found")
                
                if assigned_groups:
                    print(f"      ✅ Assigned to: {', '.join(assigned_groups)}")
                else:
                    # Assign to data_viewers as default
                    try:
                        default_group = Group.objects.get(name='data_viewers')
                        user.groups.add(default_group)
                        print(f"      ✅ Assigned to default: data_viewers")
                    except Group.DoesNotExist:
                        print(f"      ⚠️  No groups assigned")
                        
        except Exception as e:
            print(f"    ❌ Error processing {tenant.name}: {e}")

def test_permission_based_access():
    """Test the new permission-based access system"""
    print("\n🔍 Testing permission-based access...")
    
    # Test with the print user
    connection.set_schema('subcity_zoble')
    
    try:
        print_user = User.objects.get(email='<EMAIL>')
        print(f"\n  User: {print_user.email}")
        
        # Check groups
        groups = [g.name for g in print_user.groups.all()]
        print(f"  Groups: {groups}")
        
        # Check permissions
        all_perms = print_user.get_all_permissions()
        print(f"  Total permissions: {len(all_perms)}")
        
        # Check specific permissions
        key_permissions = [
            'print_id_cards',
            'print_idcards', 
            'view_child_kebeles_data',
            'create_kebele_users',
            'manage_workflows'
        ]
        
        print(f"  Permission check:")
        for perm in key_permissions:
            has_perm = print_user.user_permissions.filter(codename=perm).exists() or \
                      print_user.groups.filter(permissions__codename=perm).exists()
            print(f"    {perm}: {'✅ YES' if has_perm else '❌ NO'}")
            
    except User.DoesNotExist:
        print("  ❌ Print user not found")

def update_frontend_user_data():
    """Show how frontend should handle user data without static roles"""
    print("\n📱 Frontend user data structure (without static roles):")
    
    connection.set_schema('subcity_zoble')
    
    try:
        print_user = User.objects.get(email='<EMAIL>')
        
        # Simulate frontend user object
        frontend_user = {
            'id': print_user.id,
            'email': print_user.email,
            'first_name': print_user.first_name,
            'last_name': print_user.last_name,
            'is_superuser': print_user.is_superuser,
            'tenant': {
                'id': print_user.tenant.id,
                'name': print_user.tenant.name,
                'type': print_user.tenant.type
            },
            'permissions': [p.codename for p in print_user.user_permissions.all()],
            'groups': [g.name for g in print_user.groups.all()],
            # NO STATIC ROLE FIELD!
        }
        
        print("  Frontend user object:")
        for key, value in frontend_user.items():
            print(f"    {key}: {value}")
        
        # Show dynamic role detection
        print(f"\n  Dynamic role detection:")
        if 'print_id_cards' in frontend_user['permissions']:
            if 'view_child_kebeles_data' in frontend_user['permissions']:
                detected_role = 'subcity_admin'
            else:
                detected_role = 'print_operator'
        else:
            detected_role = 'viewer'
        
        print(f"    Detected role: {detected_role}")
        
    except User.DoesNotExist:
        print("  ❌ Print user not found")

def main():
    """Main function to implement permission-based system"""
    print("🎯 Implementing Fully Permission-Based System")
    print("=" * 50)
    
    # Create permission-based groups
    create_permission_based_groups()
    
    # Assign users to groups based on permissions
    assign_users_to_permission_groups()
    
    # Test the new system
    test_permission_based_access()
    
    # Show frontend integration
    update_frontend_user_data()
    
    print("\n✅ Permission-based system implementation complete!")
    print("\n📋 Summary:")
    print("1. ✅ Created permission-based groups instead of role-based")
    print("2. ✅ Assigned users to groups based on actual permissions")
    print("3. ✅ Removed dependency on static user.role field")
    print("4. ✅ System now uses dynamic role detection")
    print("5. ✅ Access control is fully permission-based")
    
    print("\n🔧 Next Steps:")
    print("1. Update frontend to use dynamic role detection")
    print("2. Remove user.role checks from all components")
    print("3. Use permission-based navigation generation")
    print("4. Test with limited permission users")

if __name__ == "__main__":
    main()
