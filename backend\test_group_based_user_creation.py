#!/usr/bin/env python3
"""
Test Group-Based User Creation

This script tests the new group-based user creation system to ensure:
1. Users with custom groups don't get automatic static roles
2. Users get permissions from their assigned groups
3. Static role conflicts are avoided
"""

import os
import sys
import django

# Setup Django
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import connection
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from tenants.models import Tenant
from users.models_groups import TenantGroup, GroupMembership

User = get_user_model()

def test_group_based_user_creation():
    """Test creating a user with custom group assignment"""
    print("🧪 Testing Group-Based User Creation")
    print("=" * 50)
    
    # Test in subcity schema
    connection.set_schema('subcity_zoble')
    
    # Create a test custom group with specific permissions
    print("\n1. Creating test custom group...")
    try:
        # Create group in public schema
        connection.set_schema('public')
        
        # Create Django group
        test_group, created = Group.objects.get_or_create(name='test_custom_group')
        if created:
            print(f"  ✅ Created Django group: {test_group.name}")
        
        # Create permissions for the group
        from django.contrib.contenttypes.models import ContentType
        user_ct = ContentType.objects.get_for_model(User)
        
        test_permissions = [
            ('print_id_cards', 'Can print ID cards'),
            ('view_citizens_list', 'Can view citizens list')
        ]
        
        for codename, name in test_permissions:
            perm, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=user_ct,
                defaults={'name': name}
            )
            test_group.permissions.add(perm)
            print(f"  ✅ Added permission: {codename}")
        
        # Create TenantGroup
        subcity_tenant = Tenant.objects.get(schema_name='subcity_zoble')
        tenant_group, created = TenantGroup.objects.get_or_create(
            group=test_group,
            tenant=subcity_tenant,
            defaults={
                'description': 'Test custom group for RBAC testing',
                'group_type': 'custom',
                'level': 30,
                'is_system_group': False,
                'is_active': True
            }
        )
        if created:
            print(f"  ✅ Created TenantGroup: {tenant_group.id}")
        
    except Exception as e:
        print(f"  ❌ Error creating test group: {e}")
        return False
    
    # Test user creation with custom group
    print("\n2. Testing user creation with custom group...")
    connection.set_schema('subcity_zoble')
    
    try:
        # Delete test user if exists
        test_email = '<EMAIL>'
        User.objects.filter(email=test_email).delete()
        
        # Create user data (simulating frontend request)
        user_data = {
            'email': test_email,
            'username': 'test_group_user',
            'password': 'testpass123',
            'password2': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'subcity_admin',  # This should be ignored/cleared
            'tenant': subcity_tenant.id,
            'primary_group_id': tenant_group.id  # Custom group assignment
        }
        
        # Use the UserCreateSerializer
        from users.serializers import UserCreateSerializer
        serializer = UserCreateSerializer(data=user_data)
        
        if serializer.is_valid():
            user = serializer.save()
            print(f"  ✅ User created: {user.email}")
            print(f"  🔍 User role: {user.role} (should be None)")
            print(f"  🔍 User tenant: {user.tenant.name}")
            
            # Check group membership
            connection.set_schema('public')
            memberships = GroupMembership.objects.filter(
                user_email=user.email,
                is_active=True
            )
            
            print(f"  🔍 Group memberships: {memberships.count()}")
            for membership in memberships:
                print(f"    - Group: {membership.group.group.name}")
                print(f"    - Primary: {membership.is_primary}")
            
            # Test permissions
            connection.set_schema('subcity_zoble')
            user_permissions = user.get_all_permissions()
            print(f"  🔍 User permissions: {list(user_permissions)}")
            
            # Test specific permission
            has_print = user.has_perm('users.print_id_cards')
            print(f"  🔍 Has print_id_cards permission: {has_print}")
            
            return True
            
        else:
            print(f"  ❌ Serializer errors: {serializer.errors}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error creating user: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_response():
    """Test that the API returns correct permissions for group-based users"""
    print("\n3. Testing API response for group-based user...")
    
    connection.set_schema('subcity_zoble')
    
    try:
        user = User.objects.get(email='<EMAIL>')
        
        # Test UserSerializer response
        from users.serializers import UserSerializer
        serializer = UserSerializer(instance=user)
        data = serializer.data
        
        print(f"  🔍 API Response:")
        print(f"    Email: {data['email']}")
        print(f"    Role: {data.get('role', 'None')}")
        print(f"    Groups: {data.get('groups', [])}")
        print(f"    Permissions: {data.get('permissions', [])}")
        print(f"    Permissions count: {len(data.get('permissions', []))}")
        
        # Check if permissions are returned correctly
        permissions = data.get('permissions', [])
        if 'test_permission' in permissions:
            print(f"  ✅ Test permission found in API response")
        else:
            print(f"  ❌ Test permission not found in API response")
            
        return len(permissions) > 0
        
    except Exception as e:
        print(f"  ❌ Error testing API response: {e}")
        return False

def cleanup():
    """Clean up test data"""
    print("\n4. Cleaning up test data...")
    
    try:
        # Delete test user
        connection.set_schema('subcity_zoble')
        User.objects.filter(email='<EMAIL>').delete()
        print(f"  ✅ Deleted test user")
        
        # Delete test group memberships
        connection.set_schema('public')
        GroupMembership.objects.filter(
            group__group__name='test_custom_group'
        ).delete()
        print(f"  ✅ Deleted test group memberships")
        
        # Delete test tenant group
        TenantGroup.objects.filter(group__name='test_custom_group').delete()
        print(f"  ✅ Deleted test tenant group")
        
        # Delete test Django group
        Group.objects.filter(name='test_custom_group').delete()
        print(f"  ✅ Deleted test Django group")
        
    except Exception as e:
        print(f"  ⚠️ Cleanup error: {e}")

def main():
    """Main test function"""
    print("🎯 Group-Based User Creation Test")
    print("Testing fix for automatic static role assignment")
    print("=" * 60)
    
    try:
        # Run tests
        test1_passed = test_group_based_user_creation()
        test2_passed = test_api_response()
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 TEST RESULTS:")
        print(f"  Group-based user creation: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"  API response test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
        
        if test1_passed and test2_passed:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Users with custom groups no longer get automatic static roles")
            print("✅ Group-based permissions are working correctly")
        else:
            print("\n❌ SOME TESTS FAILED!")
            print("❌ Static role assignment issue may still exist")
            
    finally:
        # Always cleanup
        cleanup()

if __name__ == "__main__":
    main()
