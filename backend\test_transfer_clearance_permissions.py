#!/usr/bin/env python
"""
Test script to check if Transfer and Clearance permissions exist
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Permission, Group
from django_tenants.utils import schema_context, get_public_schema_name

def check_permissions():
    """Check if Transfer and Clearance permissions exist"""
    print("🔍 Checking Transfer and Clearance permissions...")
    
    with schema_context(get_public_schema_name()):
        # Check for Transfer and Clearance permissions
        transfer_clearance_perms = [
            'create_transfers', 'approve_transfer_requests', 'view_transfers', 'manage_transfers',
            'create_clearances', 'view_clearances', 'manage_clearances', 'approve_clearance_requests'
        ]
        
        found_perms = []
        missing_perms = []
        
        for perm_codename in transfer_clearance_perms:
            try:
                permission = Permission.objects.get(codename=perm_codename)
                print(f"✅ {perm_codename}: {permission.name}")
                found_perms.append(perm_codename)
            except Permission.DoesNotExist:
                print(f"❌ {perm_codename}: Permission not found")
                missing_perms.append(perm_codename)
        
        print(f"\n📊 Summary: {len(found_perms)} found, {len(missing_perms)} missing")
        return len(missing_perms) == 0

def check_kebele_leader_group():
    """Check kebele_leader group permissions"""
    print("\n🔍 Checking kebele_leader group permissions...")
    
    with schema_context(get_public_schema_name()):
        try:
            group = Group.objects.get(name='kebele_leader')
            permissions = group.permissions.all()
            print(f"✅ kebele_leader group found with {permissions.count()} permissions:")
            
            transfer_clearance_perms = []
            for perm in permissions:
                if any(keyword in perm.codename for keyword in ['transfer', 'clearance']):
                    transfer_clearance_perms.append(perm.codename)
                print(f"  - {perm.codename}: {perm.name}")
            
            print(f"\n🎯 Transfer/Clearance permissions in kebele_leader group: {len(transfer_clearance_perms)}")
            for perm in transfer_clearance_perms:
                print(f"  ✅ {perm}")
                
            return len(transfer_clearance_perms) > 0
            
        except Group.DoesNotExist:
            print("❌ kebele_leader group not found")
            return False

if __name__ == '__main__':
    print("🚀 Testing Transfer and Clearance permissions...")
    
    perms_exist = check_permissions()
    group_has_perms = check_kebele_leader_group()
    
    if not perms_exist:
        print("\n⚠️ Some permissions are missing. Run the setup_predefined_roles API to create them.")
    
    if not group_has_perms:
        print("\n⚠️ kebele_leader group doesn't have Transfer/Clearance permissions. Run the setup_predefined_roles API.")
    
    if perms_exist and group_has_perms:
        print("\n✅ All Transfer and Clearance permissions are properly set up!")
    
    print("\n✅ Test completed!")
