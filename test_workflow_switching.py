#!/usr/bin/env python3
"""
Test script for workflow switching functionality.

This script tests the complete workflow switching system including:
1. Setting up RBAC system
2. Creating test kebeles with different workflows
3. Testing workflow switching
4. Verifying permissions and role assignments
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from tenants.models import Tenant, TenantWorkflowConfig
from tenants.models.subcity import SubCity
from tenants.models.kebele import Kebele

User = get_user_model()

class WorkflowSwitchingTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.auth_token = None
        self.test_results = []

    def log_test(self, test_name, success, message=""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"   📝 {message}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })

    def setup_test_data(self):
        """Set up test data for workflow switching tests."""
        print("\n🔧 Setting up test data...")
        
        try:
            # Create test subcity if it doesn't exist
            subcity, created = SubCity.objects.get_or_create(
                name="Test Subcity for Workflow",
                defaults={
                    'name_am': 'የሙከራ ክ/ከተማ',
                    'code': 'TSW001'
                }
            )
            
            if created:
                print(f"   📋 Created test subcity: {subcity.name}")
            
            # Create test kebeles with different workflows
            kebele_data = [
                {
                    'name': 'Test Kebele Centralized',
                    'workflow_type': 'centralized'
                },
                {
                    'name': 'Test Kebele Autonomous',
                    'workflow_type': 'autonomous'
                }
            ]
            
            for data in kebele_data:
                # Create tenant first
                tenant, created = Tenant.objects.get_or_create(
                    name=data['name'],
                    defaults={
                        'type': 'kebele',
                        'schema_name': data['name'].lower().replace(' ', '_'),
                        'parent': subcity.tenant if hasattr(subcity, 'tenant') else None
                    }
                )
                
                if created:
                    print(f"   📋 Created tenant: {tenant.name}")
                
                # Create kebele
                kebele, created = Kebele.objects.get_or_create(
                    name=data['name'],
                    defaults={
                        'sub_city': subcity,
                        'tenant': tenant,
                        'code': f"TK{data['name'][-3:]}"
                    }
                )
                
                if created:
                    print(f"   📋 Created kebele: {kebele.name}")
                
                # Set workflow configuration
                workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                    tenant=tenant,
                    defaults={'workflow_type': data['workflow_type']}
                )
                
                if created or workflow_config.workflow_type != data['workflow_type']:
                    workflow_config.workflow_type = data['workflow_type']
                    workflow_config.save()
                    print(f"   📋 Set workflow type: {data['workflow_type']}")
            
            self.log_test("Setup test data", True, "Test kebeles and workflows created")
            
        except Exception as e:
            self.log_test("Setup test data", False, str(e))

    def test_rbac_setup(self):
        """Test RBAC system setup."""
        print("\n🔧 Testing RBAC setup...")
        
        try:
            # Run the RBAC setup command
            from django.core.management import call_command
            call_command('setup_workflow_rbac', '--force')
            
            # Verify groups were created
            kebele_groups = Group.objects.filter(name__contains='test_kebele')
            
            if kebele_groups.exists():
                self.log_test("RBAC setup", True, f"Created {kebele_groups.count()} kebele groups")
            else:
                self.log_test("RBAC setup", False, "No kebele groups found")
                
        except Exception as e:
            self.log_test("RBAC setup", False, str(e))

    def test_workflow_switching_api(self):
        """Test workflow switching via API."""
        print("\n🔧 Testing workflow switching API...")
        
        try:
            # Get a test kebele
            kebele = Kebele.objects.filter(name__contains='Test Kebele').first()
            if not kebele:
                self.log_test("Workflow switching API", False, "No test kebele found")
                return
            
            # Get current workflow
            workflow_config = TenantWorkflowConfig.objects.get(tenant=kebele.tenant)
            current_workflow = workflow_config.workflow_type
            new_workflow = 'autonomous' if current_workflow == 'centralized' else 'centralized'
            
            print(f"   📋 Switching {kebele.name} from {current_workflow} to {new_workflow}")
            
            # Test the workflow switching logic
            from users.services.workflow_group_manager import WorkflowGroupManager
            
            manager = WorkflowGroupManager(kebele.tenant)
            result = manager.switch_workflow(
                new_workflow_type=new_workflow,
                reason="Test workflow switching"
            )
            
            if result.get('success'):
                self.log_test("Workflow switching API", True, f"Successfully switched to {new_workflow}")
            else:
                self.log_test("Workflow switching API", False, result.get('message', 'Unknown error'))
                
        except Exception as e:
            self.log_test("Workflow switching API", False, str(e))

    def test_permission_assignments(self):
        """Test that permissions are correctly assigned based on workflow type."""
        print("\n🔧 Testing permission assignments...")
        
        try:
            # Get kebeles with different workflows
            kebeles = Kebele.objects.filter(name__contains='Test Kebele')
            
            for kebele in kebeles:
                workflow_config = TenantWorkflowConfig.objects.get(tenant=kebele.tenant)
                workflow_type = workflow_config.workflow_type
                
                # Check if appropriate groups exist
                schema_name = kebele.tenant.schema_name
                expected_groups = [f"{schema_name}_clerk", f"{schema_name}_kebele_leader"]
                
                if workflow_type == 'autonomous':
                    expected_groups.append(f"{schema_name}_print_id_cards")
                
                missing_groups = []
                for group_name in expected_groups:
                    if not Group.objects.filter(name=group_name).exists():
                        missing_groups.append(group_name)
                
                if missing_groups:
                    self.log_test(
                        f"Permission assignments - {kebele.name}", 
                        False, 
                        f"Missing groups: {missing_groups}"
                    )
                else:
                    self.log_test(
                        f"Permission assignments - {kebele.name}", 
                        True, 
                        f"All required groups exist for {workflow_type} workflow"
                    )
                    
        except Exception as e:
            self.log_test("Permission assignments", False, str(e))

    def test_role_availability(self):
        """Test that roles are available based on workflow type."""
        print("\n🔧 Testing role availability...")
        
        try:
            kebeles = Kebele.objects.filter(name__contains='Test Kebele')
            
            for kebele in kebeles:
                workflow_config = TenantWorkflowConfig.objects.get(tenant=kebele.tenant)
                workflow_type = workflow_config.workflow_type
                
                # Test role availability logic
                available_roles = self.get_available_roles_for_kebele(kebele, workflow_type)
                
                expected_roles = ['clerk', 'kebele_leader']
                if workflow_type == 'autonomous':
                    expected_roles.append('print_id_cards')
                
                missing_roles = [role for role in expected_roles if role not in available_roles]
                
                if missing_roles:
                    self.log_test(
                        f"Role availability - {kebele.name}", 
                        False, 
                        f"Missing roles: {missing_roles}"
                    )
                else:
                    self.log_test(
                        f"Role availability - {kebele.name}", 
                        True, 
                        f"All expected roles available for {workflow_type} workflow"
                    )
                    
        except Exception as e:
            self.log_test("Role availability", False, str(e))

    def get_available_roles_for_kebele(self, kebele, workflow_type):
        """Get available roles for a kebele based on workflow type."""
        base_roles = ['clerk', 'kebele_leader']
        
        if workflow_type == 'autonomous':
            base_roles.append('print_id_cards')
        
        return base_roles

    def generate_report(self):
        """Generate test report."""
        print("\n" + "="*60)
        print("📊 WORKFLOW SWITCHING TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['test']}: {result['message']}")
        
        print("\n📋 Detailed Results:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['test']}")
            if result['message']:
                print(f"      📝 {result['message']}")

    def run_all_tests(self):
        """Run all workflow switching tests."""
        print("🚀 Starting Workflow Switching Tests")
        print("="*60)
        
        self.setup_test_data()
        self.test_rbac_setup()
        self.test_workflow_switching_api()
        self.test_permission_assignments()
        self.test_role_availability()
        
        self.generate_report()

if __name__ == "__main__":
    tester = WorkflowSwitchingTester()
    tester.run_all_tests()
