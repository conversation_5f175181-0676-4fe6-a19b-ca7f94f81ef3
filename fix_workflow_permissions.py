#!/usr/bin/env python3
"""
Fix workflow management permissions for subcity and city admins.

This script ensures that subcity and city admins have the necessary permissions
to manage workflows for their child tenants.
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from tenants.models import Tenant

User = get_user_model()

def create_workflow_permissions():
    """Create workflow management permissions if they don't exist."""
    print("🔧 Creating workflow management permissions...")
    
    user_ct = ContentType.objects.get_for_model(User)
    
    permissions_to_create = [
        ('manage_workflows', 'Can manage and switch workflow types'),
        ('view_workflows', 'Can view workflow configurations'),
        ('switch_workflows', 'Can switch between workflow types'),
    ]
    
    created_count = 0
    for codename, name in permissions_to_create:
        permission, created = Permission.objects.get_or_create(
            codename=codename,
            defaults={
                'name': name,
                'content_type': user_ct
            }
        )
        
        if created:
            print(f"  ✅ Created permission: {codename}")
            created_count += 1
        else:
            print(f"  ⏭️  Permission already exists: {codename}")
    
    return created_count

def grant_workflow_permissions_to_admins():
    """Grant workflow permissions to subcity and city admins."""
    print("\n🔧 Granting workflow permissions to admins...")
    
    # Get workflow permissions
    workflow_permissions = Permission.objects.filter(
        codename__in=['manage_workflows', 'view_workflows', 'switch_workflows']
    )
    
    if not workflow_permissions.exists():
        print("❌ No workflow permissions found. Creating them first...")
        create_workflow_permissions()
        workflow_permissions = Permission.objects.filter(
            codename__in=['manage_workflows', 'view_workflows', 'switch_workflows']
        )
    
    # Grant to subcity admins
    subcity_admins = User.objects.filter(role='subcity_admin')
    subcity_count = 0
    
    for admin in subcity_admins:
        for permission in workflow_permissions:
            admin.user_permissions.add(permission)
        subcity_count += 1
        print(f"  ✅ Granted workflow permissions to subcity admin: {admin.email}")
    
    # Grant to city admins
    city_admins = User.objects.filter(role='city_admin')
    city_count = 0
    
    for admin in city_admins:
        for permission in workflow_permissions:
            admin.user_permissions.add(permission)
        city_count += 1
        print(f"  ✅ Granted workflow permissions to city admin: {admin.email}")
    
    return subcity_count, city_count

def create_workflow_groups():
    """Create workflow management groups for different tenant types."""
    print("\n🔧 Creating workflow management groups...")
    
    workflow_permissions = Permission.objects.filter(
        codename__in=['manage_workflows', 'view_workflows', 'switch_workflows']
    )
    
    groups_to_create = [
        ('subcity_workflow_managers', 'Subcity Workflow Managers'),
        ('city_workflow_managers', 'City Workflow Managers'),
    ]
    
    created_count = 0
    for group_name, group_display_name in groups_to_create:
        group, created = Group.objects.get_or_create(
            name=group_name,
            defaults={'name': group_name}
        )
        
        if created:
            print(f"  ✅ Created group: {group_name}")
            created_count += 1
        else:
            print(f"  ⏭️  Group already exists: {group_name}")
        
        # Add workflow permissions to group
        for permission in workflow_permissions:
            group.permissions.add(permission)
        
        print(f"    📋 Added {workflow_permissions.count()} permissions to {group_name}")
    
    return created_count

def assign_users_to_workflow_groups():
    """Assign subcity and city admins to workflow management groups."""
    print("\n🔧 Assigning users to workflow groups...")
    
    # Get groups
    subcity_group = Group.objects.filter(name='subcity_workflow_managers').first()
    city_group = Group.objects.filter(name='city_workflow_managers').first()
    
    if not subcity_group or not city_group:
        print("❌ Workflow groups not found. Creating them first...")
        create_workflow_groups()
        subcity_group = Group.objects.get(name='subcity_workflow_managers')
        city_group = Group.objects.get(name='city_workflow_managers')
    
    # Assign subcity admins
    subcity_admins = User.objects.filter(role='subcity_admin')
    subcity_count = 0
    
    for admin in subcity_admins:
        admin.groups.add(subcity_group)
        subcity_count += 1
        print(f"  ✅ Added subcity admin to workflow group: {admin.email}")
    
    # Assign city admins
    city_admins = User.objects.filter(role='city_admin')
    city_count = 0
    
    for admin in city_admins:
        admin.groups.add(city_group)
        city_count += 1
        print(f"  ✅ Added city admin to workflow group: {admin.email}")
    
    return subcity_count, city_count

def test_workflow_permissions():
    """Test workflow permissions for a sample user."""
    print("\n🧪 Testing workflow permissions...")
    
    # Find a subcity admin to test
    subcity_admin = User.objects.filter(role='subcity_admin').first()
    
    if not subcity_admin:
        print("❌ No subcity admin found for testing")
        return
    
    print(f"Testing permissions for: {subcity_admin.email}")
    
    # Test permissions
    permissions_to_test = [
        'manage_workflows',
        'view_workflows', 
        'switch_workflows'
    ]
    
    for perm_code in permissions_to_test:
        has_perm = subcity_admin.has_perm(f'auth.{perm_code}') or \
                  subcity_admin.user_permissions.filter(codename=perm_code).exists() or \
                  subcity_admin.groups.filter(permissions__codename=perm_code).exists()
        
        print(f"  {perm_code}: {'✅ YES' if has_perm else '❌ NO'}")
    
    # Test workflow management for child kebeles
    if hasattr(subcity_admin, 'tenant') and subcity_admin.tenant:
        child_kebeles = Tenant.objects.filter(
            type='kebele', 
            parent=subcity_admin.tenant
        )
        
        print(f"\n  Child kebeles that can be managed:")
        for kebele in child_kebeles:
            print(f"    📋 {kebele.name} (ID: {kebele.id})")

def main():
    print("🚀 Fixing Workflow Management Permissions")
    print("="*50)
    
    try:
        with transaction.atomic():
            # Step 1: Create permissions
            perm_count = create_workflow_permissions()
            
            # Step 2: Grant permissions to admins
            subcity_count, city_count = grant_workflow_permissions_to_admins()
            
            # Step 3: Create workflow groups
            group_count = create_workflow_groups()
            
            # Step 4: Assign users to groups
            subcity_group_count, city_group_count = assign_users_to_workflow_groups()
            
            # Step 5: Test permissions
            test_workflow_permissions()
            
            print("\n" + "="*50)
            print("✅ Workflow Permission Fix Completed!")
            print(f"📊 Summary:")
            print(f"  • Created {perm_count} new permissions")
            print(f"  • Updated {subcity_count} subcity admins")
            print(f"  • Updated {city_count} city admins")
            print(f"  • Created {group_count} new groups")
            print(f"  • Assigned {subcity_group_count + city_group_count} users to groups")
            
            print("\n🎯 Next Steps:")
            print("1. Restart the Django server")
            print("2. Test workflow switching in the frontend")
            print("3. Check browser console for permission logs")
            
    except Exception as e:
        print(f"❌ Error fixing workflow permissions: {str(e)}")
        raise

if __name__ == "__main__":
    main()
