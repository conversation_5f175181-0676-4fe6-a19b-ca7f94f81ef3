from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType

User = get_user_model()


class Command(BaseCommand):
    help = 'Fix permissions for existing city_system_admin users'

    def handle(self, *args, **options):
        self.stdout.write('🔧 Fixing city_system_admin user permissions...')
        
        # Find all city_system_admin users
        city_sys_admins = User.objects.filter(role='city_system_admin')
        self.stdout.write(f'Found {city_sys_admins.count()} city_system_admin users to fix')
        
        if city_sys_admins.count() == 0:
            self.stdout.write(self.style.WARNING('No city_system_admin users found'))
            return
        
        # Create or get the city_system_admin group
        group, created = Group.objects.get_or_create(name='city_system_admin')
        if created:
            self.stdout.write(self.style.SUCCESS('✅ Created city_system_admin group'))
        else:
            self.stdout.write('ℹ️ city_system_admin group already exists')
        
        # Define required permissions
        required_perms = ['create_subcity_users', 'view_user_management', 'navigate_to_dashboard']
        content_type = ContentType.objects.get_for_model(User)
        
        # Add permissions to group
        for perm_codename in required_perms:
            try:
                perm = Permission.objects.get(codename=perm_codename)
                group.permissions.add(perm)
                self.stdout.write(f'  ✅ Added permission to group: {perm_codename}')
            except Permission.DoesNotExist:
                # Create permission if it doesn't exist
                perm = Permission.objects.create(
                    codename=perm_codename,
                    name=f'Can {perm_codename.replace("_", " ")}',
                    content_type=content_type
                )
                group.permissions.add(perm)
                self.stdout.write(f'  ✅ Created and added permission: {perm_codename}')
        
        # Fix each user
        for user in city_sys_admins:
            self.stdout.write(f'\nFixing user: {user.email}')
            
            # Add user to group
            user.groups.add(group)
            self.stdout.write(f'  ✅ Added {user.email} to city_system_admin group')
            
            # Verify permissions
            all_perms = []
            for group in user.groups.all():
                group_perms = list(group.permissions.all().values_list('codename', flat=True))
                all_perms.extend(group_perms)
            
            user_perms = list(user.user_permissions.all().values_list('codename', flat=True))
            all_perms.extend(user_perms)
            all_perms = list(set(all_perms))
            
            self.stdout.write(f'  📋 User now has permissions: {all_perms}')
            self.stdout.write(f'  ✅ Has create_subcity_users: {"create_subcity_users" in all_perms}')
        
        self.stdout.write(self.style.SUCCESS(f'\n✅ Fixed permissions for {city_sys_admins.count()} city_system_admin users!'))
