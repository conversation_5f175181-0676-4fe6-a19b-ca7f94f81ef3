# 🔄 Kebele Workflow Switching System

## Overview

The Kebele Workflow Switching System allows kebeles to dynamically switch between **Centralized** and **Autonomous** workflows for ID card processing. This provides flexibility for different administrative needs and capabilities.

## 🎯 Workflow Types

### 1. Centralized Workflow (Default)
**Flow**: Clerk → Kebele Leader → Subcity Admin → Printing

**Characteristics**:
- ID cards require subcity approval before printing
- Centralized quality control and security management
- print_id_cards role managed by subcity administration
- Additional approval step ensures consistency

**Roles & Permissions**:

#### Clerk (Kebele Level)
- `register_citizens`
- `view_citizens_list`
- `view_citizen_details`
- `generate_id_cards`
- `view_id_cards_list`
- `view_kebele_dashboard`
- `send_id_card_for_approval`

#### Kebele Leader (Kebele Level)
- `view_own_kebele_data`
- `view_kebele_dashboard`
- `view_kebele_reports`
- `view_citizens_list`
- `view_citizen_details`
- `view_id_cards_list`
- `approve_id_cards`
- `verify_documents`
- `create_transfers`
- `approve_transfer_requests`
- `create_clearances`
- `view_clearances`

#### Subcity Admin (Subcity Level)
- `view_child_kebeles_data`
- `view_subcity_dashboard`
- `view_subcity_reports`
- `create_kebele_users`

#### Print ID Cards (Subcity Level)
- `print_id_cards`

### 2. Autonomous Workflow
**Flow**: Clerk → Kebele Leader → Printing

**Characteristics**:
- Kebele handles full ID card process locally
- Faster processing time for citizens
- Kebele leader applies security patterns and signatures
- Local printing capability with designated printer

**Roles & Permissions**:

#### Clerk (Kebele Level)
- Same as centralized workflow

#### Kebele Leader (Kebele Level)
- Same as centralized workflow
- **Additional**: Security pattern and signature application

#### Print ID Cards (Kebele Level)
- `print_id_cards` (Available for kebele users)

## 🔧 System Architecture

### Backend Components

1. **Workflow Templates** (`backend/tenants/models_workflow.py`)
   - Predefined permission sets for each workflow type
   - Role definitions for different tenant levels

2. **Workflow Group Manager** (`backend/users/services/workflow_group_manager.py`)
   - Handles workflow switching logic
   - Updates user group assignments
   - Manages permission transitions

3. **API Endpoints** (`backend/tenants/urls.py`)
   - `/api/tenants/{id}/workflow/switch/` - Switch workflow
   - `/api/tenants/{id}/workflow/current/` - Get current workflow
   - `/api/tenants/{id}/workflow/available/` - Get available workflows

4. **Management Commands**
   - `setup_workflow_rbac` - Set up comprehensive RBAC system
   - `switch_workflow` - Command-line workflow switching

### Frontend Components

1. **Workflow Management Interface** (`frontend/src/pages/users/KebeleUserManagement.jsx`)
   - Visual workflow status display
   - Workflow switching dialog
   - Impact analysis and comparison

2. **Workflow Switcher Component** (`frontend/src/components/WorkflowSwitcher.jsx`)
   - Reusable workflow switching component
   - Status monitoring and management

## 🚀 Setup Instructions

### 1. Initialize RBAC System

```bash
# Set up comprehensive RBAC for all tenants
python manage.py setup_workflow_rbac

# Set up RBAC for specific tenant with autonomous workflow
python manage.py setup_workflow_rbac --tenant-id 123 --workflow-type autonomous

# Force recreation of existing groups
python manage.py setup_workflow_rbac --force
```

### 2. Switch Workflow via Command Line

```bash
# Switch kebele to autonomous workflow
python manage.py switch_workflow --tenant-name "Kebele Name" --workflow-type autonomous --reason "Local processing capability"

# Switch back to centralized
python manage.py switch_workflow --tenant-id 123 --workflow-type centralized --reason "Quality control requirements"
```

### 3. Frontend Usage

1. **Access Workflow Management**:
   - Navigate to Kebele User Management page
   - Subcity admins can see workflow management section
   - Each kebele shows current workflow status

2. **Switch Workflow**:
   - Click "Switch Workflow" button on kebele card
   - Review workflow comparison and impact
   - Provide reason for audit purposes
   - Confirm the switch

## 📋 Role Assignments by Tenant Type

### Kebele Tenants
- **clerk**: Basic citizen registration and ID card generation
- **kebele_leader**: Approval authority and local management
- **print_id_cards**: Available only in autonomous workflow

### Subcity Tenants
- **subcity_admin**: Manage child kebeles and users
- **subcity_system_admin**: User management for child kebeles
- **print_id_cards**: Centralized printing management

### City Tenants
- **city_admin**: Manage child subcities and overall administration
- **city_system_admin**: User management for child subcities

## 🔍 Workflow Switching Process

### 1. Pre-Switch Validation
- Verify user permissions (subcity admin or superuser)
- Validate target workflow type
- Check tenant type compatibility (autonomous only for kebeles)

### 2. Workflow Transition
- Update `TenantWorkflowConfig` record
- Create workflow-specific groups if needed
- Update user group assignments
- Log workflow change for audit

### 3. Post-Switch Updates
- Refresh user permissions
- Update UI to reflect new workflow
- Notify affected users (optional)

## 🧪 Testing

### Run Comprehensive Tests

```bash
# Run workflow switching tests
python test_workflow_switching.py
```

### Manual Testing Checklist

1. **Setup Verification**:
   - [ ] RBAC groups created for all tenant types
   - [ ] Permissions correctly assigned to groups
   - [ ] Workflow configurations exist for kebeles

2. **Switching Functionality**:
   - [ ] Can switch from centralized to autonomous
   - [ ] Can switch from autonomous to centralized
   - [ ] Proper validation and error handling
   - [ ] Audit logging works correctly

3. **Permission Verification**:
   - [ ] print_id_cards role available in autonomous kebeles
   - [ ] print_id_cards role restricted in centralized kebeles
   - [ ] User permissions update correctly after switch
   - [ ] Navigation menus reflect workflow changes

## 🔒 Security Considerations

1. **Access Control**:
   - Only subcity admins and superusers can switch workflows
   - Audit logging for all workflow changes
   - Reason required for all switches

2. **Permission Isolation**:
   - Roles restricted to appropriate tenant levels
   - print_id_cards role availability based on workflow
   - Proper group membership management

3. **Data Integrity**:
   - Atomic transactions for workflow switches
   - Rollback capability in case of errors
   - Validation of all permission assignments

## 📊 Monitoring and Audit

### Workflow Status Monitoring
- Dashboard showing workflow status for all kebeles
- Historical workflow change tracking
- Performance metrics for different workflows

### Audit Trail
- All workflow switches logged with timestamp
- User who initiated the switch
- Reason for the switch
- Before/after permission states

## 🔧 Troubleshooting

### Common Issues

1. **Missing Groups**: Run `setup_workflow_rbac --force`
2. **Permission Errors**: Verify user has subcity admin role
3. **UI Not Updating**: Clear browser cache and refresh
4. **API Errors**: Check backend logs for detailed error messages

### Debug Commands

```bash
# Check workflow configuration
python manage.py shell -c "from tenants.models import TenantWorkflowConfig; print(TenantWorkflowConfig.objects.all().values())"

# Verify group assignments
python manage.py shell -c "from django.contrib.auth.models import Group; print(Group.objects.filter(name__contains='kebele').values())"
```

## 🎯 Future Enhancements

1. **Hybrid Workflow**: Combination of local and centralized processing
2. **Batch Switching**: Switch multiple kebeles simultaneously
3. **Scheduled Switching**: Automatic workflow changes based on schedules
4. **Advanced Analytics**: Detailed workflow performance analysis
5. **Mobile Support**: Workflow management from mobile devices
