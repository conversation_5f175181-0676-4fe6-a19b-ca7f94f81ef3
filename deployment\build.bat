@echo off
REM GoID Build Script for Windows - Build and push Docker images

setlocal enabledelayedexpansion

REM Default values
set SERVICE=all
set VERSION_TYPE=patch
set PUSH=true
set DOCKER_HUB_USERNAME=aragawmebratu
set VERSION=

REM Parse command line arguments
:parse_args
if "%~1"=="" goto end_parse
if "%~1"=="-s" (
    set SERVICE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--service" (
    set SERVICE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-v" (
    set VERSION=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--version" (
    set VERSION=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-t" (
    set VERSION_TYPE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--type" (
    set VERSION_TYPE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-u" (
    set DOCKER_HUB_USERNAME=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--username" (
    set DOCKER_HUB_USERNAME=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--no-push" (
    set PUSH=false
    shift
    goto parse_args
)
if "%~1"=="--list" (
    python deployment\build-and-push.py --list
    exit /b 0
)
if "%~1"=="-h" goto show_usage
if "%~1"=="--help" goto show_usage

echo [ERROR] Unknown option: %~1
goto show_usage

:end_parse

REM Check if Docker Hub username is provided
if "%PUSH%"=="true" if "%DOCKER_HUB_USERNAME%"=="" (
    echo [ERROR] Docker Hub username is required for pushing images
    echo [WARNING] Use --username option or --no-push to build without pushing
    exit /b 1
)

REM Update versions.json with Docker Hub username
if not "%DOCKER_HUB_USERNAME%"=="" (
    echo [INFO] Updating Docker Hub repositories in configuration...
    python -c "import json; config = json.load(open('deployment/versions.json', 'r')); config['services']['backend']['docker_hub_repo'] = '%DOCKER_HUB_USERNAME%/goid-production'; config['services']['frontend']['docker_hub_repo'] = '%DOCKER_HUB_USERNAME%/goid-production'; json.dump(config, open('deployment/versions.json', 'w'), indent=2)"
)

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    exit /b 1
)

REM Login to Docker Hub if pushing
if "%PUSH%"=="true" (
    echo [INFO] Logging in to Docker Hub...
    docker login
    if errorlevel 1 (
        echo [ERROR] Failed to login to Docker Hub
        exit /b 1
    )
)

REM Build command
set BUILD_CMD=python deployment\build-and-push.py --service %SERVICE% --version-type %VERSION_TYPE%

if not "%VERSION%"=="" (
    set BUILD_CMD=!BUILD_CMD! --version %VERSION%
)

if "%PUSH%"=="false" (
    set BUILD_CMD=!BUILD_CMD! --no-push
)

echo [INFO] Starting build process...
echo [INFO] Service: %SERVICE%
echo [INFO] Version type: %VERSION_TYPE%
echo [INFO] Push to Docker Hub: %PUSH%

REM Execute build
%BUILD_CMD%
if errorlevel 1 (
    echo [ERROR] Build failed!
    exit /b 1
)

echo [SUCCESS] Build completed successfully!

if "%PUSH%"=="true" (
    echo [SUCCESS] Images pushed to Docker Hub
    echo [INFO] You can now deploy using: deployment\deploy.bat
) else (
    echo [WARNING] Images built locally only (not pushed to Docker Hub)
)

exit /b 0

:show_usage
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   -s, --service SERVICE     Service to build (backend^|frontend^|biometric-service^|all) [default: all]
echo   -v, --version VERSION     Specific version to use (otherwise auto-increment)
echo   -t, --type TYPE          Version increment type (major^|minor^|patch) [default: patch]
echo   -u, --username USERNAME  Docker Hub username
echo   --no-push               Build only, don't push to Docker Hub
echo   --list                  List current versions
echo   -h, --help              Show this help message
echo.
echo Examples:
echo   %0 --username myuser                    # Build all services with patch increment
echo   %0 -s backend -t minor --username myuser # Build backend with minor version increment
echo   %0 --list                               # List current versions
echo   %0 --no-push --username myuser          # Build without pushing to Docker Hub
exit /b 0
