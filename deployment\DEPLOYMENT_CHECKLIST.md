# GoID Production Deployment Checklist

Use this checklist to ensure a successful production deployment.

## Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Docker installed and running
- [ ] Docker Hub account created
- [ ] Git repository up to date
- [ ] Production server accessible
- [ ] Domain name configured (if applicable)
- [ ] SSL certificates ready (if using HTTPS)

### 2. Configuration Review
- [ ] Update `deployment/versions.json` with correct Docker Hub repositories
- [ ] Create and configure `deployment/.env.production`
- [ ] Set strong `SECRET_KEY`
- [ ] Configure secure `DB_PASSWORD`
- [ ] Set correct `ALLOWED_HOSTS`
- [ ] Configure `CORS_ALLOWED_ORIGINS`
- [ ] Review resource limits in docker-compose template

### 3. Security Checklist
- [ ] Change all default passwords
- [ ] Configure firewall rules
- [ ] Set up SSL/TLS certificates
- [ ] Review and restrict database access
- [ ] Configure backup strategy
- [ ] Set up monitoring and alerting

## Deployment Process

### Option 1: Complete Automated Deployment

```bash
# Linux/Mac
./deployment/production-deploy.sh --username your-dockerhub-username

# Windows
deployment\production-deploy.bat --username your-dockerhub-username
```

### Option 2: Step-by-Step Deployment

#### Step 1: Build and Push Images
```bash
# Linux/Mac
./deployment/build.sh --username your-dockerhub-username

# Windows
deployment\build.bat --username your-dockerhub-username
```

#### Step 2: Generate Environment Configuration
```bash
# Linux/Mac
./deployment/deploy.sh generate

# Windows
deployment\deploy.bat generate
```

#### Step 3: Edit Environment File
Edit `deployment/.env.production` with your production settings.

#### Step 4: Deploy Services
```bash
# Linux/Mac
./deployment/deploy.sh deploy

# Windows
deployment\deploy.bat deploy
```

## Post-Deployment Checklist

### 1. Service Verification
- [ ] All services are running: `./deployment/deploy.sh status`
- [ ] Backend health check: `curl http://localhost:8000/api/health/`
- [ ] Frontend accessible: `curl http://localhost:3000/`
- [ ] Database connectivity working
- [ ] Redis cache working

### 2. Application Testing
- [ ] User login/logout works
- [ ] Tenant creation works
- [ ] Citizen registration works
- [ ] ID card generation works
- [ ] Biometric capture works (if enabled)
- [ ] File uploads work
- [ ] API endpoints respond correctly

### 3. Performance Verification
- [ ] Response times acceptable
- [ ] Memory usage within limits
- [ ] CPU usage normal
- [ ] Disk space sufficient
- [ ] Database performance good

### 4. Security Verification
- [ ] HTTPS working (if configured)
- [ ] Authentication required for protected endpoints
- [ ] File permissions correct
- [ ] Database access restricted
- [ ] No sensitive data in logs

### 5. Backup and Monitoring
- [ ] Database backup working
- [ ] Media files backup configured
- [ ] Log rotation configured
- [ ] Monitoring alerts configured
- [ ] Health checks working

## Troubleshooting Common Issues

### Services Won't Start
1. Check logs: `./deployment/deploy.sh logs`
2. Verify environment configuration
3. Check Docker Hub image availability
4. Verify network connectivity

### Database Connection Issues
1. Check database credentials in `.env.production`
2. Verify database service is running
3. Check network connectivity between services
4. Review database logs

### Image Pull Failures
1. Verify Docker Hub credentials
2. Check image names and tags
3. Ensure images were pushed successfully
4. Check network connectivity to Docker Hub

### Performance Issues
1. Check resource limits in docker-compose
2. Monitor CPU and memory usage
3. Review database performance
4. Check for memory leaks

## Maintenance Commands

### View Service Status
```bash
./deployment/deploy.sh status
```

### View Service Logs
```bash
# All services
./deployment/deploy.sh logs

# Specific service
./deployment/deploy.sh logs --service backend --follow
```

### Update Services
```bash
# Build new versions
./deployment/build.sh --username your-dockerhub-username

# Deploy updates
./deployment/deploy.sh deploy
```

### Backup Database
```bash
docker-compose -f deployment/docker-compose.production.yml exec db pg_dump -U goid_user goid_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

### Scale Services
```bash
docker-compose -f deployment/docker-compose.production.yml up -d --scale backend=2
```

### Stop Services
```bash
./deployment/deploy.sh stop
```

## Emergency Procedures

### Rollback to Previous Version
1. Update version in `deployment/versions.json`
2. Regenerate docker-compose: `./deployment/deploy.sh generate`
3. Deploy: `./deployment/deploy.sh deploy`

### Service Recovery
1. Check service status: `./deployment/deploy.sh status`
2. Restart specific service: `docker-compose restart <service-name>`
3. Check logs for errors: `./deployment/deploy.sh logs --service <service-name>`

### Database Recovery
1. Stop all services: `./deployment/deploy.sh stop`
2. Restore database from backup
3. Start services: `./deployment/deploy.sh deploy`

## Support Contacts

- **Technical Issues**: Check logs and GitHub issues
- **Security Issues**: Follow security incident response plan
- **Infrastructure Issues**: Contact system administrator

## Version History

Track deployment versions and changes:

| Date | Version | Services | Changes | Deployed By |
|------|---------|----------|---------|-------------|
| YYYY-MM-DD | 1.0.0 | All | Initial deployment | Name |

## Notes

Add any deployment-specific notes or customizations here.
