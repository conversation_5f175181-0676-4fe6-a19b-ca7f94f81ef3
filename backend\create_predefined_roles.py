#!/usr/bin/env python
"""
Simple script to create predefined roles for the RBAC system
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group, Permission
from users.models_groups import TenantGroup
from django_tenants.utils import schema_context, get_public_schema_name

def create_predefined_roles():
    """Create predefined roles if they don't exist"""
    print("🏗️ Creating predefined roles...")
    
    # Define roles with their permissions and allowed tenant types
    roles_config = {
        'clerk': {
            'description': 'Clerk role for kebele level tenants',
            'tenant_types': ['kebele'],
            'level': 10,
            'group_type': 'operational'
        },
        'kebele_leader': {
            'description': 'Kebele leader role for kebele level tenants',
            'tenant_types': ['kebele'],
            'level': 20,
            'group_type': 'administrative'
        },
        'subcity_admin': {
            'description': 'Subcity admin role for subcity level tenants',
            'tenant_types': ['subcity'],
            'level': 30,
            'group_type': 'administrative'
        },
        'subcity_system_admin': {
            'description': 'Subcity system admin role for managing kebele users',
            'tenant_types': ['subcity'],
            'level': 35,
            'group_type': 'administrative'
        },
        'city_admin': {
            'description': 'City admin role for city level tenants',
            'tenant_types': ['city'],
            'level': 40,
            'group_type': 'administrative'
        },
        'city_system_admin': {
            'description': 'City system admin role for managing subcity users',
            'tenant_types': ['city'],
            'level': 45,
            'group_type': 'administrative'
        },
        'print_id_cards': {
            'description': 'ID card printing role for autonomous kebeles or centralized subcities',
            'tenant_types': ['kebele', 'subcity'],
            'level': 15,
            'group_type': 'operational'
        }
    }
    
    created_count = 0
    with schema_context(get_public_schema_name()):
        for role_name, config in roles_config.items():
            # Create Django group
            group, created = Group.objects.get_or_create(name=role_name)
            
            if created:
                print(f"  ✅ Created role group: {role_name}")
                created_count += 1
            else:
                print(f"  ℹ️ Role group already exists: {role_name}")
            
            # Create or update TenantGroup
            tenant_group, tg_created = TenantGroup.objects.get_or_create(
                group=group,
                defaults={
                    'name': role_name,
                    'description': config['description'],
                    'group_type': config['group_type'],
                    'level': config['level'],
                    'allowed_tenant_types': config['tenant_types'],
                    'is_active': True,
                    'tenant': None  # Global role
                }
            )
            
            if not tg_created:
                # Update existing TenantGroup
                tenant_group.allowed_tenant_types = config['tenant_types']
                tenant_group.level = config['level']
                tenant_group.description = config['description']
                tenant_group.group_type = config['group_type']
                tenant_group.save()
                print(f'    🔄 Updated TenantGroup for {role_name}')
            else:
                print(f'    ✅ Created TenantGroup for {role_name}')
    
    print(f"📊 Created/Updated {created_count} role groups")

def check_existing_roles():
    """Check what predefined roles already exist"""
    print("🔍 Checking existing predefined roles...")
    
    with schema_context(get_public_schema_name()):
        # Check for predefined role groups
        predefined_roles = ['clerk', 'kebele_leader', 'subcity_admin', 'subcity_system_admin', 'city_admin', 'city_system_admin', 'print_id_cards']
        
        for role_name in predefined_roles:
            try:
                group = Group.objects.get(name=role_name)
                tenant_group = TenantGroup.objects.get(group=group)
                print(f"✅ {role_name}: Level {tenant_group.level}, Types: {tenant_group.allowed_tenant_types}")
            except Group.DoesNotExist:
                print(f"❌ {role_name}: Group not found")
            except TenantGroup.DoesNotExist:
                print(f"⚠️ {role_name}: Group exists but no TenantGroup")

if __name__ == '__main__':
    print("🚀 Setting up predefined roles...")
    check_existing_roles()
    create_predefined_roles()
    print("\n🔍 Final check...")
    check_existing_roles()
    print("✅ Done!")
