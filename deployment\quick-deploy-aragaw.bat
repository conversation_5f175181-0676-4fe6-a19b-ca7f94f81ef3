@echo off
REM Quick Deployment Script for aragawmebratu/goid-production
REM This script builds and deploys the GoID system using your Docker Hub repository

setlocal enabledelayedexpansion

REM Configuration
set DOCKER_HUB_USERNAME=aragawmebratu
set VERSION_TYPE=patch
set ENV_FILE=deployment\.env.production

echo ================================
echo GoID Production Deployment for %DOCKER_HUB_USERNAME%
echo ================================

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    exit /b 1
)

REM Step 1: Build and push images
echo ================================
echo Step 1: Building and Pushing Images
echo ================================

echo [INFO] Building backend service...
deployment\build.bat --service backend --username %DOCKER_HUB_USERNAME% --type %VERSION_TYPE%
if errorlevel 1 (
    echo [ERROR] Failed to build backend service
    exit /b 1
)

echo [INFO] Building frontend service...
deployment\build.bat --service frontend --username %DOCKER_HUB_USERNAME% --type %VERSION_TYPE%
if errorlevel 1 (
    echo [ERROR] Failed to build frontend service
    exit /b 1
)

echo [SUCCESS] All images built and pushed successfully!

REM Step 2: Create environment file
echo ================================
echo Step 2: Environment Configuration
echo ================================

if not exist "%ENV_FILE%" (
    echo [INFO] Creating environment file from sample...
    copy deployment\.env.production.sample "%ENV_FILE%"
    
    echo [WARNING] Please edit %ENV_FILE% with your production settings:
    echo [WARNING]   - DB_PASSWORD: Set a secure database password
    echo [WARNING]   - SECRET_KEY: Set a secure Django secret key
    echo [WARNING]   - ALLOWED_HOSTS: Set your domain name
    echo [WARNING]   - CORS_ALLOWED_ORIGINS: Set your frontend URLs
    
    pause
)

REM Step 3: Generate docker-compose file
echo ================================
echo Step 3: Generating Docker Compose Configuration
echo ================================

echo [INFO] Generating docker-compose.production.yml...
deployment\deploy.bat generate --env-file %ENV_FILE%
if errorlevel 1 (
    echo [ERROR] Failed to generate docker-compose file
    exit /b 1
)

REM Step 4: Deploy services
echo ================================
echo Step 4: Deploying Services
echo ================================

echo [INFO] Pulling latest images...
deployment\deploy.bat pull --env-file %ENV_FILE%

echo [INFO] Deploying services...
deployment\deploy.bat deploy --env-file %ENV_FILE%
if errorlevel 1 (
    echo [ERROR] Failed to deploy services
    exit /b 1
)

REM Step 5: Show status
echo ================================
echo Step 5: Deployment Status
echo ================================

echo [INFO] Checking service status...
deployment\deploy.bat status --env-file %ENV_FILE%

echo [SUCCESS] Deployment completed successfully!
echo.
echo Your GoID system is now running:
echo   Frontend: http://10.139.8.162:3000
echo   Frontend: https://goid.uog.edu.et
echo   Backend:  http://10.139.8.162:8000
echo   Database: 10.139.8.162:5432
echo   pgAdmin:  http://10.139.8.162:5050
echo.
echo Next steps:
echo   1. Install biometric service on client machines
echo   2. Configure kebele information
echo   3. Test fingerprint capture functionality
echo.
echo Useful commands:
echo   Check status: deployment\deploy.bat status
echo   View logs:    deployment\deploy.bat logs
echo   Stop services: deployment\deploy.bat stop

echo ================================
echo Deployment Complete
echo ================================
pause
