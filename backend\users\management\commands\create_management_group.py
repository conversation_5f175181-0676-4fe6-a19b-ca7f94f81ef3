"""
Management command to create management groups with navigation permissions.
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant
from users.utils.group_permissions import (
    create_management_group, 
    list_available_permission_sets,
    get_group_navigation_permissions
)


class Command(BaseCommand):
    help = 'Create management groups with navigation permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--group-name',
            type=str,
            help='Name of the group to create',
        )
        parser.add_argument(
            '--permission-set',
            type=str,
            help='Permission set to apply (kebele_management, subcity_management, etc.)',
        )
        parser.add_argument(
            '--tenant-schema',
            type=str,
            help='Tenant schema name (optional)',
        )
        parser.add_argument(
            '--description',
            type=str,
            help='Custom description for the group',
        )
        parser.add_argument(
            '--list-sets',
            action='store_true',
            help='List available permission sets',
        )
        parser.add_argument(
            '--show-permissions',
            type=str,
            help='Show permissions for a specific group',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Management Group Creator'))
        self.stdout.write('=' * 40)

        # Ensure we're in public schema
        with schema_context(get_public_schema_name()):
            if options['list_sets']:
                self.list_permission_sets()
                return
            
            if options['show_permissions']:
                self.show_group_permissions(options['show_permissions'])
                return
            
            if not options['group_name'] or not options['permission_set']:
                self.stdout.write(
                    self.style.ERROR('❌ Both --group-name and --permission-set are required')
                )
                self.stdout.write('Use --list-sets to see available permission sets')
                return
            
            self.create_group(
                options['group_name'],
                options['permission_set'],
                options.get('tenant_schema'),
                options.get('description')
            )

    def list_permission_sets(self):
        """List all available permission sets."""
        self.stdout.write('\n📋 Available Permission Sets:')
        self.stdout.write('-' * 35)
        
        permission_sets = list_available_permission_sets()
        
        for name, info in permission_sets.items():
            self.stdout.write(f'\n🔹 {name}')
            self.stdout.write(f'   Description: {info["description"]}')
            self.stdout.write(f'   Permissions: {info["permission_count"]}')
        
        self.stdout.write(f'\n📊 Total permission sets: {len(permission_sets)}')

    def show_group_permissions(self, group_name):
        """Show permissions for a specific group."""
        self.stdout.write(f'\n🔍 Permissions for group: {group_name}')
        self.stdout.write('-' * 40)
        
        permissions = get_group_navigation_permissions(group_name)
        
        if permissions:
            for perm in sorted(permissions):
                self.stdout.write(f'  ✅ {perm}')
            self.stdout.write(f'\n📊 Total permissions: {len(permissions)}')
        else:
            self.stdout.write('❌ No permissions found or group does not exist')

    def create_group(self, group_name, permission_set, tenant_schema=None, description=None):
        """Create a management group."""
        self.stdout.write(f'\n🔧 Creating Management Group: {group_name}')
        self.stdout.write('-' * 50)
        
        # Get tenant if specified
        tenant = None
        if tenant_schema:
            try:
                tenant = Tenant.objects.get(schema_name=tenant_schema)
                self.stdout.write(f'📍 Tenant: {tenant.name} ({tenant_schema})')
            except Tenant.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Tenant with schema "{tenant_schema}" not found')
                )
                return
        
        # Create the group
        success, result, tenant_group = create_management_group(
            group_name, permission_set, tenant, description
        )
        
        if success:
            self.stdout.write(self.style.SUCCESS(f'✅ Successfully created group: {group_name}'))
            
            if tenant_group:
                self.stdout.write(f'📋 TenantGroup ID: {tenant_group.id}')
                self.stdout.write(f'📝 Description: {tenant_group.description}')
            
            # Show assigned permissions
            permissions = get_group_navigation_permissions(group_name)
            self.stdout.write(f'\n📊 Assigned {len(permissions)} permissions:')
            
            # Group permissions by category
            nav_perms = [p for p in permissions if 'navigate' in p]
            view_perms = [p for p in permissions if 'view' in p and 'navigate' not in p]
            access_perms = [p for p in permissions if 'access' in p]
            manage_perms = [p for p in permissions if 'manage' in p or 'create' in p]
            
            if nav_perms:
                self.stdout.write('\n🧭 Navigation:')
                for perm in sorted(nav_perms):
                    self.stdout.write(f'  - {perm}')
            
            if view_perms:
                self.stdout.write('\n👁️ View Access:')
                for perm in sorted(view_perms)[:5]:  # Show first 5
                    self.stdout.write(f'  - {perm}')
                if len(view_perms) > 5:
                    self.stdout.write(f'  ... and {len(view_perms) - 5} more')
            
            if access_perms:
                self.stdout.write('\n🔑 Menu Access:')
                for perm in sorted(access_perms):
                    self.stdout.write(f'  - {perm}')
            
            if manage_perms:
                self.stdout.write('\n⚙️ Management:')
                for perm in sorted(manage_perms)[:3]:  # Show first 3
                    self.stdout.write(f'  - {perm}')
                if len(manage_perms) > 3:
                    self.stdout.write(f'  ... and {len(manage_perms) - 3} more')
            
        else:
            self.stdout.write(self.style.ERROR(f'❌ Failed to create group: {result}'))


# Example usage in the command help
EXAMPLES = """
Examples:

1. List available permission sets:
   python manage.py create_management_group --list-sets

2. Create a kebele management group:
   python manage.py create_management_group \\
     --group-name "Kebele14_Management" \\
     --permission-set "kebele_management" \\
     --tenant-schema "kebele_kebele14" \\
     --description "Kebele 14 User Management Group"

3. Create a subcity management group:
   python manage.py create_management_group \\
     --group-name "Zoble_Management" \\
     --permission-set "subcity_management" \\
     --tenant-schema "subcity_zoble" \\
     --description "Zoble Subcity Management Group"

4. Create a printer-only group:
   python manage.py create_management_group \\
     --group-name "ID_Card_Printers" \\
     --permission-set "printer_only" \\
     --description "ID Card Printing Group"

5. Show permissions for an existing group:
   python manage.py create_management_group --show-permissions "Printer"
"""
