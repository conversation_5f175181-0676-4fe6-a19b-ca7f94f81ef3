# GoID Production Deployment for aragawmebratu/goid-production

This guide is specifically configured for deploying the GoID system using the `aragawmebratu/goid-production` Docker Hub repository.

## 🚀 Quick Start

### Option 1: One-Command Deployment

```bash
# Windows
deployment\quick-deploy-aragaw.bat

# Linux/Mac
./deployment/quick-deploy-aragaw.sh
```

### Option 2: Manual Step-by-Step

#### 1. Build and Push Images
```bash
# Build backend
./deployment/build.sh --service backend --username aragawmebratu

# Build frontend  
./deployment/build.sh --service frontend --username aragawmebratu
```

#### 2. Configure Environment
```bash
# Copy sample environment file
cp deployment/.env.production.sample deployment/.env.production

# Edit with your settings
nano deployment/.env.production
```

#### 3. Deploy Services
```bash
# Generate docker-compose file
./deployment/deploy.sh generate

# Deploy all services
./deployment/deploy.sh deploy
```

## 📋 Configuration

### Required Environment Variables

Edit `deployment/.env.production`:

```env
# Docker Hub (already configured)
DOCKER_HUB_USERNAME=aragawmebratu

# Database (CHANGE THESE!)
DB_PASSWORD=your-secure-password-here

# Django (CHANGE THESE!)
SECRET_KEY=your-very-long-secret-key-here
ALLOWED_HOSTS=your-domain.com,your-ip-address

# URLs (update for your server)
REACT_APP_API_URL=http://your-server:8000
CORS_ALLOWED_ORIGINS=http://your-server:3000
```

### Docker Hub Images

The system uses these pre-configured images:
- `aragawmebratu/goid-production-backend:latest`
- `aragawmebratu/goid-production-frontend:latest`

## 🏗️ Architecture

### Server Components (Docker)
```
┌─────────────────────────────────────────┐
│            Production Server            │
│                                         │
│  ┌─────────────┐  ┌─────────────────┐  │
│  │  Frontend   │  │    Backend      │  │
│  │   :3000     │  │     :8000       │  │
│  └─────────────┘  └─────────────────┘  │
│                           │             │
│  ┌─────────────┐  ┌─────────────────┐  │
│  │ PostgreSQL  │  │     Redis       │  │
│  │   :5432     │  │     :6379       │  │
│  └─────────────┘  └─────────────────┘  │
└─────────────────────────────────────────┘
```

### Client Components (Each Kebele)
```
┌─────────────────────────────────────────┐
│              Kebele PC                  │
│                                         │
│  ┌─────────────┐  ┌─────────────────┐  │
│  │  Browser    │  │ Biometric Svc   │  │
│  │ Server:3000 │  │ localhost:8002  │  │
│  └─────────────┘  └─────────────────┘  │
│                           │             │
│                  ┌─────────────────┐    │
│                  │  USB Device     │    │
│                  │ Futronic FS88H  │    │
│                  └─────────────────┘    │
└─────────────────────────────────────────┘
```

## 📦 Services

### Backend Service
- **Image**: `aragawmebratu/goid-production-backend:latest`
- **Port**: 8000
- **Features**: Django API, FMatcher, Multi-tenant support

### Frontend Service  
- **Image**: `aragawmebratu/goid-production-frontend:latest`
- **Port**: 3000
- **Features**: React UI, Nginx server

### Database Service
- **Image**: `postgres:15`
- **Port**: 5432
- **Storage**: Persistent volume

### Cache Service
- **Image**: `redis:7-alpine`
- **Port**: 6379
- **Usage**: Sessions, caching

## 🔧 Management Commands

### Service Management
```bash
# Check status
./deployment/deploy.sh status

# View logs
./deployment/deploy.sh logs
./deployment/deploy.sh logs --service backend --follow

# Restart services
./deployment/deploy.sh stop
./deployment/deploy.sh deploy

# Update services
./deployment/build.sh --username aragawmebratu
./deployment/deploy.sh deploy
```

### Database Management
```bash
# Backup database
docker-compose -f deployment/docker-compose.production.yml exec db \
  pg_dump -U goid_user goid_db > backup_$(date +%Y%m%d).sql

# Restore database
docker-compose -f deployment/docker-compose.production.yml exec -T db \
  psql -U goid_user goid_db < backup.sql
```

## 🖥️ Client Installation

### 1. Build Client Installer
```bash
cd deployment/client-biometric
python build_installer.py
```

### 2. Distribute to Kebeles
- Copy `dist/GoID-Biometric-Service-Installer.zip` to each kebele PC
- Extract and run `install.bat`
- Configure server URL during installation

### 3. Client Configuration
Each kebele machine needs:
- Python 3.10+
- Java Runtime Environment
- Futronic FS88H device drivers
- GoID Biometric Service (from installer)

## 🔍 Troubleshooting

### Server Issues
```bash
# Check if services are running
docker ps

# Check service logs
./deployment/deploy.sh logs --service backend

# Check database connectivity
docker-compose exec db psql -U goid_user -d goid_db -c "SELECT 1;"
```

### Client Issues
- Check biometric service in system tray
- Verify device connection via Device Manager
- Test service: `http://localhost:8002/api/health`
- Check service logs in installation directory

### Common Problems

#### Images Not Found
```bash
# Login to Docker Hub
docker login

# Verify images exist
docker pull aragawmebratu/goid-production-backend:latest
docker pull aragawmebratu/goid-production-frontend:latest
```

#### Port Conflicts
```bash
# Check what's using ports
netstat -tulpn | grep :8000
netstat -tulpn | grep :3000

# Stop conflicting services or change ports in .env file
```

#### Database Connection Issues
```bash
# Check database logs
./deployment/deploy.sh logs --service db

# Reset database (WARNING: destroys data)
docker-compose down -v
./deployment/deploy.sh deploy
```

## 📊 Monitoring

### Health Checks
- Backend: `http://your-server:8000/api/health/`
- Frontend: `http://your-server:3000/`
- Database: Check via pgAdmin at `http://your-server:5050`

### Performance
```bash
# Check resource usage
docker stats

# Check disk usage
docker system df

# Clean up unused resources
docker system prune
```

## 🔐 Security

### Production Checklist
- [ ] Change default passwords in `.env.production`
- [ ] Use strong SECRET_KEY (50+ characters)
- [ ] Configure proper ALLOWED_HOSTS
- [ ] Set up SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Regular security updates

### Backup Strategy
- Daily database backups
- Weekly full system backup
- Test restore procedures
- Offsite backup storage

## 📞 Support

### Log Locations
- Server logs: `./deployment/deploy.sh logs`
- Client logs: Installation directory on each PC
- Database logs: pgAdmin or direct container access

### Version Information
- Current backend version: Check `deployment/versions.json`
- Current frontend version: Check `deployment/versions.json`
- Docker images: `docker images | grep aragawmebratu`

For technical support, provide:
- Service version information
- Error logs from both server and client
- Network configuration details
- Steps to reproduce the issue
