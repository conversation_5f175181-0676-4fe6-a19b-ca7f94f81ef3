version: '3.8'

services:
  # Main web application backend
  backend:
    image: aragawmebratu/goid-production:backend-${BACKEND_VERSION:-latest}
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=goid.settings
      - DEBUG=False
      - DATABASE_URL=postgresql://goid_user:${DB_PASSWORD:-goid_password}@db:5432/goid_db
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-************,goid.uog.edu.et,localhost}
      - SECRET_KEY=${SECRET_KEY:-change-this-secret-key-in-production}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://************:3000,https://goid.uog.edu.et}
      # Biometric service configuration - clients connect to server
      - BIOMETRIC_SERVICE_MODE=server
      - BIOMETRIC_CLIENT_TIMEOUT=30
      - <PERSON><PERSON><PERSON>TR<PERSON>_ALLOWED_CLIENTS=${BIOMETRIC_ALLOWED_CLIENTS:-*}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - backend_media:/app/media
      - backend_static:/app/static
      - ./backups:/app/backups
    networks:
      - goid_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Frontend application
  frontend:
    image: aragawmebratu/goid-production:frontend-${FRONTEND_VERSION:-latest}
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
      - REACT_APP_BIOMETRIC_SERVICE_URL=${REACT_APP_BIOMETRIC_SERVICE_URL:-http://localhost:8002}
    depends_on:
      - backend
    networks:
      - goid_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # NOTE: Biometric service runs LOCALLY on each CLIENT MACHINE
  #
  # ARCHITECTURE:
  # - Server (this Docker setup): Runs backend, frontend, database, fpmatcher
  # - Client machines: Run biometric capture service locally on localhost:8002
  # - Frontend on client machine connects to: localhost:8002 (biometric) + server:8000 (backend)
  #
  # CLIENT SETUP (on each kebele machine):
  # 1. Install GoID Biometric Service using the installer package
  # 2. Connect Futronic FS88H device via USB
  # 3. Configure server URL in biometric service settings
  # 4. Service runs automatically and provides localhost:8002 endpoint
  #
  # This ensures USB device access and eliminates network latency for fingerprint capture

  # Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: goid_db
      POSTGRES_USER: goid_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-goid_password}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - goid_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U goid_user -d goid_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - goid_network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    networks:
      - goid_network
    restart: unless-stopped
    profiles:
      - admin
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - goid_network
    restart: unless-stopped
    profiles:
      - proxy
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  backend_media:
    driver: local
  backend_static:
    driver: local
  nginx_logs:
    driver: local

networks:
  goid_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
