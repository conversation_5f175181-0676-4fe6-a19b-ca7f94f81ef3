import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Calendar } from '../ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { CalendarIcon, FileText, FileSpreadsheet, BarChart3, FileJson, Info } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '../../lib/utils';
import reportsService from '../../services/reportsService';

const ReportGenerationModal = ({ isOpen, onClose, onGenerate, templates }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    report_type: '',
    template: '',
    format: 'pdf',
    filters: {},
    parameters: {},
    period_start: null,
    period_end: null
  });
  
  const [reportTypes, setReportTypes] = useState([]);
  const [reportFormats, setReportFormats] = useState([]);
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadReportMetadata();
    }
  }, [isOpen]);

  useEffect(() => {
    // Filter templates based on selected report type
    if (formData.report_type) {
      const filtered = templates.filter(template => 
        template.report_type === formData.report_type
      );
      setFilteredTemplates(filtered);
      
      // Reset template selection if current template doesn't match type
      if (formData.template && !filtered.find(t => t.id === formData.template)) {
        setFormData(prev => ({ ...prev, template: '' }));
        setSelectedTemplate(null);
      }
    } else {
      setFilteredTemplates([]);
      setFormData(prev => ({ ...prev, template: '' }));
      setSelectedTemplate(null);
    }
  }, [formData.report_type, templates]);

  useEffect(() => {
    // Update selected template details
    if (formData.template) {
      const template = templates.find(t => t.id === formData.template);
      setSelectedTemplate(template);
      
      // Auto-fill filters from template defaults
      if (template && template.default_filters) {
        setFormData(prev => ({
          ...prev,
          filters: { ...template.default_filters }
        }));
      }
    } else {
      setSelectedTemplate(null);
    }
  }, [formData.template, templates]);

  const loadReportMetadata = async () => {
    try {
      const [typesData, formatsData] = await Promise.all([
        reportsService.getReportTypes(),
        reportsService.getReportFormats()
      ]);
      
      setReportTypes(typesData);
      setReportFormats(formatsData);
    } catch (error) {
      console.error('Error loading report metadata:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const handleFilterChange = (filterKey, value) => {
    setFormData(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [filterKey]: value
      }
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    const validation = reportsService.validateReportData(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    
    setLoading(true);
    try {
      await onGenerate(formData);
      handleClose();
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      title: '',
      description: '',
      report_type: '',
      template: '',
      format: 'pdf',
      filters: {},
      parameters: {},
      period_start: null,
      period_end: null
    });
    setErrors({});
    setSelectedTemplate(null);
    onClose();
  };

  const getFormatIcon = (format) => {
    const icons = {
      'pdf': <FileText className="h-4 w-4" />,
      'excel': <FileSpreadsheet className="h-4 w-4" />,
      'csv': <BarChart3 className="h-4 w-4" />,
      'json': <FileJson className="h-4 w-4" />
    };
    return icons[format] || <FileText className="h-4 w-4" />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generate New Report</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Report Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter report title"
                    className={errors.title ? 'border-red-500' : ''}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-500">{errors.title}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="report_type">Report Type *</Label>
                  <Select 
                    value={formData.report_type} 
                    onValueChange={(value) => handleInputChange('report_type', value)}
                  >
                    <SelectTrigger className={errors.report_type ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      {reportTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.report_type && (
                    <p className="text-sm text-red-500">{errors.report_type}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter report description (optional)"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Template Selection */}
          {formData.report_type && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Template Selection</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Choose Template (Optional)</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div
                      className={cn(
                        "p-3 border rounded-lg cursor-pointer transition-colors",
                        !formData.template ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => handleInputChange('template', '')}
                    >
                      <div className="font-medium">Default Template</div>
                      <div className="text-sm text-muted-foreground">
                        Use system default configuration
                      </div>
                    </div>
                    
                    {filteredTemplates.map((template) => (
                      <div
                        key={template.id}
                        className={cn(
                          "p-3 border rounded-lg cursor-pointer transition-colors",
                          formData.template === template.id ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                        )}
                        onClick={() => handleInputChange('template', template.id)}
                      >
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {template.description}
                        </div>
                        {template.is_system_template && (
                          <Badge variant="secondary" className="mt-1">System</Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {selectedTemplate && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-start gap-2">
                      <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-blue-900">Template Details</div>
                        <div className="text-sm text-blue-700 mt-1">
                          {selectedTemplate.description}
                        </div>
                        {selectedTemplate.default_filters && Object.keys(selectedTemplate.default_filters).length > 0 && (
                          <div className="text-sm text-blue-700 mt-2">
                            <strong>Default Filters:</strong> {JSON.stringify(selectedTemplate.default_filters)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Format and Period */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Output Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Output Format *</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {reportFormats.map((format) => (
                    <div
                      key={format.value}
                      className={cn(
                        "p-3 border rounded-lg cursor-pointer transition-colors text-center",
                        formData.format === format.value ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => handleInputChange('format', format.value)}
                    >
                      <div className="flex justify-center mb-2">
                        {getFormatIcon(format.value)}
                      </div>
                      <div className="font-medium">{format.label}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {format.description}
                      </div>
                    </div>
                  ))}
                </div>
                {errors.format && (
                  <p className="text-sm text-red-500">{errors.format}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Period Start (Optional)</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.period_start && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.period_start ? format(formData.period_start, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.period_start}
                        onSelect={(date) => handleInputChange('period_start', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>Period End (Optional)</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.period_end && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.period_end ? format(formData.period_end, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.period_end}
                        onSelect={(date) => handleInputChange('period_end', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              {errors.period_end && (
                <p className="text-sm text-red-500">{errors.period_end}</p>
              )}
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Generating...' : 'Generate Report'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ReportGenerationModal;
