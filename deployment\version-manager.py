#!/usr/bin/env python3
"""
GoID Version Management System
Manages service versions, tags, and deployment history
"""

import json
import os
import sys
import argparse
from datetime import datetime
from pathlib import Path

class VersionManager:
    def __init__(self, config_path="deployment/versions.json"):
        self.config_path = config_path
        self.config = self.load_config()
        self.history_path = "deployment/deployment_history.json"
        self.history = self.load_history()
        
    def load_config(self):
        """Load version configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Configuration file {self.config_path} not found")
            sys.exit(1)
    
    def save_config(self):
        """Save updated configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def load_history(self):
        """Load deployment history"""
        try:
            with open(self.history_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {"deployments": []}
    
    def save_history(self):
        """Save deployment history"""
        with open(self.history_path, 'w') as f:
            json.dump(self.history, f, indent=2)
    
    def list_versions(self):
        """List current versions of all services"""
        print("📋 Current Service Versions:")
        print("-" * 70)
        print(f"{'Service':<20} {'Version':<12} {'Last Build':<20} {'Repository'}")
        print("-" * 70)
        
        for service, config in self.config["services"].items():
            version = config["current_version"]
            last_build = config.get("last_build", "Never")
            if last_build != "Never":
                last_build = datetime.fromisoformat(last_build).strftime("%Y-%m-%d %H:%M")
            repo = config["docker_hub_repo"]
            print(f"{service:<20} v{version:<11} {last_build:<20} {repo}")
    
    def set_version(self, service, version):
        """Set specific version for a service"""
        if service not in self.config["services"]:
            print(f"❌ Unknown service: {service}")
            return False
            
        old_version = self.config["services"][service]["current_version"]
        self.config["services"][service]["current_version"] = version
        self.save_config()
        
        print(f"✅ Updated {service} version: {old_version} → {version}")
        return True
    
    def increment_version(self, service, version_type="patch"):
        """Increment version number"""
        if service not in self.config["services"]:
            print(f"❌ Unknown service: {service}")
            return None
            
        current = self.config["services"][service]["current_version"]
        major, minor, patch = map(int, current.split('.'))
        
        if version_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif version_type == "minor":
            minor += 1
            patch = 0
        else:  # patch
            patch += 1
            
        new_version = f"{major}.{minor}.{patch}"
        self.config["services"][service]["current_version"] = new_version
        self.save_config()
        
        print(f"✅ Incremented {service} version: {current} → {new_version} ({version_type})")
        return new_version
    
    def record_deployment(self, services, environment="production", notes=""):
        """Record a deployment in history"""
        deployment = {
            "timestamp": datetime.now().isoformat(),
            "environment": environment,
            "services": {},
            "notes": notes
        }
        
        # Record versions of deployed services
        for service in services:
            if service in self.config["services"]:
                deployment["services"][service] = self.config["services"][service]["current_version"]
        
        self.history["deployments"].append(deployment)
        self.save_history()
        
        print(f"✅ Recorded deployment of {', '.join(services)} to {environment}")
    
    def show_history(self, limit=10):
        """Show deployment history"""
        deployments = self.history["deployments"]
        
        if not deployments:
            print("📋 No deployment history found")
            return
        
        print(f"📋 Deployment History (last {min(limit, len(deployments))} deployments):")
        print("-" * 80)
        
        for deployment in deployments[-limit:]:
            timestamp = datetime.fromisoformat(deployment["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
            environment = deployment["environment"]
            services = deployment["services"]
            notes = deployment.get("notes", "")
            
            print(f"🚀 {timestamp} - {environment}")
            for service, version in services.items():
                print(f"   {service}: v{version}")
            if notes:
                print(f"   Notes: {notes}")
            print()
    
    def compare_versions(self, service1, version1, service2=None, version2=None):
        """Compare versions"""
        if service2 is None:
            service2 = service1
            version2 = self.config["services"][service1]["current_version"]
        
        def version_tuple(v):
            return tuple(map(int, v.split('.')))
        
        v1_tuple = version_tuple(version1)
        v2_tuple = version_tuple(version2)
        
        if v1_tuple < v2_tuple:
            print(f"📊 {service1} v{version1} < {service2} v{version2}")
        elif v1_tuple > v2_tuple:
            print(f"📊 {service1} v{version1} > {service2} v{version2}")
        else:
            print(f"📊 {service1} v{version1} = {service2} v{version2}")
    
    def update_docker_hub_repos(self, username):
        """Update Docker Hub repository names"""
        for service in self.config["services"]:
            self.config["services"][service]["docker_hub_repo"] = f"{username}/goid-{service}"
        
        self.save_config()
        print(f"✅ Updated Docker Hub repositories for username: {username}")
    
    def export_versions(self, format="json"):
        """Export current versions"""
        versions = {}
        for service, config in self.config["services"].items():
            versions[service] = config["current_version"]
        
        if format == "json":
            print(json.dumps(versions, indent=2))
        elif format == "env":
            for service, version in versions.items():
                env_var = f"{service.upper().replace('-', '_')}_VERSION"
                print(f"{env_var}={version}")
        elif format == "docker-compose":
            for service, version in versions.items():
                repo = self.config["services"][service]["docker_hub_repo"]
                print(f"    image: {repo}:{version}")

def main():
    parser = argparse.ArgumentParser(description="Manage GoID service versions")
    parser.add_argument("action", choices=[
        "list", "set", "increment", "history", "compare", "export", "update-repos", "record"
    ], help="Action to perform")
    
    parser.add_argument("--service", help="Service name")
    parser.add_argument("--version", help="Version number")
    parser.add_argument("--type", choices=["major", "minor", "patch"], 
                       default="patch", help="Version increment type")
    parser.add_argument("--username", help="Docker Hub username")
    parser.add_argument("--format", choices=["json", "env", "docker-compose"], 
                       default="json", help="Export format")
    parser.add_argument("--limit", type=int, default=10, help="History limit")
    parser.add_argument("--services", nargs="+", help="Services for deployment record")
    parser.add_argument("--environment", default="production", help="Deployment environment")
    parser.add_argument("--notes", help="Deployment notes")
    
    args = parser.parse_args()
    
    manager = VersionManager()
    
    if args.action == "list":
        manager.list_versions()
    elif args.action == "set":
        if not args.service or not args.version:
            print("❌ --service and --version required for set action")
            sys.exit(1)
        manager.set_version(args.service, args.version)
    elif args.action == "increment":
        if not args.service:
            print("❌ --service required for increment action")
            sys.exit(1)
        manager.increment_version(args.service, args.type)
    elif args.action == "history":
        manager.show_history(args.limit)
    elif args.action == "export":
        manager.export_versions(args.format)
    elif args.action == "update-repos":
        if not args.username:
            print("❌ --username required for update-repos action")
            sys.exit(1)
        manager.update_docker_hub_repos(args.username)
    elif args.action == "record":
        if not args.services:
            print("❌ --services required for record action")
            sys.exit(1)
        manager.record_deployment(args.services, args.environment, args.notes or "")

if __name__ == "__main__":
    main()
