#!/usr/bin/env python3
"""
Setup system admin users for testing hierarchical user management.
This script creates city_system_admin and subcity_system_admin users.
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group
from django.db import transaction
from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant
from users.models import User

def setup_system_admin_users():
    """Setup system admin users for testing."""
    print("🔧 Setting up system admin users...")
    
    try:
        with schema_context(get_public_schema_name()):
            # Get tenants
            city_tenant = Tenant.objects.filter(type='city').first()
            subcity_tenant = Tenant.objects.filter(type='subcity').first()
            
            if not city_tenant:
                print("❌ No city tenant found!")
                return False
                
            if not subcity_tenant:
                print("❌ No subcity tenant found!")
                return False
            
            print(f"✅ Found city tenant: {city_tenant.name}")
            print(f"✅ Found subcity tenant: {subcity_tenant.name}")
            
            # Get groups
            try:
                city_system_admin_group = Group.objects.get(name='city_system_admin')
                subcity_system_admin_group = Group.objects.get(name='subcity_system_admin')
                print("✅ Found system admin groups")
            except Group.DoesNotExist as e:
                print(f"❌ System admin group not found: {e}")
                return False
        
        # Create city system admin user
        with schema_context(city_tenant.schema_name):
            city_system_admin, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'username': '<EMAIL>',
                    'first_name': 'City System',
                    'last_name': 'Admin',
                    'role': 'city_system_admin',
                    'tenant': city_tenant,
                    'is_active': True
                }
            )
            
            if created:
                city_system_admin.set_password('admin123')
                city_system_admin.save()
                print(f"✅ Created city system admin: {city_system_admin.email}")
            else:
                print(f"ℹ️  City system admin already exists: {city_system_admin.email}")
        
        # Create subcity system admin user
        with schema_context(subcity_tenant.schema_name):
            subcity_system_admin, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'username': '<EMAIL>',
                    'first_name': 'Subcity System',
                    'last_name': 'Admin',
                    'role': 'subcity_system_admin',
                    'tenant': subcity_tenant,
                    'is_active': True
                }
            )
            
            if created:
                subcity_system_admin.set_password('admin123')
                subcity_system_admin.save()
                print(f"✅ Created subcity system admin: {subcity_system_admin.email}")
            else:
                print(f"ℹ️  Subcity system admin already exists: {subcity_system_admin.email}")
        
        # Assign users to groups
        with schema_context(get_public_schema_name()):
            city_system_admin.groups.add(city_system_admin_group)
            subcity_system_admin.groups.add(subcity_system_admin_group)
            print("✅ Assigned users to system admin groups")
        
        print("\n🎉 System admin users setup completed!")
        print("\n📋 Test Users Created:")
        print(f"  🏛️  City System Admin: <EMAIL> (password: admin123)")
        print(f"     - Can create users for subcities under {city_tenant.name}")
        print(f"     - Access: /users/city-management")
        print(f"  🏢  Subcity System Admin: <EMAIL> (password: admin123)")
        print(f"     - Can create users for kebeles under {subcity_tenant.name}")
        print(f"     - Access: /users/kebele-management")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Starting system admin users setup...")
    success = setup_system_admin_users()
    
    if success:
        print("\n✅ System admin users setup completed successfully!")
        print("ℹ️  You can now test hierarchical user management with these accounts")
    else:
        print("\n❌ System admin users setup failed!")
        sys.exit(1)
