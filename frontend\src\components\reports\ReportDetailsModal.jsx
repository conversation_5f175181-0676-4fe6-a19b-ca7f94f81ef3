import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON>itle, Di<PERSON>Footer } from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { 
  Download, 
  Calendar, 
  Clock, 
  User, 
  FileText, 
  BarChart3, 
  Database,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';
import reportsService from '../../services/reportsService';

const ReportDetailsModal = ({ isOpen, onClose, report, onDownload }) => {
  if (!report) return null;

  const getStatusIcon = (status) => {
    const icons = {
      'pending': <Clock className="h-4 w-4" />,
      'processing': <Loader2 className="h-4 w-4 animate-spin" />,
      'completed': <CheckCircle className="h-4 w-4" />,
      'failed': <XCircle className="h-4 w-4" />,
      'expired': <AlertCircle className="h-4 w-4" />
    };
    return icons[status] || <AlertCircle className="h-4 w-4" />;
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'text-yellow-600',
      'processing': 'text-blue-600',
      'completed': 'text-green-600',
      'failed': 'text-red-600',
      'expired': 'text-gray-600'
    };
    return colors[status] || 'text-gray-600';
  };

  const renderKeyMetrics = () => {
    if (!report.summary_data?.key_metrics) return null;

    const metrics = report.summary_data.key_metrics;
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Key Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(metrics).map(([key, value]) => (
              <div key={key} className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {typeof value === 'number' ? value.toLocaleString() : value}
                </div>
                <div className="text-sm text-muted-foreground capitalize">
                  {key.replace(/_/g, ' ')}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderFilters = () => {
    if (!report.filters || Object.keys(report.filters).length === 0) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Applied Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(report.filters).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center py-2 border-b last:border-b-0">
                <span className="font-medium capitalize">{key.replace(/_/g, ' ')}:</span>
                <span className="text-muted-foreground">
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <FileText className="h-6 w-6" />
            Report Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Information */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h2 className="text-2xl font-bold mb-2">{report.title}</h2>
                  {report.description && (
                    <p className="text-muted-foreground mb-4">{report.description}</p>
                  )}
                  
                  <div className="flex items-center gap-4 flex-wrap">
                    <div className={`flex items-center gap-2 ${getStatusColor(report.status)}`}>
                      {getStatusIcon(report.status)}
                      <span className="font-medium">{report.status_display}</span>
                    </div>
                    
                    <Badge variant="outline">
                      {report.report_type_display}
                    </Badge>
                    
                    <Badge variant="secondary">
                      {report.format_display}
                    </Badge>
                  </div>
                </div>
                
                {report.is_downloadable && (
                  <Button onClick={onDownload} className="ml-4">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                )}
              </div>

              <Separator className="my-4" />

              {/* Metadata Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm text-muted-foreground">Generated By</div>
                    <div className="font-medium">{report.generated_by_username}</div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm text-muted-foreground">Created</div>
                    <div className="font-medium">
                      {format(new Date(report.created_at), 'MMM dd, yyyy HH:mm')}
                    </div>
                  </div>
                </div>

                {report.generated_at && (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm text-muted-foreground">Completed</div>
                      <div className="font-medium">
                        {format(new Date(report.generated_at), 'MMM dd, yyyy HH:mm')}
                      </div>
                    </div>
                  </div>
                )}

                {report.processing_time_seconds && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm text-muted-foreground">Processing Time</div>
                      <div className="font-medium">
                        {reportsService.formatProcessingTime(report.processing_time_seconds)}
                      </div>
                    </div>
                  </div>
                )}

                {report.file_size && (
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm text-muted-foreground">File Size</div>
                      <div className="font-medium">
                        {reportsService.formatFileSize(report.file_size)}
                      </div>
                    </div>
                  </div>
                )}

                {report.total_records && (
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm text-muted-foreground">Total Records</div>
                      <div className="font-medium">{report.total_records.toLocaleString()}</div>
                    </div>
                  </div>
                )}

                {report.expires_at && (
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm text-muted-foreground">Expires</div>
                      <div className="font-medium">
                        {format(new Date(report.expires_at), 'MMM dd, yyyy')}
                      </div>
                    </div>
                  </div>
                )}

                {report.template_name && (
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm text-muted-foreground">Template</div>
                      <div className="font-medium">{report.template_name}</div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Period Information */}
          {(report.period_start || report.period_end) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Report Period</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  {report.period_start && (
                    <div>
                      <div className="text-sm text-muted-foreground">From</div>
                      <div className="font-medium">
                        {format(new Date(report.period_start), 'MMM dd, yyyy')}
                      </div>
                    </div>
                  )}
                  {report.period_end && (
                    <div>
                      <div className="text-sm text-muted-foreground">To</div>
                      <div className="font-medium">
                        {format(new Date(report.period_end), 'MMM dd, yyyy')}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Key Metrics */}
          {renderKeyMetrics()}

          {/* Applied Filters */}
          {renderFilters()}

          {/* Error Message */}
          {report.status === 'failed' && report.error_message && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="text-lg text-red-600 flex items-center gap-2">
                  <XCircle className="h-5 w-5" />
                  Error Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-red-50 p-4 rounded-lg">
                  <p className="text-red-800">{report.error_message}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {report.is_downloadable && (
            <Button onClick={onDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download Report
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReportDetailsModal;
