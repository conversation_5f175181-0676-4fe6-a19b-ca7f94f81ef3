#!/bin/bash
# Quick Deployment Script for aragawmebratu/goid-production
# This script builds and deploys the GoID system using your Docker Hub repository

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
VERSION_TYPE="patch"

print_header "GoID Production Deployment for $DOCKER_HUB_USERNAME"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Step 1: Build and push images
print_header "Step 1: Building and Pushing Images"

print_status "Building backend service..."
if ! ./deployment/build.sh --service backend --username "$DOCKER_HUB_USERNAME" --type "$VERSION_TYPE"; then
    print_error "Failed to build backend service"
    exit 1
fi

print_status "Building frontend service..."
if ! ./deployment/build.sh --service frontend --username "$DOCKER_HUB_USERNAME" --type "$VERSION_TYPE"; then
    print_error "Failed to build frontend service"
    exit 1
fi

print_success "All images built and pushed successfully!"

# Step 2: Create environment file
print_header "Step 2: Environment Configuration"

ENV_FILE="deployment/.env.production"
if [[ ! -f "$ENV_FILE" ]]; then
    print_status "Creating environment file from sample..."
    cp deployment/.env.production.sample "$ENV_FILE"
    
    print_warning "Please edit $ENV_FILE with your production settings:"
    print_warning "  - DB_PASSWORD: Set a secure database password"
    print_warning "  - SECRET_KEY: Set a secure Django secret key"
    print_warning "  - ALLOWED_HOSTS: Set your domain name"
    print_warning "  - CORS_ALLOWED_ORIGINS: Set your frontend URLs"
    
    read -p "Press Enter after editing the environment file to continue..."
fi

# Step 3: Generate docker-compose file
print_header "Step 3: Generating Docker Compose Configuration"

print_status "Generating docker-compose.production.yml..."
if ! ./deployment/deploy.sh generate --env-file "$ENV_FILE"; then
    print_error "Failed to generate docker-compose file"
    exit 1
fi

# Step 4: Deploy services
print_header "Step 4: Deploying Services"

print_status "Pulling latest images..."
if ! ./deployment/deploy.sh pull --env-file "$ENV_FILE"; then
    print_warning "Failed to pull some images, continuing with deployment..."
fi

print_status "Deploying services..."
if ! ./deployment/deploy.sh deploy --env-file "$ENV_FILE"; then
    print_error "Failed to deploy services"
    exit 1
fi

# Step 5: Show status
print_header "Step 5: Deployment Status"

print_status "Checking service status..."
./deployment/deploy.sh status --env-file "$ENV_FILE"

print_success "Deployment completed successfully!"
print_status ""
print_status "Your GoID system is now running:"
print_status "  Frontend: http://10.139.8.162:3000"
print_status "  Frontend: https://goid.uog.edu.et"
print_status "  Backend:  http://10.139.8.162:8000"
print_status "  Database: 10.139.8.162:5432"
print_status "  pgAdmin:  http://10.139.8.162:5050"
print_status ""
print_status "Next steps:"
print_status "  1. Install biometric service on client machines"
print_status "  2. Configure kebele information"
print_status "  3. Test fingerprint capture functionality"
print_status ""
print_status "Useful commands:"
print_status "  Check status: ./deployment/deploy.sh status"
print_status "  View logs:    ./deployment/deploy.sh logs"
print_status "  Stop services: ./deployment/deploy.sh stop"

print_header "Deployment Complete"
