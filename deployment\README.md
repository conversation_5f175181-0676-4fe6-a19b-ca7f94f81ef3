# GoID Production Deployment Guide

This directory contains all the tools and configurations needed to deploy GoID to production using Docker Hub and Docker Compose.

## Overview

The deployment system provides:
- **Automated versioning** for each service
- **Docker Hub integration** for image distribution
- **Production-ready docker-compose** configurations
- **Easy deployment scripts** for both Linux/Mac and Windows
- **Service management** tools for monitoring and maintenance

## Quick Start

### 1. Initial Setup

1. **Configure Docker Hub credentials:**
   ```bash
   # Edit deployment/versions.json and update Docker Hub repositories
   # Or use the build script with --username option
   ```

2. **Create environment configuration:**
   ```bash
   # Linux/Mac
   ./deployment/deploy.sh generate

   # Windows
   deployment\deploy.bat generate
   ```

3. **Edit the generated environment file:**
   ```bash
   # Edit deployment/.env.production with your settings
   nano deployment/.env.production
   ```

### 2. Build and Push Images

```bash
# Linux/Mac - Build all services and push to Docker Hub
./deployment/build.sh --username your-dockerhub-username

# Windows - Build all services and push to Docker Hub
deployment\build.bat --username your-dockerhub-username
```

### 3. Deploy to Production

```bash
# Linux/Mac - Deploy all services
./deployment/deploy.sh

# Windows - Deploy all services
deployment\deploy.bat
```

## Detailed Usage

### Building Images

The build system supports versioning and selective building:

```bash
# Build all services with patch version increment
./deployment/build.sh --username myuser

# Build specific service with minor version increment
./deployment/build.sh --service backend --type minor --username myuser

# Build with specific version
./deployment/build.sh --service frontend --version 2.1.0 --username myuser

# Build without pushing (local only)
./deployment/build.sh --no-push --username myuser

# List current versions
./deployment/build.sh --list
```

### Deployment Management

```bash
# Deploy all services
./deployment/deploy.sh

# Deploy specific services
./deployment/deploy.sh deploy --services "backend frontend"

# Check service status
./deployment/deploy.sh status

# View logs
./deployment/deploy.sh logs
./deployment/deploy.sh logs --service backend --follow

# Stop all services
./deployment/deploy.sh stop

# Pull latest images
./deployment/deploy.sh pull
```

### Environment Configuration

The `.env.production` file contains all production settings:

```env
# Docker Hub Configuration
DOCKER_HUB_USERNAME=your-dockerhub-username

# Service Versions
BACKEND_VERSION=1.0.0
FRONTEND_VERSION=1.0.0
BIOMETRIC_VERSION=1.0.0

# Database Configuration
DB_PASSWORD=your-secure-db-password
DB_PORT=5432

# Application Configuration
SECRET_KEY=your-very-secure-secret-key
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://your-domain.com

# API URLs
REACT_APP_API_URL=http://localhost:8000
REACT_APP_BIOMETRIC_SERVICE_URL=http://localhost:8002
```

## File Structure

```
deployment/
├── README.md                           # This file
├── versions.json                       # Service version tracking
├── .env.production                     # Production environment variables
├── docker-compose.production.yml      # Generated production compose file
├── docker-compose.production.template.yml  # Template for compose file
├── build-and-push.py                  # Python build script
├── deploy.py                          # Python deployment script
├── build.sh                           # Linux/Mac build script
├── build.bat                          # Windows build script
├── deploy.sh                          # Linux/Mac deployment script
└── deploy.bat                         # Windows deployment script
```

## Services

### Backend Service
- **Image:** `your-username/goid-backend`
- **Port:** 8000
- **Dependencies:** PostgreSQL, Redis
- **Features:** Django application with multi-tenant support

### Frontend Service
- **Image:** `your-username/goid-frontend`
- **Port:** 3000 (mapped to 80 in container)
- **Dependencies:** Backend service
- **Features:** React application with Nginx

### Biometric Service (Optional)
- **Image:** `your-username/goid-biometric-service`
- **Port:** 8002
- **Dependencies:** USB device access
- **Features:** Fingerprint capture and processing

### Infrastructure Services
- **PostgreSQL:** Database storage
- **Redis:** Caching and sessions
- **pgAdmin:** Database management (optional)
- **Nginx:** Reverse proxy (optional)

## Production Considerations

### Security
- Change default passwords in `.env.production`
- Use strong SECRET_KEY
- Configure proper ALLOWED_HOSTS
- Set up SSL/TLS certificates
- Restrict database access

### Performance
- Configure resource limits in docker-compose
- Set up database connection pooling
- Configure Redis memory limits
- Monitor service health

### Backup
- Regular database backups
- Media files backup
- Configuration backup

### Monitoring
- Service health checks
- Log aggregation
- Performance monitoring
- Alert configuration

## Troubleshooting

### Common Issues

1. **Docker Hub login fails:**
   ```bash
   docker login
   ```

2. **Environment file missing:**
   ```bash
   ./deployment/deploy.sh generate
   ```

3. **Service won't start:**
   ```bash
   ./deployment/deploy.sh logs --service backend
   ```

4. **Database connection issues:**
   - Check database credentials in `.env.production`
   - Verify database service is running
   - Check network connectivity

### Useful Commands

```bash
# Check Docker status
docker ps
docker images

# View service logs
docker-compose -f deployment/docker-compose.production.yml logs backend

# Execute commands in containers
docker-compose -f deployment/docker-compose.production.yml exec backend python manage.py shell

# Database backup
docker-compose -f deployment/docker-compose.production.yml exec db pg_dump -U goid_user goid_db > backup.sql
```

## Support

For issues and questions:
1. Check the logs: `./deployment/deploy.sh logs`
2. Verify configuration: `./deployment/deploy.sh status`
3. Review environment settings in `.env.production`
4. Check Docker Hub for image availability
