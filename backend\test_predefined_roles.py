#!/usr/bin/env python
"""
Test script to check and set up predefined roles
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group, Permission
from users.models_groups import TenantGroup
from django_tenants.utils import schema_context, get_public_schema_name

def check_existing_roles():
    """Check what predefined roles already exist"""
    print("🔍 Checking existing predefined roles...")
    
    with schema_context(get_public_schema_name()):
        # Check for predefined role groups
        predefined_roles = ['clerk', 'kebele_leader', 'subcity_admin', 'subcity_system_admin', 'city_admin', 'city_system_admin', 'print_id_cards']
        
        for role_name in predefined_roles:
            try:
                group = Group.objects.get(name=role_name)
                tenant_group = TenantGroup.objects.get(group=group)
                print(f"✅ {role_name}: Level {tenant_group.level}, Types: {tenant_group.allowed_tenant_types}")
            except Group.DoesNotExist:
                print(f"❌ {role_name}: Group not found")
            except TenantGroup.DoesNotExist:
                print(f"⚠️ {role_name}: Group exists but no TenantGroup")

def create_predefined_roles():
    """Create predefined roles if they don't exist"""
    print("\n🏗️ Creating predefined roles...")
    
    # Define roles with their permissions and allowed tenant types
    roles_config = {
        'clerk': {
            'description': 'Clerk role for kebele level tenants',
            'tenant_types': ['kebele'],
            'level': 10,
            'permissions': [
                'register_citizens', 'view_citizens_list', 'view_citizen_details',
                'generate_id_cards', 'view_id_cards_list', 'view_kebele_dashboard',
                'navigate_to_dashboard', 'navigate_to_citizens', 'navigate_to_id_cards'
            ]
        },
        'kebele_leader': {
            'description': 'Kebele leader role for kebele level tenants',
            'tenant_types': ['kebele'],
            'level': 20,
            'permissions': [
                'view_own_kebele_data', 'view_kebele_dashboard', 'view_kebele_reports',
                'view_citizens_list', 'view_citizen_details', 'view_id_cards_list',
                'approve_id_cards', 'verify_documents', 'navigate_to_dashboard',
                'navigate_to_citizens', 'navigate_to_id_cards', 'view_user_management'
            ]
        },
        'subcity_admin': {
            'description': 'Subcity admin role for subcity level tenants',
            'tenant_types': ['subcity'],
            'level': 30,
            'permissions': [
                'view_child_kebeles_data', 'view_subcity_dashboard', 'view_subcity_reports',
                'approve_id_cards', 'print_id_cards', 'send_id_cards_to_higher_level',
                'verify_documents', 'create_kebele_users', 'navigate_to_dashboard',
                'navigate_to_citizens', 'navigate_to_id_cards', 'view_user_management'
            ]
        },
        'subcity_system_admin': {
            'description': 'Subcity system admin role for managing kebele users',
            'tenant_types': ['subcity'],
            'level': 35,
            'permissions': [
                'create_kebele_users', 'view_user_management', 'navigate_to_dashboard'
            ]
        },
        'city_admin': {
            'description': 'City admin role for city level tenants',
            'tenant_types': ['city'],
            'level': 40,
            'permissions': [
                'view_child_subcities_data', 'view_city_dashboard', 'view_city_reports',
                'manage_tenants', 'view_all_reports', 'create_subcity_users',
                'navigate_to_dashboard', 'view_user_management'
            ]
        },
        'city_system_admin': {
            'description': 'City system admin role for managing subcity users',
            'tenant_types': ['city'],
            'level': 45,
            'permissions': [
                'create_subcity_users', 'view_user_management', 'navigate_to_dashboard'
            ]
        },
        'print_id_cards': {
            'description': 'ID card printing role for autonomous kebeles or centralized subcities',
            'tenant_types': ['kebele', 'subcity'],
            'level': 15,
            'permissions': [
                'print_id_cards', 'view_id_cards_list', 'navigate_to_id_cards'
            ]
        }
    }
    
    created_count = 0
    with schema_context(get_public_schema_name()):
        for role_name, config in roles_config.items():
            # Create Django group
            group, created = Group.objects.get_or_create(name=role_name)
            
            if created:
                print(f"  ✅ Created role group: {role_name}")
                created_count += 1
            else:
                print(f"  ℹ️ Role group already exists: {role_name}")
            
            # Add permissions to group
            permissions_added = 0
            for perm_codename in config['permissions']:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    if not group.permissions.filter(codename=perm_codename).exists():
                        group.permissions.add(permission)
                        permissions_added += 1
                except Permission.DoesNotExist:
                    print(f"    ⚠️ Permission not found: {perm_codename}")
            
            if permissions_added > 0:
                print(f"    ➕ Added {permissions_added} permissions to {role_name}")
            
            # Create or update TenantGroup
            tenant_group, tg_created = TenantGroup.objects.get_or_create(
                group=group,
                defaults={
                    'name': role_name,
                    'description': config['description'],
                    'group_type': 'administrative' if 'admin' in role_name or 'leader' in role_name else 'operational',
                    'level': config['level'],
                    'allowed_tenant_types': config['tenant_types'],
                    'is_active': True,
                    'tenant': None  # Global role
                }
            )
            
            if not tg_created:
                # Update existing TenantGroup
                tenant_group.allowed_tenant_types = config['tenant_types']
                tenant_group.level = config['level']
                tenant_group.save()
                print(f'    🔄 Updated TenantGroup for {role_name}')
    
    print(f"📊 Created/Updated {created_count} role groups")

if __name__ == '__main__':
    print("🚀 Setting up predefined roles...")
    check_existing_roles()
    create_predefined_roles()
    print("\n🔍 Final check...")
    check_existing_roles()
    print("✅ Done!")
