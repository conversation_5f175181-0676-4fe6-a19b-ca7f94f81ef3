#!/usr/bin/env python3
"""
Clean up the orphaned Zoble tenant that's causing migration issues.
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

def cleanup_zoble_tenant():
    """Clean up the orphaned Zoble tenant."""
    from django.db import connection
    from tenants.models.tenant import Tenant, Domain
    
    print('🔧 Fixing subcity_zoble schema issue...')
    
    # Check if Zoble tenant exists
    zoble_tenant = Tenant.objects.filter(name='Zoble').first()
    if zoble_tenant:
        print(f'Found orphaned tenant: {zoble_tenant.name} (schema: {zoble_tenant.schema_name})')
        
        # Delete domains first
        domains = Domain.objects.filter(tenant=zoble_tenant)
        print(f'Deleting {domains.count()} domains...')
        domains.delete()
        
        # Clean up all foreign key references first
        with connection.cursor() as cursor:
            print(f'Cleaning up foreign key references...')

            # Delete from tenants_subcity table
            cursor.execute('DELETE FROM tenants_subcity WHERE tenant_id = %s', [zoble_tenant.id])
            print(f'   ✅ Cleaned tenants_subcity')

            # Delete from tenants_kebele table
            cursor.execute('DELETE FROM tenants_kebele WHERE tenant_id = %s', [zoble_tenant.id])
            print(f'   ✅ Cleaned tenants_kebele')

            # Delete from tenants_cityadministration table
            cursor.execute('DELETE FROM tenants_cityadministration WHERE tenant_id = %s', [zoble_tenant.id])
            print(f'   ✅ Cleaned tenants_cityadministration')

        # Drop the schema completely
        with connection.cursor() as cursor:
            print(f'Dropping schema: {zoble_tenant.schema_name}')
            cursor.execute(f'DROP SCHEMA IF EXISTS "{zoble_tenant.schema_name}" CASCADE;')

        # Delete tenant record
        with connection.cursor() as cursor:
            cursor.execute('DELETE FROM tenants_tenant WHERE id = %s', [zoble_tenant.id])
        
        print('✅ Orphaned tenant and schema cleaned up')
    else:
        print('No Zoble tenant found')
    
    print('✅ Cleanup completed')

if __name__ == '__main__':
    cleanup_zoble_tenant()
