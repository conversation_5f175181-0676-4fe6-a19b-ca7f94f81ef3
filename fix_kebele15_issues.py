#!/usr/bin/env python3
"""
Fix Kebele15 Issues:
1. Workflow inconsistency (cards finishing approval without subcity)
2. Data integrity issues (showing wrong citizen details)
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from idcards.models import IDCard, IDCardStatus
from django.utils import timezone

def diagnose_issues():
    """Diagnose all issues in Kebele15."""
    print("🔍 Diagnosing Kebele15 Issues")
    print("=" * 50)
    
    try:
        # Get Kebele15 tenant
        kebele15 = Tenant.objects.get(id=18, name='Kebele15')
        print(f"📍 Kebele: {kebele15.name} (ID: {kebele15.id})")
        
        # Check workflow configuration
        try:
            workflow_config = kebele15.workflow_config
            print(f"✅ Workflow type: {workflow_config.workflow_type}")
            print(f"📋 Approval levels: {workflow_config.id_card_processing.get('approval_levels', [])}")
        except Exception as e:
            print(f"❌ Workflow config issue: {e}")
            return False
        
        # Check ID cards in the kebele schema
        with schema_context(kebele15.schema_name):
            from citizens.models import Citizen
            
            all_cards = IDCard.objects.all()
            print(f"\n📊 ID Card Analysis:")
            print(f"   Total ID cards: {all_cards.count()}")
            
            # Status distribution
            for status_choice in IDCardStatus.choices:
                status_code = status_choice[0]
                count = all_cards.filter(status=status_code).count()
                if count > 0:
                    print(f"   {status_choice[1]}: {count}")
            
            # Check for workflow issues
            print(f"\n🔍 Workflow Issues:")
            
            # Issue 1: Cards approved without subcity approval in centralized workflow
            problematic_cards = all_cards.filter(
                status=IDCardStatus.APPROVED,
                subcity_approved_by__isnull=True
            )
            print(f"   Cards approved without subcity: {problematic_cards.count()}")
            
            # Issue 2: Cards stuck in kebele_approved but should be processed
            stuck_cards = all_cards.filter(
                status=IDCardStatus.KEBELE_APPROVED,
                kebele_approved_at__isnull=False
            )
            print(f"   Cards waiting for subcity: {stuck_cards.count()}")
            
            # Issue 3: Check specific card mentioned in the issue
            maryam_card = all_cards.filter(
                citizen__first_name__icontains='Maryam',
                citizen__last_name__icontains='Newton'
            ).first()
            
            if maryam_card:
                print(f"\n🎯 Maryam's Card Analysis:")
                print(f"   Card Number: {maryam_card.card_number}")
                print(f"   Status: {maryam_card.status}")
                print(f"   Kebele approved: {maryam_card.kebele_approved_at}")
                print(f"   Subcity approved: {maryam_card.subcity_approved_at}")
                print(f"   Has kebele pattern: {maryam_card.has_kebele_pattern}")
                print(f"   Has subcity pattern: {maryam_card.has_subcity_pattern}")
                
                # Check if this card has workflow issues
                if (maryam_card.status == IDCardStatus.APPROVED and 
                    maryam_card.subcity_approved_by is None):
                    print(f"   🚨 ISSUE: Card is APPROVED without subcity approval!")
                elif maryam_card.status == IDCardStatus.KEBELE_APPROVED:
                    print(f"   ✅ CORRECT: Card is waiting for subcity approval")
                else:
                    print(f"   ℹ️ Status: {maryam_card.status}")
            
            return {
                'total_cards': all_cards.count(),
                'problematic_cards': problematic_cards.count(),
                'stuck_cards': stuck_cards.count(),
                'maryam_card': maryam_card
            }
            
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        return None

def fix_workflow_issues():
    """Fix workflow inconsistencies."""
    print("\n🔧 Fixing Workflow Issues")
    print("=" * 50)
    
    try:
        kebele15 = Tenant.objects.get(id=18, name='Kebele15')
        
        with schema_context(kebele15.schema_name):
            # Fix cards that are approved without subcity approval
            problematic_cards = IDCard.objects.filter(
                status=IDCardStatus.APPROVED,
                subcity_approved_by__isnull=True,
                kebele_approved_by__isnull=False
            )
            
            if not problematic_cards.exists():
                print("✅ No workflow issues found")
                return 0
            
            print(f"🔧 Found {problematic_cards.count()} cards to fix")
            
            fixed_count = 0
            with transaction.atomic():
                for card in problematic_cards:
                    print(f"   Fixing Card #{card.card_number}: {card.citizen.get_full_name()}")
                    
                    # Reset to kebele_approved status (waiting for subcity approval)
                    card.status = IDCardStatus.KEBELE_APPROVED
                    card.has_subcity_pattern = False  # Remove subcity pattern
                    card.submitted_to_subcity_at = timezone.now()  # Mark as submitted to subcity
                    
                    # Clear any printing-related flags that shouldn't be set yet
                    card.printed_at = None
                    card.printed_by = None
                    
                    card.save()
                    
                    print(f"     ✅ Reset to KEBELE_APPROVED status")
                    fixed_count += 1
            
            print(f"✅ Fixed {fixed_count} workflow issues")
            return fixed_count
            
    except Exception as e:
        print(f"❌ Error fixing workflow issues: {e}")
        return 0

def fix_data_integrity():
    """Fix any data integrity issues."""
    print("\n🔧 Checking Data Integrity")
    print("=" * 50)
    
    try:
        kebele15 = Tenant.objects.get(id=18, name='Kebele15')
        
        with schema_context(kebele15.schema_name):
            from citizens.models import Citizen
            
            # Check for orphaned ID cards (cards without valid citizens)
            all_cards = IDCard.objects.all()
            orphaned_cards = []
            
            for card in all_cards:
                try:
                    # Try to access citizen data
                    citizen_name = card.citizen.get_full_name()
                    citizen_id = card.citizen.digital_id
                except Exception as e:
                    orphaned_cards.append(card)
                    print(f"   🚨 Orphaned card: {card.card_number} - {e}")
            
            if orphaned_cards:
                print(f"❌ Found {len(orphaned_cards)} orphaned cards")
                return False
            else:
                print("✅ All ID cards have valid citizen references")
                
            # Check for duplicate card numbers
            card_numbers = all_cards.values_list('card_number', flat=True)
            duplicates = []
            seen = set()
            for card_number in card_numbers:
                if card_number in seen:
                    duplicates.append(card_number)
                seen.add(card_number)
            
            if duplicates:
                print(f"❌ Found duplicate card numbers: {duplicates}")
                return False
            else:
                print("✅ All card numbers are unique")
                
            return True
            
    except Exception as e:
        print(f"❌ Error checking data integrity: {e}")
        return False

def clear_cache():
    """Clear any cached data that might cause issues."""
    print("\n🗑️ Clearing Cache")
    print("=" * 50)
    
    try:
        from django.core.cache import cache
        
        cache_keys = [
            'tenant_workflow_18',
            'workflow_config_18',
            'kebele15_workflow',
            'kebele15_idcards',
            'biometric_templates_all'
        ]
        
        for key in cache_keys:
            cache.delete(key)
            print(f"🗑️ Cleared cache key: {key}")
        
        # Clear all kebele15-related cache
        cache.clear()
        print("🗑️ Cleared all cache")
        
    except Exception as e:
        print(f"⚠️ Error clearing cache: {e}")

def verify_fixes():
    """Verify that all fixes were successful."""
    print("\n✅ Verifying Fixes")
    print("=" * 50)
    
    try:
        kebele15 = Tenant.objects.get(id=18, name='Kebele15')
        workflow_config = kebele15.workflow_config
        
        print(f"📍 Kebele: {kebele15.name}")
        print(f"🔧 Workflow type: {workflow_config.workflow_type}")
        
        with schema_context(kebele15.schema_name):
            all_cards = IDCard.objects.all()
            
            # Check workflow consistency
            problematic_cards = all_cards.filter(
                status=IDCardStatus.APPROVED,
                subcity_approved_by__isnull=True
            )
            
            kebele_approved_cards = all_cards.filter(status=IDCardStatus.KEBELE_APPROVED)
            fully_approved_cards = all_cards.filter(
                status=IDCardStatus.APPROVED,
                subcity_approved_by__isnull=False
            )
            
            print(f"📊 Current Status:")
            print(f"   Total cards: {all_cards.count()}")
            print(f"   Kebele approved (waiting for subcity): {kebele_approved_cards.count()}")
            print(f"   Fully approved (with subcity approval): {fully_approved_cards.count()}")
            print(f"   Problematic (approved without subcity): {problematic_cards.count()}")
            
            # Check Maryam's card specifically
            maryam_card = all_cards.filter(
                citizen__first_name__icontains='Maryam',
                citizen__last_name__icontains='Newton'
            ).first()
            
            if maryam_card:
                print(f"\n🎯 Maryam's Card Status:")
                print(f"   Status: {maryam_card.status}")
                print(f"   Workflow correct: {'✅' if maryam_card.status == IDCardStatus.KEBELE_APPROVED else '❌'}")
            
            if problematic_cards.count() == 0:
                print("\n🎉 All workflow issues resolved!")
                return True
            else:
                print(f"\n❌ Still have {problematic_cards.count()} problematic cards")
                return False
                
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Kebele15 Issues Fix Script")
    print("=" * 50)
    
    # Step 1: Diagnose issues
    diagnosis = diagnose_issues()
    
    if diagnosis:
        # Step 2: Fix workflow issues
        fixed_count = fix_workflow_issues()
        
        # Step 3: Check data integrity
        data_integrity_ok = fix_data_integrity()
        
        # Step 4: Clear cache
        clear_cache()
        
        # Step 5: Verify fixes
        success = verify_fixes()
        
        if success:
            print("\n🎉 All Kebele15 issues have been resolved!")
            print("\nSummary:")
            print("✅ Workflow configuration: Centralized")
            print("✅ ID card approval flow: Kebele Leader → Subcity Admin → Printing")
            print("✅ Data integrity: All cards have valid citizen references")
            print("✅ Cache: Cleared to ensure fresh data")
            
            if fixed_count > 0:
                print(f"✅ Fixed {fixed_count} workflow inconsistencies")
        else:
            print("\n⚠️ Some issues may still exist - manual review recommended")
    else:
        print("\n❌ Failed to diagnose issues")
