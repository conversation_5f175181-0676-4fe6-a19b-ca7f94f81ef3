#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create navigation permissions for the RBAC system
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django_tenants.utils import schema_context, get_public_schema_name

def create_navigation_permissions():
    """Create navigation permissions if they don't exist"""
    print("🏗️ Creating navigation permissions...")
    
    # Get or create content type for users app
    with schema_context(get_public_schema_name()):
        content_type, _ = ContentType.objects.get_or_create(
            app_label='users',
            model='user'
        )
        
        # Define navigation permissions
        navigation_permissions = [
            # Basic navigation
            ('navigate_to_dashboard', 'Can navigate to dashboard'),
            ('navigate_to_citizens', 'Can navigate to citizens'),
            ('navigate_to_id_cards', 'Can navigate to ID cards'),
            ('navigate_to_reports', 'Can navigate to reports'),
            
            # Dashboard permissions
            ('view_kebele_dashboard', 'Can view kebele dashboard'),
            ('view_subcity_dashboard', 'Can view subcity dashboard'),
            ('view_city_dashboard', 'Can view city dashboard'),
            
            # Citizens permissions
            ('register_citizens', 'Can register citizens'),
            ('view_citizens_list', 'Can view citizens list'),
            ('view_citizen_details', 'Can view citizen details'),
            ('view_own_kebele_data', 'Can view own kebele data'),
            ('view_child_kebeles_data', 'Can view child kebeles data'),
            ('view_child_subcities_data', 'Can view child subcities data'),
            
            # ID Cards permissions
            ('generate_id_cards', 'Can generate ID cards'),
            ('view_id_cards_list', 'Can view ID cards list'),
            ('approve_id_cards', 'Can approve ID cards'),
            ('print_id_cards', 'Can print ID cards'),
            ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
            ('verify_documents', 'Can verify documents'),
            
            # User management permissions
            ('view_user_management', 'Can view user management'),
            ('create_kebele_users', 'Can create kebele users'),
            ('create_subcity_users', 'Can create subcity users'),
            
            # Reports permissions
            ('view_kebele_reports', 'Can view kebele reports'),
            ('view_subcity_reports', 'Can view subcity reports'),
            ('view_city_reports', 'Can view city reports'),
            ('view_all_reports', 'Can view all reports'),
            
            # System permissions
            ('manage_tenants', 'Can manage tenants'),
        ]
        
        created_count = 0
        for codename, name in navigation_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=content_type,
                defaults={'name': name}
            )
            
            if created:
                print(f"  ✅ Created permission: {codename}")
                created_count += 1
            else:
                print(f"  ℹ️ Permission already exists: {codename}")
        
        print(f"📊 Created {created_count} new permissions")

def check_existing_permissions():
    """Check what permissions already exist"""
    print("🔍 Checking existing permissions...")
    
    with schema_context(get_public_schema_name()):
        # Check for navigation permissions
        navigation_perms = [
            'navigate_to_dashboard', 'navigate_to_citizens', 'navigate_to_id_cards',
            'view_kebele_dashboard', 'view_subcity_dashboard', 'view_city_dashboard',
            'register_citizens', 'view_citizens_list', 'view_citizen_details',
            'generate_id_cards', 'view_id_cards_list', 'approve_id_cards', 'print_id_cards'
        ]
        
        for perm_codename in navigation_perms:
            try:
                permission = Permission.objects.get(codename=perm_codename)
                print(f"✅ {perm_codename}: {permission.name}")
            except Permission.DoesNotExist:
                print(f"❌ {perm_codename}: Permission not found")

if __name__ == '__main__':
    print("🚀 Setting up navigation permissions...")
    check_existing_permissions()
    create_navigation_permissions()
    print("\n🔍 Final check...")
    check_existing_permissions()
    print("✅ Done!")
