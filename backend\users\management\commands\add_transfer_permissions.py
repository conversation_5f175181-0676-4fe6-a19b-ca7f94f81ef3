from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Add transfer and clearance permissions to kebele_leader group'

    def handle(self, *args, **options):
        self.stdout.write("🔧 Adding Transfer & Clearance Permissions to Kebele Leader")
        self.stdout.write("="*60)
        
        try:
            # Find kebele_leader group
            group = Group.objects.get(name='kebele_leader')
            self.stdout.write(f"✅ Found kebele_leader group (ID: {group.id})")
            
            # Get content type for User model
            user_ct = ContentType.objects.get_for_model(User)
            
            # Show current permissions
            current_count = group.permissions.count()
            self.stdout.write(f"\n📋 Current permissions ({current_count}):")
            for perm in group.permissions.all().order_by('codename'):
                self.stdout.write(f"  • {perm.codename}")
            
            # Define required transfer permissions
            required_permissions = [
                ('transfer_citizens', 'Can transfer citizens'),
                ('create_transfers', 'Can create transfer requests'),
                ('approve_transfer_requests', 'Can approve transfer requests'),
                ('view_transfers', 'Can view transfers'),
                ('create_clearances', 'Can create clearance requests'),
                ('view_clearances', 'Can view clearances'),
            ]
            
            self.stdout.write(f"\n🔧 Adding missing permissions...")
            
            added_count = 0
            for codename, name in required_permissions:
                # Create permission if it doesn't exist
                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    defaults={
                        'name': name,
                        'content_type': user_ct
                    }
                )
                
                if created:
                    self.stdout.write(f"✅ Created permission: {codename}")
                
                # Add permission to group if not already present
                if not group.permissions.filter(codename=codename).exists():
                    group.permissions.add(permission)
                    self.stdout.write(f"✅ Added to group: {codename}")
                    added_count += 1
                else:
                    self.stdout.write(f"⏭️  Already exists: {codename}")
            
            # Show final results
            final_count = group.permissions.count()
            self.stdout.write(f"\n📊 Summary:")
            self.stdout.write(f"  • Permissions added: {added_count}")
            self.stdout.write(f"  • Total permissions: {current_count} → {final_count}")
            
            # Verify transfer permissions
            transfer_perms = group.permissions.filter(codename__icontains='transfer')
            clearance_perms = group.permissions.filter(codename__icontains='clearance')
            
            self.stdout.write(f"\n🔍 Verification:")
            self.stdout.write(f"Transfer permissions ({transfer_perms.count()}):")
            for perm in transfer_perms:
                self.stdout.write(f"  ✅ {perm.codename}")
            
            self.stdout.write(f"Clearance permissions ({clearance_perms.count()}):")
            for perm in clearance_perms:
                self.stdout.write(f"  ✅ {perm.codename}")
            
            # Test with a kebele_leader user
            self.stdout.write(f"\n🧪 Testing with kebele_leader user...")
            kebele_leader_user = User.objects.filter(role='kebele_leader').first()
            if kebele_leader_user:
                self.stdout.write(f"Testing with: {kebele_leader_user.email}")
                
                # Check if user is in kebele_leader group
                is_in_group = kebele_leader_user.groups.filter(name='kebele_leader').exists()
                self.stdout.write(f"Is in kebele_leader group: {is_in_group}")
                
                if not is_in_group:
                    self.stdout.write("⚠️ User not in kebele_leader group, adding...")
                    group.user_set.add(kebele_leader_user)
                    self.stdout.write("✅ Added user to kebele_leader group")
                
                # Test specific permissions
                test_permissions = ['transfer_citizens', 'create_transfers', 'create_clearances']
                for perm in test_permissions:
                    has_perm = kebele_leader_user.has_perm(f'users.{perm}')
                    status = "✅" if has_perm else "❌"
                    self.stdout.write(f"  {status} {perm}: {has_perm}")
            else:
                self.stdout.write("ℹ️ No kebele_leader users found to test")
            
            self.stdout.write(f"\n✅ Transfer & Clearance permissions setup complete!")
            self.stdout.write(f"\n🎯 Next Steps:")
            self.stdout.write("1. Clear browser cache and refresh page")
            self.stdout.write("2. Login as kebele_leader user")
            self.stdout.write("3. Go to citizen details page")
            self.stdout.write("4. Transfer and Clearance buttons should now be visible")
            
        except Group.DoesNotExist:
            self.stdout.write("❌ kebele_leader group not found!")
        except Exception as e:
            self.stdout.write(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
