#!/usr/bin/env python3
"""
Complete fix for workflow switching 403 Forbidden error.

This script addresses all potential causes of the 403 error:
1. Missing permissions
2. Incorrect role assignments
3. Tenant hierarchy issues
4. API endpoint permissions
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from tenants.models import Tenant
from common.multi_tenant_rbac import MultiTenantRBAC

User = get_user_model()

def diagnose_permission_issue():
    """Diagnose the specific permission issue causing 403 error."""
    print("🔍 Diagnosing workflow permission issue...")
    
    # Find subcity admins
    subcity_admins = User.objects.filter(role='subcity_admin')
    
    if not subcity_admins.exists():
        print("❌ No subcity admins found!")
        return
    
    for admin in subcity_admins:
        print(f"\n👤 Checking user: {admin.email}")
        print(f"   Role: {admin.role}")
        print(f"   Is superuser: {admin.is_superuser}")
        print(f"   Tenant: {getattr(admin, 'tenant', 'None')}")
        
        # Check workflow permissions
        workflow_perms = ['manage_workflows', 'view_workflows', 'switch_workflows']
        for perm in workflow_perms:
            has_perm = (admin.has_perm(f'auth.{perm}') or 
                       admin.user_permissions.filter(codename=perm).exists() or
                       admin.groups.filter(permissions__codename=perm).exists())
            print(f"   {perm}: {'✅' if has_perm else '❌'}")
        
        # Check child kebeles
        if hasattr(admin, 'tenant') and admin.tenant:
            child_kebeles = Tenant.objects.filter(
                type='kebele',
                parent=admin.tenant
            )
            print(f"   Child kebeles: {child_kebeles.count()}")
            
            for kebele in child_kebeles[:3]:  # Show first 3
                can_manage = MultiTenantRBAC.can_manage_workflow(admin, kebele)
                print(f"     📋 {kebele.name}: {'✅' if can_manage else '❌'}")

def fix_missing_permissions():
    """Create and assign missing workflow permissions."""
    print("\n🔧 Creating missing workflow permissions...")
    
    user_ct = ContentType.objects.get_for_model(User)
    
    permissions_data = [
        ('manage_workflows', 'Can manage and switch workflow types'),
        ('view_workflows', 'Can view workflow configurations'),
        ('switch_workflows', 'Can switch between workflow types'),
        ('create_kebele_users', 'Can create users for kebele tenants'),
        ('view_child_kebeles_data', 'Can view data from child kebeles'),
        ('view_subcity_dashboard', 'Can view subcity dashboard'),
        ('view_subcity_reports', 'Can view subcity reports'),
    ]
    
    created_count = 0
    for codename, name in permissions_data:
        permission, created = Permission.objects.get_or_create(
            codename=codename,
            defaults={
                'name': name,
                'content_type': user_ct
            }
        )
        
        if created:
            print(f"  ✅ Created permission: {codename}")
            created_count += 1
        else:
            print(f"  ⏭️  Permission exists: {codename}")
    
    return created_count

def assign_permissions_to_admins():
    """Assign workflow permissions directly to admin users."""
    print("\n🔧 Assigning permissions to admin users...")
    
    # Get all workflow-related permissions
    workflow_permissions = Permission.objects.filter(
        codename__in=[
            'manage_workflows', 'view_workflows', 'switch_workflows',
            'create_kebele_users', 'view_child_kebeles_data',
            'view_subcity_dashboard', 'view_subcity_reports'
        ]
    )
    
    # Assign to subcity admins
    subcity_admins = User.objects.filter(role='subcity_admin')
    subcity_count = 0
    
    for admin in subcity_admins:
        for permission in workflow_permissions:
            admin.user_permissions.add(permission)
        subcity_count += 1
        print(f"  ✅ Granted permissions to subcity admin: {admin.email}")
    
    # Assign to city admins
    city_admins = User.objects.filter(role='city_admin')
    city_count = 0
    
    for admin in city_admins:
        for permission in workflow_permissions:
            admin.user_permissions.add(permission)
        city_count += 1
        print(f"  ✅ Granted permissions to city admin: {admin.email}")
    
    return subcity_count, city_count

def fix_tenant_hierarchy():
    """Fix any tenant hierarchy issues."""
    print("\n🔧 Checking tenant hierarchy...")
    
    # Check for kebeles without proper parent relationships
    orphaned_kebeles = Tenant.objects.filter(type='kebele', parent__isnull=True)
    
    if orphaned_kebeles.exists():
        print(f"⚠️  Found {orphaned_kebeles.count()} kebeles without parent subcity")
        for kebele in orphaned_kebeles:
            print(f"   📋 Orphaned kebele: {kebele.name}")
    else:
        print("✅ All kebeles have proper parent relationships")
    
    # Check for subcities without proper parent relationships
    orphaned_subcities = Tenant.objects.filter(type='subcity', parent__isnull=True)
    
    if orphaned_subcities.exists():
        print(f"⚠️  Found {orphaned_subcities.count()} subcities without parent city")
        for subcity in orphaned_subcities:
            print(f"   📋 Orphaned subcity: {subcity.name}")
    else:
        print("✅ All subcities have proper parent relationships")

def test_workflow_api_access():
    """Test workflow API access for a sample user."""
    print("\n🧪 Testing workflow API access...")
    
    subcity_admin = User.objects.filter(role='subcity_admin').first()
    
    if not subcity_admin:
        print("❌ No subcity admin found for testing")
        return
    
    print(f"Testing API access for: {subcity_admin.email}")
    
    # Get child kebeles
    if hasattr(subcity_admin, 'tenant') and subcity_admin.tenant:
        child_kebeles = Tenant.objects.filter(
            type='kebele',
            parent=subcity_admin.tenant
        )
        
        print(f"Child kebeles to test: {child_kebeles.count()}")
        
        for kebele in child_kebeles[:2]:  # Test first 2
            print(f"\n  Testing kebele: {kebele.name} (ID: {kebele.id})")
            
            # Test MultiTenantRBAC
            can_manage = MultiTenantRBAC.can_manage_workflow(subcity_admin, kebele)
            print(f"    MultiTenantRBAC.can_manage_workflow: {'✅' if can_manage else '❌'}")
            
            # Test role-based check
            role_check = (subcity_admin.role == 'subcity_admin' and
                         subcity_admin.tenant.type == 'subcity' and
                         kebele.type == 'kebele' and
                         kebele.parent_id == subcity_admin.tenant.id)
            print(f"    Role-based hierarchy check: {'✅' if role_check else '❌'}")
            
            # Test permission check
            has_manage_perm = subcity_admin.has_perm('auth.manage_workflows')
            print(f"    Has manage_workflows permission: {'✅' if has_manage_perm else '❌'}")

def create_test_api_call():
    """Create a test script for API calls."""
    print("\n🔧 Creating test API call script...")
    
    test_script = """
# Test workflow switching API call
# Run this in Django shell: python manage.py shell

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from common.multi_tenant_rbac import MultiTenantRBAC
import requests

User = get_user_model()

# Get a subcity admin
admin = User.objects.filter(role='subcity_admin').first()
print(f"Testing with user: {admin.email}")

# Get a child kebele
kebele = Tenant.objects.filter(type='kebele', parent=admin.tenant).first()
print(f"Testing with kebele: {kebele.name} (ID: {kebele.id})")

# Test permission
can_manage = MultiTenantRBAC.can_manage_workflow(admin, kebele)
print(f"Can manage workflow: {can_manage}")

# Test API call (you'll need to be logged in)
# POST /api/tenants/{kebele.id}/workflow/switch/
# Body: {"workflow_type": "autonomous", "reason": "Test switch"}
"""
    
    with open('test_workflow_api.py', 'w') as f:
        f.write(test_script)
    
    print("  ✅ Created test_workflow_api.py")

def main():
    print("🚀 Fixing Workflow 403 Forbidden Error")
    print("="*50)
    
    try:
        with transaction.atomic():
            # Step 1: Diagnose the issue
            diagnose_permission_issue()
            
            # Step 2: Fix missing permissions
            perm_count = fix_missing_permissions()
            
            # Step 3: Assign permissions to admins
            subcity_count, city_count = assign_permissions_to_admins()
            
            # Step 4: Check tenant hierarchy
            fix_tenant_hierarchy()
            
            # Step 5: Test API access
            test_workflow_api_access()
            
            # Step 6: Create test script
            create_test_api_call()
            
            print("\n" + "="*50)
            print("✅ Workflow 403 Error Fix Completed!")
            print(f"📊 Summary:")
            print(f"  • Created {perm_count} new permissions")
            print(f"  • Updated {subcity_count} subcity admins")
            print(f"  • Updated {city_count} city admins")
            
            print("\n🎯 Next Steps:")
            print("1. Restart the Django server: python manage.py runserver")
            print("2. Clear browser cache and refresh the page")
            print("3. Try workflow switching again")
            print("4. Check browser console for debug logs")
            print("5. Check Django server logs for permission checks")
            
            print("\n🔧 If still having issues:")
            print("1. Run: python test_workflow_api.py")
            print("2. Check user role and tenant assignments")
            print("3. Verify API endpoint is accessible")
            
    except Exception as e:
        print(f"❌ Error fixing workflow permissions: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
