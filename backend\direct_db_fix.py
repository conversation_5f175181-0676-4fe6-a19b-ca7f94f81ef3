#!/usr/bin/env python
"""
Direct database fix for predefined roles - bypasses all API layers
"""
import os
import sys
import django
import psycopg2
from psycopg2.extras import RealDictCursor

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.conf import settings

def get_db_connection():
    """Get direct database connection"""
    db_config = settings.DATABASES['default']
    return psycopg2.connect(
        host=db_config['HOST'],
        port=db_config['PORT'],
        database=db_config['NAME'],
        user=db_config['USER'],
        password=db_config['PASSWORD']
    )

def fix_permissions_directly():
    """Fix permissions directly in the database"""
    print("🔧 Fixing permissions directly in database...")
    
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    try:
        # First, get the content type ID for users.user
        cursor.execute("""
            SELECT id FROM django_content_type 
            WHERE app_label = 'users' AND model = 'user'
        """)
        content_type_result = cursor.fetchone()
        
        if not content_type_result:
            # Create content type if it doesn't exist
            cursor.execute("""
                INSERT INTO django_content_type (app_label, model) 
                VALUES ('users', 'user') 
                RETURNING id
            """)
            content_type_id = cursor.fetchone()['id']
            print(f"  ✅ Created content type with ID: {content_type_id}")
        else:
            content_type_id = content_type_result['id']
            print(f"  ℹ️ Using existing content type ID: {content_type_id}")
        
        # Define all required permissions
        permissions = [
            ('view_kebele_dashboard', 'Can view kebele dashboard'),
            ('view_subcity_dashboard', 'Can view subcity dashboard'),
            ('view_city_dashboard', 'Can view city dashboard'),
            ('register_citizens', 'Can register citizens'),
            ('view_citizens_list', 'Can view citizens list'),
            ('view_citizen_details', 'Can view citizen details'),
            ('view_own_kebele_data', 'Can view own kebele data'),
            ('view_child_kebeles_data', 'Can view child kebeles data'),
            ('view_child_subcities_data', 'Can view child subcities data'),
            ('generate_id_cards', 'Can generate ID cards'),
            ('view_id_cards_list', 'Can view ID cards list'),
            ('approve_id_cards', 'Can approve ID cards'),
            ('print_id_cards', 'Can print ID cards'),
            ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
            ('verify_documents', 'Can verify documents'),
            ('create_kebele_users', 'Can create kebele users'),
            ('create_subcity_users', 'Can create subcity users'),
            ('view_kebele_reports', 'Can view kebele reports'),
            ('view_subcity_reports', 'Can view subcity reports'),
            ('view_city_reports', 'Can view city reports'),
            ('view_all_reports', 'Can view all reports'),
            ('create_transfers', 'Can create transfer requests'),
            ('approve_transfer_requests', 'Can approve transfer requests'),
            ('create_clearances', 'Can create clearance requests'),
            ('view_clearances', 'Can view clearances'),
            ('manage_tenants', 'Can manage tenants'),
        ]
        
        # Insert permissions
        created_permissions = 0
        for codename, name in permissions:
            cursor.execute("""
                INSERT INTO auth_permission (name, content_type_id, codename)
                VALUES (%s, %s, %s)
                ON CONFLICT (content_type_id, codename) DO NOTHING
                RETURNING id
            """, (name, content_type_id, codename))
            
            if cursor.fetchone():
                created_permissions += 1
                print(f"    ✅ Created permission: {codename}")
            else:
                print(f"    ℹ️ Permission exists: {codename}")
        
        print(f"  📊 Created {created_permissions} new permissions")
        
        # Define role configurations
        roles_config = {
            'clerk': [
                'register_citizens', 'view_citizens_list', 'view_citizen_details',
                'generate_id_cards', 'view_id_cards_list', 'view_kebele_dashboard'
            ],
            'kebele_leader': [
                'view_own_kebele_data', 'view_kebele_dashboard', 'view_kebele_reports',
                'view_citizens_list', 'view_citizen_details', 'view_id_cards_list',
                'approve_id_cards', 'verify_documents', 'create_transfers',
                'approve_transfer_requests', 'create_clearances', 'view_clearances'
            ],
            'subcity_admin': [
                'view_child_kebeles_data', 'view_subcity_dashboard', 'view_subcity_reports',
                'approve_id_cards', 'print_id_cards', 'send_id_cards_to_higher_level',
                'verify_documents', 'create_kebele_users'
            ],
            'subcity_system_admin': ['create_kebele_users'],
            'city_admin': [
                'view_child_subcities_data', 'view_city_dashboard', 'view_city_reports',
                'manage_tenants', 'view_all_reports', 'create_subcity_users'
            ],
            'city_system_admin': ['create_subcity_users'],
            'print_id_cards': ['print_id_cards']
        }
        
        # Create/update groups and assign permissions
        for role_name, permission_codenames in roles_config.items():
            print(f"\n🔧 Processing role: {role_name}")
            
            # Create group if it doesn't exist
            cursor.execute("""
                INSERT INTO auth_group (name) VALUES (%s)
                ON CONFLICT (name) DO NOTHING
                RETURNING id
            """, (role_name,))
            
            result = cursor.fetchone()
            if result:
                group_id = result['id']
                print(f"    ✅ Created group: {role_name} (ID: {group_id})")
            else:
                # Get existing group ID
                cursor.execute("SELECT id FROM auth_group WHERE name = %s", (role_name,))
                group_id = cursor.fetchone()['id']
                print(f"    ℹ️ Using existing group: {role_name} (ID: {group_id})")
            
            # Clear existing permissions for this group
            cursor.execute("DELETE FROM auth_group_permissions WHERE group_id = %s", (group_id,))
            
            # Add permissions to group
            permissions_added = 0
            for perm_codename in permission_codenames:
                cursor.execute("""
                    SELECT id FROM auth_permission 
                    WHERE codename = %s AND content_type_id = %s
                """, (perm_codename, content_type_id))
                
                perm_result = cursor.fetchone()
                if perm_result:
                    perm_id = perm_result['id']
                    cursor.execute("""
                        INSERT INTO auth_group_permissions (group_id, permission_id)
                        VALUES (%s, %s)
                        ON CONFLICT DO NOTHING
                    """, (group_id, perm_id))
                    permissions_added += 1
                    print(f"      ✅ Added permission: {perm_codename}")
                else:
                    print(f"      ❌ Permission not found: {perm_codename}")
            
            print(f"    📊 Added {permissions_added} permissions to {role_name}")
        
        # Commit all changes
        conn.commit()
        print("\n✅ Database changes committed successfully")
        
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        cursor.close()
        conn.close()

def assign_users_to_groups():
    """Assign users to their correct groups"""
    print("\n👥 Assigning users to correct groups...")
    
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    try:
        # Get all users with their roles and tenant info
        cursor.execute("""
            SELECT DISTINCT 
                u.id as user_id,
                u.email,
                u.role,
                t.type as tenant_type,
                t.schema_name
            FROM public.auth_user u
            JOIN public.tenants_tenant t ON u.tenant_id = t.id
            WHERE u.role IS NOT NULL
        """)
        
        users = cursor.fetchall()
        print(f"  Found {len(users)} users with roles")
        
        role_mapping = {
            'clerk': 'clerk',
            'kebele_leader': 'kebele_leader',
            'subcity_admin': 'subcity_admin',
            'subcity_system_admin': 'subcity_system_admin',
            'city_admin': 'city_admin',
            'city_system_admin': 'city_system_admin'
        }
        
        assigned_count = 0
        for user in users:
            group_name = role_mapping.get(user['role'])
            if not group_name:
                print(f"    ⚠️ No mapping for role: {user['role']} (user: {user['email']})")
                continue
            
            # Get group ID
            cursor.execute("SELECT id FROM auth_group WHERE name = %s", (group_name,))
            group_result = cursor.fetchone()
            if not group_result:
                print(f"    ❌ Group not found: {group_name}")
                continue
            
            group_id = group_result['id']
            
            # Clear existing group memberships for this user
            cursor.execute("DELETE FROM auth_user_groups WHERE user_id = %s", (user['user_id'],))
            
            # Add user to correct group
            cursor.execute("""
                INSERT INTO auth_user_groups (user_id, group_id)
                VALUES (%s, %s)
                ON CONFLICT DO NOTHING
            """, (user['user_id'], group_id))
            
            assigned_count += 1
            print(f"    ✅ Assigned {user['email']} ({user['role']}) to {group_name}")
        
        # Commit changes
        conn.commit()
        print(f"  📊 Assigned {assigned_count} users to groups")
        
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Error assigning users: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        cursor.close()
        conn.close()

def verify_fix():
    """Verify the fix worked"""
    print("\n🔍 Verifying the fix...")
    
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    try:
        # Check kebele_leader group permissions
        cursor.execute("""
            SELECT p.codename, p.name
            FROM auth_permission p
            JOIN auth_group_permissions gp ON p.id = gp.permission_id
            JOIN auth_group g ON gp.group_id = g.id
            WHERE g.name = 'kebele_leader'
            ORDER BY p.codename
        """)
        
        kebele_leader_perms = cursor.fetchall()
        print(f"  kebele_leader group has {len(kebele_leader_perms)} permissions:")
        for perm in kebele_leader_perms:
            print(f"    - {perm['codename']}")
        
        # Check if Transfer and Clearance permissions are there
        transfer_clearance_perms = [p for p in kebele_leader_perms 
                                  if any(keyword in p['codename'] for keyword in ['transfer', 'clearance'])]
        
        if transfer_clearance_perms:
            print(f"  ✅ Found {len(transfer_clearance_perms)} Transfer/Clearance permissions:")
            for perm in transfer_clearance_perms:
                print(f"    ✅ {perm['codename']}")
        else:
            print("  ❌ No Transfer/Clearance permissions found")
        
        return len(transfer_clearance_perms) > 0
        
    except Exception as e:
        print(f"❌ Error verifying: {e}")
        return False
        
    finally:
        cursor.close()
        conn.close()

if __name__ == '__main__':
    print("🚀 Starting direct database fix for predefined roles...")
    
    try:
        # Step 1: Fix permissions
        if fix_permissions_directly():
            print("✅ Permissions fixed successfully")
        else:
            print("❌ Failed to fix permissions")
            exit(1)
        
        # Step 2: Assign users to groups
        if assign_users_to_groups():
            print("✅ Users assigned successfully")
        else:
            print("❌ Failed to assign users")
            exit(1)
        
        # Step 3: Verify the fix
        if verify_fix():
            print("✅ Fix verification successful")
        else:
            print("❌ Fix verification failed")
            exit(1)
        
        print("\n🎉 Direct database fix completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart the Docker containers")
        print("2. Clear browser cache and refresh")
        print("3. Check /debug/role to verify permissions")
        print("4. Test navigation menus")
        
    except Exception as e:
        print(f"❌ Critical error: {e}")
        import traceback
        traceback.print_exc()
