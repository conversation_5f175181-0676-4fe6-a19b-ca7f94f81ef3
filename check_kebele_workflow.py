#!/usr/bin/env python3
"""
Check kebele workflow configuration
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig

def check_kebele_workflow():
    """Check workflow configuration for kebeles."""
    print("🔍 Checking kebele workflow configurations...")
    
    # Get all kebele tenants
    kebeles = Tenant.objects.filter(type='kebele')
    print(f"Found {kebeles.count()} kebele tenants")
    
    for kebele in kebeles:
        print(f"\n📍 Kebele: {kebele.name} (ID: {kebele.id})")
        
        # Check if it has workflow config
        try:
            if hasattr(kebele, 'workflow_config') and kebele.workflow_config:
                config = kebele.workflow_config
                print(f"   ✅ Has workflow config: {config.workflow_type}")
                print(f"   📋 Config details:")
                print(f"      - ID Card Processing: {config.id_card_processing}")
                print(f"      - Citizen Registration: {config.citizen_registration}")
            else:
                print(f"   ❌ No workflow config found")
                
                # Create default centralized config
                print(f"   🔧 Creating default centralized workflow config...")
                config = TenantWorkflowConfig.objects.create(
                    tenant=kebele,
                    workflow_type='centralized'
                )
                print(f"   ✅ Created centralized config")
                
        except Exception as e:
            print(f"   ❌ Error checking workflow config: {e}")

def set_kebele_autonomous(kebele_id):
    """Set a specific kebele to autonomous workflow."""
    try:
        kebele = Tenant.objects.get(id=kebele_id, type='kebele')
        print(f"🔧 Setting {kebele.name} to autonomous workflow...")
        
        # Get or create workflow config
        config, created = TenantWorkflowConfig.objects.get_or_create(
            tenant=kebele,
            defaults={'workflow_type': 'autonomous'}
        )
        
        if not created:
            config.workflow_type = 'autonomous'
            config.id_card_processing = {
                'can_print_locally': True,
                'requires_higher_approval': False,
                'approval_levels': ['kebele_leader'],
                'printing_authority': 'kebele',
                'quality_control': 'local'
            }
            config.save()
            
        print(f"✅ {kebele.name} is now set to autonomous workflow")
        return True
        
    except Tenant.DoesNotExist:
        print(f"❌ Kebele with ID {kebele_id} not found")
        return False
    except Exception as e:
        print(f"❌ Error setting autonomous workflow: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Set specific kebele to autonomous
        kebele_id = int(sys.argv[1])
        set_kebele_autonomous(kebele_id)
    else:
        # Check all kebeles
        check_kebele_workflow()
