# GoID Complete Production Deployment Guide

This guide covers the complete deployment of the GoID system with distributed biometric architecture.

## System Architecture Overview

### Centralized Server (Production Server)
```
┌─────────────────────────────────────────────────────────────┐
│                    Production Server                        │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Frontend  │  │   Backend   │  │     Database        │ │
│  │   (React)   │  │  (Django)   │  │   (PostgreSQL)      │ │
│  │   Port 3000 │  │  Port 8000  │  │     Port 5432       │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
│                           │                                 │
│                    ┌─────────────┐                         │
│                    │  FMatcher   │                         │
│                    │ (Duplicate  │                         │
│                    │ Detection)  │                         │
│                    └─────────────┘                         │
└─────────────────────────────────────────────────────────────┘
```

### Distributed Clients (Kebele Offices)
```
┌─────────────────────────────────────────────────────────────┐
│                    Kebele Office PC                         │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Browser   │  │ Biometric   │  │    USB Device       │ │
│  │             │  │  Service    │  │   Futronic FS88H    │ │
│  │ Server:3000 │  │localhost:8002│  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
│         │                 │                   │            │
│         └─────────────────┼───────────────────┘            │
│                           │                                │
│                    Local Communication                     │
└─────────────────────────────────────────────────────────────┘
                             │
                    ┌─────────────────┐
                    │ Internet/Network│
                    └─────────────────┘
                             │
                    ┌─────────────────┐
                    │ Production Server│
                    └─────────────────┘
```

## Deployment Process

### Phase 1: Server Deployment

#### 1. Build and Push Server Images
```bash
# Build all server services
./deployment/build.sh --username your-dockerhub-username

# This creates and pushes:
# - your-dockerhub-username/goid-backend:1.0.0
# - your-dockerhub-username/goid-frontend:1.0.0
```

#### 2. Deploy to Production Server
```bash
# Generate configuration
./deployment/deploy.sh generate

# Edit deployment/.env.production with your settings:
# - DOCKER_HUB_USERNAME=your-dockerhub-username
# - DB_PASSWORD=secure-password
# - SECRET_KEY=your-secret-key
# - ALLOWED_HOSTS=your-domain.com

# Deploy services
./deployment/deploy.sh deploy
```

#### 3. Verify Server Deployment
```bash
# Check service status
./deployment/deploy.sh status

# Test endpoints
curl http://your-server:8000/api/health/
curl http://your-server:3000/
```

### Phase 2: Client Installation

#### 1. Build Client Installer Package
```bash
cd deployment/client-biometric
python build_installer.py

# This creates:
# - dist/GoID-Biometric-Service-Installer.zip
# - dist/GoID-Biometric-Service-Installer.exe (if PyInstaller available)
```

#### 2. Distribute to Kebele Offices
- Copy installer package to each kebele office
- Ensure each PC has Python 3.10+ and Java installed
- Connect Futronic FS88H device via USB

#### 3. Install on Each Client Machine
```bash
# Extract installer package
# Run install.bat or install.exe
# Follow installation wizard
# Configure server URL and kebele information
```

#### 4. Verify Client Installation
- Check system tray for biometric service icon
- Test device connection
- Verify communication with server

## Configuration Details

### Server Configuration (.env.production)
```env
# Docker Hub
DOCKER_HUB_USERNAME=your-dockerhub-username

# Service Versions
BACKEND_VERSION=1.0.0
FRONTEND_VERSION=1.0.0

# Database
DB_PASSWORD=your-secure-db-password

# Application
SECRET_KEY=your-very-secure-secret-key
ALLOWED_HOSTS=your-domain.com,your-ip-address
CORS_ALLOWED_ORIGINS=http://your-domain.com:3000

# API URLs
REACT_APP_API_URL=http://your-domain.com:8000
```

### Client Configuration (config.json)
```json
{
  "server": {
    "url": "http://your-production-server:8000"
  },
  "service": {
    "port": 8002
  },
  "client_info": {
    "kebele_name": "Kebele 01",
    "subcity_name": "Subcity Name",
    "city_name": "City Name",
    "client_id": "kebele_001"
  }
}
```

## Communication Flow

### Fingerprint Capture Workflow
1. **User Action**: Clerk clicks "Capture Fingerprint" in browser
2. **Frontend Request**: Browser calls server API
3. **Server Processing**: Django backend calls localhost:8002 on client
4. **Local Capture**: Biometric service captures from USB device
5. **Template Return**: ANSI/ISO template sent to server
6. **Duplicate Check**: Server runs FMatcher against all stored templates
7. **Storage**: If no duplicates, template stored in database

### Network Communication
```
Client Browser → Production Server (HTTP/HTTPS)
     ↓
Client Browser → localhost:8002 (Local HTTP)
     ↓
USB Device (Direct communication)
```

## Security Considerations

### Server Security
- Use HTTPS in production
- Strong database passwords
- Firewall configuration
- Regular security updates

### Client Security
- Local biometric service on localhost only
- No external network access for biometric service
- USB device physical security
- Regular client updates

## Troubleshooting

### Server Issues
```bash
# Check service logs
./deployment/deploy.sh logs --service backend

# Check database connectivity
./deployment/deploy.sh logs --service db

# Restart services
./deployment/deploy.sh restart
```

### Client Issues
```bash
# Check biometric service status
# Right-click system tray icon → Status

# Test device connection
# Right-click system tray icon → Test Device

# View service logs
# Check biometric_service.log in installation directory
```

### Common Problems

#### Server Won't Start
- Check Docker Hub credentials
- Verify environment configuration
- Check port availability
- Review service logs

#### Client Can't Connect to Server
- Verify server URL in client config
- Check network connectivity
- Verify firewall settings
- Test server endpoints manually

#### Biometric Device Not Working
- Check USB connection
- Verify device drivers
- Test with different USB port
- Check device permissions

#### Duplicate Detection Issues
- Verify FMatcher is working on server
- Check template format compatibility
- Review duplicate detection logs
- Test with known duplicates

## Maintenance

### Server Maintenance
```bash
# Update services
./deployment/build.sh --username your-dockerhub-username
./deployment/deploy.sh deploy

# Backup database
docker-compose exec db pg_dump -U goid_user goid_db > backup.sql

# Monitor resources
./deployment/deploy.sh status
```

### Client Maintenance
- Regular biometric service updates
- Device driver updates
- System tray monitoring
- Log file rotation

## Support and Monitoring

### Health Checks
- Server: `http://your-server:8000/api/health/`
- Client: `http://localhost:8002/api/health` (on each client)

### Log Locations
- Server: Docker container logs
- Client: Installation directory logs

### Performance Monitoring
- Server resource usage
- Database performance
- Network latency
- Fingerprint capture times

This distributed architecture ensures optimal performance with local USB device access while maintaining centralized data management and duplicate detection across all kebeles.
