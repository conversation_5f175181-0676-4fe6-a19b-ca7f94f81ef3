import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { usePermissions } from '../../hooks/usePermissions';
import { hasPermission, canManageUsersInTenant } from '../../utils/multiTenantRBAC';
import axios from 'axios';

const RBACUserManagement = ({ tenantType, currentUser }) => {
  const { user } = usePermissions();
  const [managedTenants, setManagedTenants] = useState([]);
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [createUserDialogOpen, setCreateUserDialogOpen] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state for new user
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    password2: '',
    role: '',
    phone_number: ''
  });

  // Available roles based on tenant type
  const getAvailableRoles = (targetTenantType) => {
    const rolesByTenantType = {
      city: [
        { value: 'city_admin', label: 'City Admin', permissions: ['view_child_subcities_data', 'create_subcity_users', 'view_city_reports'] },
        { value: 'city_system_admin', label: 'City System Admin', permissions: ['create_subcity_users'] }
      ],
      subcity: [
        { value: 'subcity_admin', label: 'Subcity Admin', permissions: ['view_child_kebeles_data', 'create_kebele_users'] },
        { value: 'subcity_system_admin', label: 'Subcity System Admin', permissions: ['create_kebele_users'] },
        { value: 'print_id_cards', label: 'Print ID Cards', permissions: ['print_id_cards', 'navigate_to_id_cards'] }
      ],
      kebele: [
        { value: 'kebele_leader', label: 'Kebele Leader', permissions: ['approve_id_cards', 'verify_documents'] },
        { value: 'clerk', label: 'Registration Clerk', permissions: ['register_citizens', 'generate_id_cards'] },
        { value: 'print_id_cards', label: 'Print ID Cards', permissions: ['print_id_cards', 'navigate_to_id_cards'] }
      ]
    };

    const availableRoles = rolesByTenantType[targetTenantType] || [];
    console.log(`🔍 RBAC: Available roles for ${targetTenantType}:`, availableRoles.map(r => r.label));
    return availableRoles;
  };

  // Check if current user can manage users in target tenant
  const canManageUsers = (targetTenant) => {
    if (!user || !targetTenant) return false;
    return canManageUsersInTenant(user, targetTenant);
  };

  // Fetch manageable tenants based on user permissions
  const fetchManageableTenants = async () => {
    try {
      setLoading(true);
      
      // Determine which tenants user can manage based on their level
      let endpoint = '';
      if (hasPermission(user, 'create_subcity_users')) {
        // City admin - can manage subcities
        endpoint = '/api/tenants/?type=subcity';
      } else if (hasPermission(user, 'create_kebele_users')) {
        // Subcity admin - can manage kebeles
        endpoint = '/api/tenants/?type=kebele';
      } else {
        setError('You do not have permission to manage users');
        return;
      }

      const response = await axios.get(endpoint);
      const tenants = response.data.results || response.data;
      
      // Filter tenants user can actually manage
      const manageableTenantsFiltered = tenants.filter(tenant => canManageUsers(tenant));
      
      setManagedTenants(manageableTenantsFiltered);
      
      console.log('🔍 RBAC: Manageable tenants:', manageableTenantsFiltered.length);
      
    } catch (error) {
      console.error('Failed to fetch manageable tenants:', error);
      setError('Failed to load manageable tenants');
    } finally {
      setLoading(false);
    }
  };

  // Fetch users for selected tenant
  const fetchTenantUsers = async (tenantId) => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/tenants/${tenantId}/users/`);
      setUsers(response.data.users || []);
    } catch (error) {
      console.error('Failed to fetch tenant users:', error);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  // Handle tenant selection
  const handleTenantSelect = (tenant) => {
    if (!canManageUsers(tenant)) {
      setError(`You don't have permission to manage users in ${tenant.name}`);
      return;
    }
    
    setSelectedTenant(tenant);
    fetchTenantUsers(tenant.id);
    setError('');
  };

  // Handle user creation
  const handleCreateUser = async () => {
    if (!selectedTenant) return;

    try {
      setLoading(true);
      
      // Validate required fields
      if (!newUser.username || !newUser.email || !newUser.password || !newUser.password2 || !newUser.role) {
        setError('Please fill in all required fields');
        return;
      }

      // Validate password confirmation
      if (newUser.password !== newUser.password2) {
        setError('Passwords do not match');
        return;
      }

      const userData = {
        ...newUser,
        tenant: selectedTenant.id
      };

      console.log('🔍 RBAC: Creating user with data:', userData);

      await axios.post(`/api/tenants/${selectedTenant.id}/create_user/`, userData);

      // Refresh users list
      fetchTenantUsers(selectedTenant.id);

      // Reset form and close dialog
      setNewUser({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        password: '',
        password2: '',
        role: '',
        phone_number: ''
      });
      setCreateUserDialogOpen(false);
      setSuccess('User created successfully!');

    } catch (error) {
      console.error('Failed to create user:', error);
      setError(error.response?.data?.detail || 'Failed to create user');
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field, value) => {
    setNewUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Get role badge color
  const getRoleBadgeColor = (role) => {
    const colors = {
      'city_admin': 'error',
      'city_system_admin': 'warning',
      'subcity_admin': 'primary',
      'subcity_system_admin': 'secondary',
      'kebele_leader': 'secondary',
      'clerk': 'success',
      'print_id_cards': 'info'
    };
    return colors[role] || 'default';
  };

  useEffect(() => {
    if (user) {
      fetchManageableTenants();
    }
  }, [user]);

  if (!user) {
    return <CircularProgress />;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SecurityIcon />
        RBAC User Management
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Tenant Selection */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Select Tenant to Manage
        </Typography>
        
        <Grid container spacing={2}>
          {managedTenants.map((tenant) => (
            <Grid item xs={12} sm={6} md={4} key={tenant.id}>
              <Paper
                sx={{
                  p: 2,
                  cursor: 'pointer',
                  border: selectedTenant?.id === tenant.id ? 2 : 1,
                  borderColor: selectedTenant?.id === tenant.id ? 'primary.main' : 'divider',
                  '&:hover': { borderColor: 'primary.main' }
                }}
                onClick={() => handleTenantSelect(tenant)}
              >
                <Typography variant="h6">{tenant.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {tenant.type} • {tenant.schema_name}
                </Typography>
                <Chip
                  label={canManageUsers(tenant) ? 'Can Manage' : 'No Permission'}
                  color={canManageUsers(tenant) ? 'success' : 'error'}
                  size="small"
                  sx={{ mt: 1 }}
                />
              </Paper>
            </Grid>
          ))}
        </Grid>

        {managedTenants.length === 0 && !loading && (
          <Typography color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
            No manageable tenants found. Check your permissions.
          </Typography>
        )}
      </Paper>

      {/* Selected Tenant Management */}
      {selectedTenant && (
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5">
              Managing {selectedTenant.name} Users
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => fetchTenantUsers(selectedTenant.id)}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<PersonAddIcon />}
                onClick={() => setCreateUserDialogOpen(true)}
                disabled={!canManageUsers(selectedTenant)}
              >
                Add User
              </Button>
            </Box>
          </Box>

          {/* Users Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Permissions</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ mr: 2, bgcolor: 'secondary.main' }}>
                          {user.first_name?.[0] || user.username?.[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {user.first_name} {user.last_name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            @{user.username}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={user.role}
                        color={getRoleBadgeColor(user.role)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption">
                        {user.permissions?.length || 0} permissions
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Edit User">
                        <IconButton size="small">
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {users.length === 0 && !loading && (
            <Typography color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
              No users found in this tenant.
            </Typography>
          )}
        </Paper>
      )}

      {/* Create User Dialog */}
      <Dialog
        open={createUserDialogOpen}
        onClose={() => setCreateUserDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Add New User to {selectedTenant?.name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={newUser.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={newUser.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={newUser.first_name}
                onChange={(e) => handleInputChange('first_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={newUser.last_name}
                onChange={(e) => handleInputChange('last_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={newUser.phone_number}
                onChange={(e) => handleInputChange('phone_number', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Role</InputLabel>
                <Select
                  value={newUser.role}
                  label="Role"
                  onChange={(e) => handleInputChange('role', e.target.value)}
                >
                  {getAvailableRoles(selectedTenant?.type).map((role) => {
                    console.log(`🔍 RBAC: Rendering role option: ${role.label} (${role.value})`);
                    return (
                      <MenuItem key={role.value} value={role.value}>
                        {role.label}
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={newUser.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                required
                helperText="Minimum 8 characters"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Confirm Password"
                type="password"
                value={newUser.password2 || ''}
                onChange={(e) => handleInputChange('password2', e.target.value)}
                required
                error={newUser.password && newUser.password2 && newUser.password !== newUser.password2}
                helperText={newUser.password && newUser.password2 && newUser.password !== newUser.password2 ? "Passwords don't match" : ""}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateUserDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateUser}
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RBACUserManagement;
