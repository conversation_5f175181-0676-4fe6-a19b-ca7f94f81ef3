import React from 'react';
import { useLocalization } from '../../contexts/LocalizationContext';
import {
  Box,
  Typography,
  Paper,
  Alert,
  Chip
} from '@mui/material';
import {
  Business as BusinessIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import RBACUserManagement from '../../components/UserManagement/RBACUserManagement';
import { usePermissions } from '../../hooks/usePermissions';
import { hasPermission } from '../../utils/multiTenantRBAC';

const CityUserManagement = () => {
  const { t } = useLocalization();
  const { user, dynamicRole } = usePermissions();

  // Check if user has permission to manage subcity users
  const canManageSubcityUsers = hasPermission(user, 'create_subcity_users');

  if (!user) {
    return <div>Loading...</div>;
  }

  if (!canManageSubcityUsers) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <SecurityIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            You don't have permission to manage subcity users.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Required permission: <Chip label="create_subcity_users" size="small" />
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Your current role: <Chip label={dynamicRole} size="small" color="primary" />
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BusinessIcon />
          {t('city_user_management', 'City User Management')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('manage_users_for_subcities_under', 'Manage users for subcities under your city administration')}
        </Typography>
      </Box>

      {/* Permission Info */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>{t('city_admin_permissions', 'City Admin Permissions')}:</strong> {t('can_create_manage_users_subcities', 'You can create and manage users for subcities that belong to your city.')}
          {' '}{t('users_created_access_respective_subcity_tenants', 'Users created will have access to their respective subcity tenants.')}
        </Typography>
      </Alert>

      {/* User Management Component */}
      <RBACUserManagement 
        tenantType="subcity"
        currentUser={user}
      />

      {/* Help Section */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Available Roles for Subcity Users
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <Chip
            label="Subcity Admin"
            color="primary"
            variant="outlined"
            size="small"
          />
          <Chip
            label="Subcity System Admin"
            color="secondary"
            variant="outlined"
            size="small"
          />
          <Chip
            label="Print ID Cards"
            color="info"
            variant="outlined"
            size="small"
          />
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          • <strong>Subcity Admin:</strong> Can manage kebele users and approve ID cards<br/>
          • <strong>Subcity System Admin:</strong> Can create and manage users for child kebeles only<br/>
          • <strong>Print ID Cards:</strong> Can print ID cards in centralized workflow
        </Typography>
      </Paper>
    </Box>
  );
};

export default CityUserManagement;
