#!/usr/bin/env python3
"""
Add missing transfer and clearance permissions to kebele_leader group.

This script will:
1. Check current kebele_leader group permissions
2. Add missing transfer and clearance permissions
3. Verify the permissions are added correctly
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model

User = get_user_model()

def add_transfer_permissions():
    """Add missing transfer and clearance permissions to kebele_leader group."""
    
    print("🔧 Adding Transfer & Clearance Permissions to Kebele Leader")
    print("="*60)
    
    try:
        # Find kebele_leader group
        kebele_leader_group = Group.objects.filter(name='kebele_leader').first()
        if not kebele_leader_group:
            print("❌ kebele_leader group not found!")
            return
        
        print(f"✅ Found kebele_leader group (ID: {kebele_leader_group.id})")
        
        # Get content type for User model (commonly used for custom permissions)
        user_ct = ContentType.objects.get_for_model(User)
        
        # Define required permissions for transfer and clearance workflows
        required_permissions = [
            ('transfer_citizens', 'Can transfer citizens'),
            ('create_transfers', 'Can create transfer requests'),
            ('approve_transfer_requests', 'Can approve transfer requests'),
            ('view_transfers', 'Can view transfers'),
            ('create_clearances', 'Can create clearance requests'),
            ('view_clearances', 'Can view clearances'),
            ('manage_clearances', 'Can manage clearances'),
        ]
        
        print(f"\n📋 Current permissions ({kebele_leader_group.permissions.count()}):")
        current_perms = kebele_leader_group.permissions.all()
        for perm in current_perms:
            print(f"  • {perm.codename} - {perm.name}")
        
        print(f"\n🔧 Adding missing permissions...")
        
        added_count = 0
        for codename, name in required_permissions:
            # Create permission if it doesn't exist
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                defaults={
                    'name': name,
                    'content_type': user_ct
                }
            )
            
            if created:
                print(f"✅ Created permission: {codename}")
            
            # Add permission to group if not already present
            if not kebele_leader_group.permissions.filter(codename=codename).exists():
                kebele_leader_group.permissions.add(permission)
                print(f"✅ Added to group: {codename}")
                added_count += 1
            else:
                print(f"⏭️  Already exists: {codename}")
        
        print(f"\n📊 Summary:")
        print(f"  • Permissions added: {added_count}")
        print(f"  • Total permissions now: {kebele_leader_group.permissions.count()}")
        
        # Verify transfer permissions
        print(f"\n🔍 Verification - Transfer & Clearance permissions:")
        transfer_perms = kebele_leader_group.permissions.filter(codename__icontains='transfer')
        clearance_perms = kebele_leader_group.permissions.filter(codename__icontains='clearance')
        
        print(f"Transfer permissions ({transfer_perms.count()}):")
        for perm in transfer_perms:
            print(f"  ✅ {perm.codename}")
        
        print(f"Clearance permissions ({clearance_perms.count()}):")
        for perm in clearance_perms:
            print(f"  ✅ {perm.codename}")
        
        # Test with a kebele_leader user
        print(f"\n🧪 Testing with kebele_leader user...")
        kebele_leader_user = User.objects.filter(role='kebele_leader').first()
        if kebele_leader_user:
            print(f"Testing with: {kebele_leader_user.email}")
            
            # Check if user is in kebele_leader group
            is_in_group = kebele_leader_user.groups.filter(name='kebele_leader').exists()
            print(f"Is in kebele_leader group: {is_in_group}")
            
            if not is_in_group:
                print("⚠️ User not in kebele_leader group, adding...")
                kebele_leader_group.user_set.add(kebele_leader_user)
                print("✅ Added user to kebele_leader group")
            
            # Test specific permissions
            test_permissions = ['transfer_citizens', 'create_transfers', 'create_clearances']
            for perm in test_permissions:
                has_perm = kebele_leader_user.has_perm(f'users.{perm}')
                status = "✅" if has_perm else "❌"
                print(f"  {status} {perm}: {has_perm}")
        else:
            print("ℹ️ No kebele_leader users found to test")
        
        print(f"\n✅ Transfer & Clearance permissions setup complete!")
        print(f"\n🎯 Next Steps:")
        print("1. Clear browser cache and refresh page")
        print("2. Login as kebele_leader user")
        print("3. Go to citizen details page")
        print("4. Transfer and Clearance buttons should now be visible")
        
    except Exception as e:
        print(f"❌ Error adding transfer permissions: {e}")
        import traceback
        traceback.print_exc()

def test_citizen_detail_permissions():
    """Test the specific permission logic used in CitizenDetails.jsx"""
    print("\n🧪 Testing Citizen Detail Permission Logic")
    print("-" * 40)
    
    try:
        kebele_leader_user = User.objects.filter(role='kebele_leader').first()
        if not kebele_leader_user:
            print("❌ No kebele_leader user found")
            return
        
        print(f"Testing with: {kebele_leader_user.email}")
        print(f"User role: {kebele_leader_user.role}")
        print(f"User tenant: {kebele_leader_user.tenant}")
        print(f"User tenant type: {kebele_leader_user.tenant.type if kebele_leader_user.tenant else 'None'}")
        
        # Test the conditions from CitizenDetails.jsx
        # canInitiateTransfer() conditions:
        is_kebele_tenant = kebele_leader_user.tenant and kebele_leader_user.tenant.type == 'kebele'
        is_kebele_leader = kebele_leader_user.role == 'kebele_leader'
        has_transfer_permission = kebele_leader_user.has_perm('users.transfer_citizens')
        
        print(f"\nConditions for Transfer/Clearance buttons:")
        print(f"  • Is kebele tenant: {is_kebele_tenant}")
        print(f"  • Is kebele leader: {is_kebele_leader}")
        print(f"  • Has transfer_citizens permission: {has_transfer_permission}")
        print(f"  • Final result: {is_kebele_tenant and is_kebele_leader and has_transfer_permission}")
        
        if is_kebele_tenant and is_kebele_leader and has_transfer_permission:
            print("✅ Transfer and Clearance buttons should be visible!")
        else:
            print("❌ Transfer and Clearance buttons will NOT be visible")
            if not is_kebele_tenant:
                print("   Fix: User needs to be in a kebele tenant")
            if not is_kebele_leader:
                print("   Fix: User needs kebele_leader role")
            if not has_transfer_permission:
                print("   Fix: User needs transfer_citizens permission")
        
    except Exception as e:
        print(f"❌ Error testing permissions: {e}")

if __name__ == "__main__":
    add_transfer_permissions()
    test_citizen_detail_permissions()
