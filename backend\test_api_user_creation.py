#!/usr/bin/env python3
"""
Test API User Creation with Custom Groups

This script tests the actual API endpoint that the frontend uses
to create users with custom groups.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.db import connection
from tenants.models import Tenant
from users.models_groups import TenantGroup, GroupMembership
from django.contrib.auth.models import Group, Permission
import json

User = get_user_model()

def setup_test_environment():
    """Set up test environment with custom group"""
    print("🔧 Setting up test environment...")
    
    # Create test group in public schema
    connection.set_schema('public')
    
    # Create Django group
    test_group, created = Group.objects.get_or_create(name='api_test_group')
    if created:
        print(f"  ✅ Created Django group: {test_group.name}")
    
    # Create permissions
    from django.contrib.contenttypes.models import ContentType
    user_ct = ContentType.objects.get_for_model(User)
    
    perm, created = Permission.objects.get_or_create(
        codename='api_test_permission',
        content_type=user_ct,
        defaults={'name': 'API Test Permission'}
    )
    test_group.permissions.add(perm)
    
    # Create TenantGroup
    subcity_tenant = Tenant.objects.get(schema_name='subcity_zoble')
    tenant_group, created = TenantGroup.objects.get_or_create(
        group=test_group,
        tenant=subcity_tenant,
        defaults={
            'description': 'API test group',
            'group_type': 'custom',
            'level': 30,
            'is_system_group': False,
            'is_active': True
        }
    )
    
    print(f"  ✅ TenantGroup ID: {tenant_group.id}")
    return tenant_group

def test_api_user_creation():
    """Test user creation via API with custom group"""
    print("\n🧪 Testing API User Creation...")
    
    # Setup
    tenant_group = setup_test_environment()
    subcity_tenant = Tenant.objects.get(schema_name='subcity_zoble')
    
    # Clean up any existing test user
    connection.set_schema('subcity_zoble')
    User.objects.filter(email='<EMAIL>').delete()
    
    # Create a test client
    client = Client()
    
    # Login as Barbara (subcity admin) to get authentication
    connection.set_schema('subcity_zoble')
    barbara = User.objects.get(email='<EMAIL>')
    client.force_login(barbara)
    
    # Prepare API request data
    api_data = {
        'email': '<EMAIL>',
        'username': 'api_test_user',
        'password': 'testpass123',
        'password2': 'testpass123',
        'first_name': 'API',
        'last_name': 'Test',
        'role': 'subcity_admin',  # This should be cleared
        'primary_group_id': tenant_group.id,  # Custom group
        'phone_number': '1234567890'
    }
    
    print(f"  🔍 API Request Data: {api_data}")
    print(f"  🔍 Target endpoint: /api/tenants/{subcity_tenant.id}/create_user/")
    
    # Make API request
    response = client.post(
        f'/api/tenants/{subcity_tenant.id}/create_user/',
        data=json.dumps(api_data),
        content_type='application/json'
    )
    
    print(f"  🔍 API Response Status: {response.status_code}")
    
    if response.status_code == 201:
        response_data = response.json()
        print(f"  ✅ User created successfully")
        print(f"  🔍 Response role: {response_data.get('role')}")
        
        # Check the actual user in database
        connection.set_schema('subcity_zoble')
        user = User.objects.get(email='<EMAIL>')
        print(f"  🔍 Database user role: {user.role}")
        print(f"  🔍 Database user tenant: {user.tenant.name}")
        
        # Check group membership
        connection.set_schema('public')
        memberships = GroupMembership.objects.filter(
            user_email=user.email,
            is_active=True
        )
        print(f"  🔍 Group memberships: {memberships.count()}")
        for membership in memberships:
            print(f"    - Group: {membership.group.group.name}")
            print(f"    - Primary: {membership.is_primary}")
        
        # Check permissions
        connection.set_schema('subcity_zoble')
        user_permissions = user.get_all_permissions()
        print(f"  🔍 User permissions: {list(user_permissions)}")
        
        # Test specific permission
        has_test_perm = user.has_perm('users.api_test_permission')
        print(f"  🔍 Has api_test_permission: {has_test_perm}")
        
        return user.role is None and memberships.count() > 0
        
    else:
        print(f"  ❌ API request failed: {response.content}")
        return False

def test_frontend_user_creation():
    """Test the exact flow that frontend would use"""
    print("\n🌐 Testing Frontend User Creation Flow...")
    
    # This simulates what the frontend RBACUserManagement component would do
    
    # 1. Frontend gets available groups
    connection.set_schema('public')
    available_groups = TenantGroup.objects.filter(
        tenant__schema_name='subcity_zoble',
        is_active=True
    )
    print(f"  🔍 Available groups: {[g.group.name for g in available_groups]}")
    
    # 2. Frontend selects a custom group
    selected_group = available_groups.first()
    print(f"  🔍 Selected group: {selected_group.group.name} (ID: {selected_group.id})")
    
    # 3. Frontend submits user creation form
    frontend_data = {
        'username': 'frontend_test_user',
        'email': '<EMAIL>',
        'first_name': 'Frontend',
        'last_name': 'Test',
        'password': 'testpass123',
        'password2': 'testpass123',
        'role': 'subcity_admin',  # This gets sent but should be ignored
        'phone_number': '1234567890',
        'primary_group_id': selected_group.id  # Custom group selection
    }
    
    print(f"  🔍 Frontend form data: {frontend_data}")
    
    # 4. Test the API call
    client = Client()
    connection.set_schema('subcity_zoble')
    barbara = User.objects.get(email='<EMAIL>')
    client.force_login(barbara)
    
    # Clean up
    User.objects.filter(email='<EMAIL>').delete()
    
    subcity_tenant = Tenant.objects.get(schema_name='subcity_zoble')
    response = client.post(
        f'/api/tenants/{subcity_tenant.id}/create_user/',
        data=json.dumps(frontend_data),
        content_type='application/json'
    )
    
    print(f"  🔍 Frontend API Response: {response.status_code}")
    
    if response.status_code == 201:
        # Check result
        user = User.objects.get(email='<EMAIL>')
        print(f"  ✅ Frontend user created")
        print(f"  🔍 User role: {user.role} (should be None)")
        print(f"  🔍 User has custom group: {user.role is None}")
        
        return user.role is None
    else:
        print(f"  ❌ Frontend API failed: {response.content}")
        return False

def cleanup():
    """Clean up test data"""
    print("\n🧹 Cleaning up...")
    
    try:
        # Delete test users
        connection.set_schema('subcity_zoble')
        User.objects.filter(email__in=[
            '<EMAIL>',
            '<EMAIL>'
        ]).delete()
        
        # Delete test group data
        connection.set_schema('public')
        GroupMembership.objects.filter(
            group__group__name='api_test_group'
        ).delete()
        TenantGroup.objects.filter(group__name='api_test_group').delete()
        Group.objects.filter(name='api_test_group').delete()
        
        print("  ✅ Cleanup completed")
        
    except Exception as e:
        print(f"  ⚠️ Cleanup error: {e}")

def main():
    """Main test function"""
    print("🎯 API User Creation Test")
    print("Testing group-based user creation via API")
    print("=" * 60)
    
    try:
        # Run tests
        test1_passed = test_api_user_creation()
        test2_passed = test_frontend_user_creation()
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 TEST RESULTS:")
        print(f"  API user creation: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"  Frontend flow test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
        
        if test1_passed and test2_passed:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ API correctly handles custom group assignment")
            print("✅ Static roles are cleared for group-based users")
        else:
            print("\n❌ SOME TESTS FAILED!")
            print("❌ Static role assignment issue still exists")
            
    finally:
        cleanup()

if __name__ == "__main__":
    main()
