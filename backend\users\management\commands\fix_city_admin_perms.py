from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant

User = get_user_model()

class Command(BaseCommand):
    help = 'Fix city admin permissions for create_subcity_users'

    def handle(self, *args, **options):
        self.stdout.write('🔧 Fixing city admin permissions...')
        
        # Create permission in public schema
        with schema_context(get_public_schema_name()):
            permission, created = Permission.objects.get_or_create(
                codename='create_subcity_users',
                defaults={
                    'name': 'Can create subcity users',
                    'content_type_id': 1
                }
            )
            self.stdout.write(f'Permission: {permission.codename} ({"created" if created else "exists"})')
            
            # Create city_admin group
            city_admin_group, created = Group.objects.get_or_create(name='city_admin')
            city_admin_group.permissions.add(permission)
            self.stdout.write(f'Group: {city_admin_group.name} with permission added')

        # Process each city tenant
        for tenant in Tenant.objects.filter(type='city'):
            self.stdout.write(f'Processing {tenant.name}...')
            
            with schema_context(tenant.schema_name):
                # Ensure the group exists in this schema
                city_admin_group, created = Group.objects.get_or_create(name='city_admin')
                if created:
                    # Add permission to the group in this schema
                    try:
                        permission = Permission.objects.get(codename='create_subcity_users')
                        city_admin_group.permissions.add(permission)
                    except Permission.DoesNotExist:
                        # Create permission in this schema if it doesn't exist
                        permission = Permission.objects.create(
                            codename='create_subcity_users',
                            name='Can create subcity users',
                            content_type_id=1
                        )
                        city_admin_group.permissions.add(permission)
                
                # Add city admins to the group
                for admin in User.objects.filter(role='city_admin'):
                    admin.groups.add(city_admin_group)
                    self.stdout.write(f'  ✅ Added {admin.email} to city_admin group')
                    
                    # Verify permission
                    has_perm = admin.user_permissions.filter(codename='create_subcity_users').exists() or \
                              admin.groups.filter(permissions__codename='create_subcity_users').exists()
                    self.stdout.write(f'     Has permission: {has_perm}')

        self.stdout.write('✅ Done!')
