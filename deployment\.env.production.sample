# GoID Production Environment Configuration

# Docker Hub Configuration
DOCKER_HUB_USERNAME=aragawmebratu

# Service Versions (will be auto-populated from versions.json)
BACKEND_VERSION=latest
FRONTEND_VERSION=latest
BIOMETRIC_VERSION=latest

# Database Configuration
DB_PASSWORD=your-secure-db-password
DB_PORT=5432

# Redis Configuration
REDIS_PORT=6379

# Application Configuration (CHANGE THESE IN PRODUCTION!)
SECRET_KEY=@w8^2oo@bz$mqnl-=bcs5t^2cj)b_3l4g8j6*p0fu@w(7=^_a#
ALLOWED_HOSTS=************,goid.uog.edu.et,localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://************:3000,https://goid.uog.edu.et,http://goid.uog.edu.et:3000

# API URLs
REACT_APP_API_URL=http://************:8000
REACT_APP_BIOMETRIC_SERVICE_URL=http://localhost:8002

# Admin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=5050
