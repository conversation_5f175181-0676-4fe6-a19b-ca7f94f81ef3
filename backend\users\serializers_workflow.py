"""
Workflow Management Serializers

Serializers for workflow switching and group management operations.
"""

from rest_framework import serializers
from tenants.models import Tenant
from users.models import User


class WorkflowSwitchSerializer(serializers.Serializer):
    """Serializer for switching tenant workflow."""
    
    tenant_id = serializers.IntegerField()
    workflow_type = serializers.ChoiceField(choices=['centralized', 'autonomous'])
    reason = serializers.CharField(max_length=500, required=False, default='')
    
    def validate_tenant_id(self, value):
        """Validate that tenant exists."""
        try:
            tenant = Tenant.objects.get(id=value)
            return value
        except Tenant.DoesNotExist:
            raise serializers.ValidationError("Tenant not found")
    
    def validate(self, data):
        """Additional validation for workflow switch."""
        tenant_id = data['tenant_id']
        workflow_type = data['workflow_type']
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            raise serializers.ValidationError("Tenant not found")
        
        # Autonomous workflow is only for kebeles
        if workflow_type == 'autonomous' and tenant.type != 'kebele':
            raise serializers.ValidationError(
                "Autonomous workflow is only available for kebele tenants"
            )
        
        return data


class DesignatedPrinterSerializer(serializers.Serializer):
    """Serializer for assigning designated printer role."""
    
    tenant_id = serializers.IntegerField()
    user_email = serializers.EmailField()
    
    def validate(self, data):
        """Validate tenant and user."""
        tenant_id = data['tenant_id']
        user_email = data['user_email']
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            raise serializers.ValidationError("Tenant not found")
        
        # Check if user exists in tenant
        try:
            from django_tenants.utils import schema_context
            with schema_context(tenant.schema_name):
                user = User.objects.get(email=user_email, tenant=tenant)
        except User.DoesNotExist:
            raise serializers.ValidationError(
                f"User {user_email} not found in tenant {tenant.name}"
            )
        
        return data


class UserWorkflowAssignmentSerializer(serializers.Serializer):
    """Serializer for user workflow group assignments."""
    
    email = serializers.EmailField()
    role = serializers.CharField()
    primary_group = serializers.CharField(allow_null=True)
    workflow_aligned = serializers.BooleanField()


class WorkflowStatusSerializer(serializers.Serializer):
    """Serializer for workflow status information."""
    
    tenant = serializers.CharField()
    workflow_type = serializers.ChoiceField(choices=['centralized', 'autonomous'])
    users = UserWorkflowAssignmentSerializer(many=True)
    total_users = serializers.IntegerField()
    aligned_users = serializers.IntegerField()
    
    def to_representation(self, instance):
        """Add computed fields."""
        data = super().to_representation(instance)
        
        # Add alignment percentage
        total = data['total_users']
        aligned = data['aligned_users']
        data['alignment_percentage'] = round((aligned / total * 100) if total > 0 else 100, 1)
        
        # Add workflow description
        workflow_descriptions = {
            'centralized': 'Standard hierarchical workflow with subcity approval and printing',
            'autonomous': 'Self-sufficient kebele handling all processes locally'
        }
        data['workflow_description'] = workflow_descriptions.get(
            data['workflow_type'], 
            'Unknown workflow type'
        )
        
        return data


class WorkflowImpactSerializer(serializers.Serializer):
    """Serializer for workflow impact analysis."""
    
    tenant = serializers.CharField()
    current_workflow = serializers.ChoiceField(choices=['centralized', 'autonomous'])
    target_workflow = serializers.ChoiceField(choices=['centralized', 'autonomous'])
    requires_change = serializers.BooleanField()
    pending_id_cards = serializers.IntegerField()
    approved_id_cards = serializers.IntegerField()
    user_count_by_role = serializers.DictField()
    changes_needed = serializers.ListField(child=serializers.CharField())
    estimated_impact = serializers.ChoiceField(choices=['Low', 'Medium', 'High'])
    
    def to_representation(self, instance):
        """Add additional impact analysis."""
        data = super().to_representation(instance)
        
        # Add recommendations
        if data['target_workflow'] == 'autonomous':
            data['recommendations'] = [
                'Ensure kebele has reliable internet connection',
                'Install ID card printing equipment',
                'Train kebele leader on approval process',
                'Set up local security pattern application',
                'Establish backup procedures'
            ]
        else:  # centralized
            data['recommendations'] = [
                'Coordinate with subcity administration',
                'Update staff on submission procedures',
                'Ensure proper communication channels',
                'Review pending approvals before switch'
            ]
        
        # Add timeline estimate
        pending_cards = data['pending_id_cards']
        if pending_cards > 20:
            data['estimated_timeline'] = '2-3 weeks'
        elif pending_cards > 5:
            data['estimated_timeline'] = '1-2 weeks'
        else:
            data['estimated_timeline'] = '1-3 days'
        
        return data


class WorkflowConfigurationSerializer(serializers.Serializer):
    """Serializer for workflow configuration details."""
    
    workflow_type = serializers.ChoiceField(choices=['centralized', 'autonomous'])
    id_card_processing = serializers.DictField()
    citizen_registration = serializers.DictField()
    approval_workflow = serializers.DictField()
    document_verification = serializers.DictField()
    
    def to_representation(self, instance):
        """Format configuration for display."""
        data = super().to_representation(instance)
        
        # Add human-readable descriptions
        id_card_config = data.get('id_card_processing', {})
        data['capabilities'] = {
            'local_printing': id_card_config.get('can_print_locally', False),
            'requires_higher_approval': id_card_config.get('requires_higher_approval', True),
            'printing_authority': id_card_config.get('printing_authority', 'subcity'),
            'approval_levels': id_card_config.get('approval_levels', [])
        }
        
        return data


class WorkflowSwitchResultSerializer(serializers.Serializer):
    """Serializer for workflow switch results."""
    
    success = serializers.BooleanField()
    message = serializers.CharField()
    old_workflow = serializers.CharField(required=False)
    new_workflow = serializers.CharField(required=False)
    affected_users = serializers.IntegerField(required=False)
    changes = serializers.ListField(required=False)
    
    def to_representation(self, instance):
        """Add summary information."""
        data = super().to_representation(instance)
        
        if data.get('success') and data.get('changes'):
            # Summarize changes
            changes = data['changes']
            added_to_groups = len([c for c in changes if c.get('action') == 'added_to_group'])
            removed_from_groups = len([c for c in changes if c.get('action') == 'removed_from_group'])
            
            data['change_summary'] = {
                'users_updated': data.get('affected_users', 0),
                'group_assignments_added': added_to_groups,
                'group_assignments_removed': removed_from_groups
            }
        
        return data
