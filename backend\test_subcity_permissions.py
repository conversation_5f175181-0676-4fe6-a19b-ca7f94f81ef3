#!/usr/bin/env python
"""
Test script to check subcity admin permissions.
Run this with: docker-compose -f docker-compose.production.yml exec backend python test_subcity_permissions.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group, Permission
from django_tenants.utils import schema_context, get_public_schema_name
from users.models import User
from tenants.models import Tenant


def test_permissions():
    print("🔍 Testing subcity admin permissions...")
    print("="*50)
    
    # Check if required permissions exist
    print("1. Checking required permissions...")
    required_perms = ['manage_users', 'manage_citizens', 'manage_idcards']
    for perm in required_perms:
        try:
            permission = Permission.objects.get(codename=perm)
            print(f"  ✅ {perm}: {permission.name}")
        except Permission.DoesNotExist:
            print(f"  ❌ {perm}: NOT FOUND")
    
    print()
    
    # Check if SubCity Administrators group exists
    print("2. Checking SubCity Administrators group...")
    with schema_context(get_public_schema_name()):
        try:
            group = Group.objects.get(name='SubCity Administrators')
            print(f"  ✅ Group exists: {group.name}")
            
            # Check group permissions
            group_perms = group.permissions.all()
            print(f"  📋 Group has {group_perms.count()} permissions:")
            for perm in group_perms:
                print(f"    - {perm.codename}")
            
            # Check group members
            members = group.user_set.all()
            print(f"  👥 Group has {members.count()} members:")
            for member in members:
                print(f"    - {member.email} (role: {member.role})")
                
        except Group.DoesNotExist:
            print("  ❌ SubCity Administrators group NOT FOUND")
            return False
    
    print()
    
    # Find subcity admin users
    print("3. Finding subcity admin users...")
    subcity_users = []
    
    # Check public schema
    with schema_context(get_public_schema_name()):
        public_users = User.objects.filter(role='subcity_admin', is_active=True)
        subcity_users.extend([(user, 'public') for user in public_users])
    
    # Check tenant schemas
    for tenant in Tenant.objects.all():
        try:
            with schema_context(tenant.schema_name):
                tenant_users = User.objects.filter(role='subcity_admin', is_active=True)
                subcity_users.extend([(user, tenant.name) for user in tenant_users])
        except Exception as e:
            print(f"  ⚠️ Error checking {tenant.name}: {e}")
    
    print(f"  Found {len(subcity_users)} subcity admin users:")
    for user, location in subcity_users:
        print(f"    - {user.email} (in {location})")
    
    print()
    
    # Test specific user permissions if any found
    if subcity_users:
        print("4. Testing user permissions...")
        test_user, location = subcity_users[0]
        print(f"  Testing user: {test_user.email}")
        
        with schema_context(get_public_schema_name()):
            # Check if user is in the group
            is_in_group = test_user.groups.filter(name='SubCity Administrators').exists()
            print(f"  👥 In SubCity Administrators group: {is_in_group}")
            
            # Test specific permissions
            test_perms = ['manage_users', 'manage_citizens', 'manage_idcards']
            for perm in test_perms:
                has_perm = test_user.has_group_permission(perm)
                print(f"  🔑 {perm}: {has_perm}")
    
    print()
    print("="*50)
    print("✅ Permission test completed!")
    
    return True


def fix_permissions():
    """Fix permissions if needed."""
    print("🔧 Fixing permissions...")
    
    with schema_context(get_public_schema_name()):
        # Ensure group exists
        group, created = Group.objects.get_or_create(name='SubCity Administrators')
        if created:
            print("  ✅ Created SubCity Administrators group")
        
        # Add required permissions
        required_perms = ['manage_users', 'manage_citizens', 'manage_idcards']
        for perm_code in required_perms:
            try:
                permission = Permission.objects.get(codename=perm_code)
                group.permissions.add(permission)
                print(f"  ➕ Added permission: {perm_code}")
            except Permission.DoesNotExist:
                print(f"  ❌ Permission not found: {perm_code}")
        
        # Find and assign subcity admin users
        all_subcity_users = []
        
        # Public schema users
        public_users = User.objects.filter(role='subcity_admin', is_active=True)
        all_subcity_users.extend(public_users)
        
        # Tenant schema users
        for tenant in Tenant.objects.all():
            try:
                with schema_context(tenant.schema_name):
                    tenant_users = User.objects.filter(role='subcity_admin', is_active=True)
                    all_subcity_users.extend(tenant_users)
            except:
                pass
        
        # Assign users to group
        assigned_count = 0
        for user in all_subcity_users:
            group.user_set.add(user)
            assigned_count += 1
            print(f"  👤 Assigned {user.email} to group")
        
        print(f"  📊 Assigned {assigned_count} users to group")
    
    print("✅ Fix completed!")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--fix':
        fix_permissions()
    else:
        test_permissions()
