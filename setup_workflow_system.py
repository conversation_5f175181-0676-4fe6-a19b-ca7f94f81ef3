#!/usr/bin/env python3
"""
Quick setup script for the workflow switching system.

This script sets up the complete workflow switching system including:
1. RBAC groups and permissions
2. Workflow configurations for existing kebeles
3. Test data for demonstration
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.core.management import call_command
from django.contrib.auth.models import Group
from tenants.models import Tenant, TenantWorkflowConfig
from tenants.models.kebele import Kebele

def main():
    print("🚀 Setting up Workflow Switching System")
    print("="*50)
    
    try:
        # Step 1: Set up RBAC system
        print("\n📋 Step 1: Setting up RBAC system...")
        call_command('setup_workflow_rbac', '--force')
        print("✅ RBAC system setup completed")
        
        # Step 2: Configure existing kebeles
        print("\n📋 Step 2: Configuring existing kebeles...")
        kebeles = Kebele.objects.all()
        configured_count = 0
        
        for kebele in kebeles:
            # Create workflow configuration if it doesn't exist
            workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                tenant=kebele.tenant,
                defaults={'workflow_type': 'centralized'}
            )
            
            if created:
                print(f"   ✅ Configured {kebele.name} with centralized workflow")
                configured_count += 1
            else:
                print(f"   ⏭️  {kebele.name} already configured ({workflow_config.workflow_type})")
        
        print(f"✅ Configured {configured_count} new kebeles")
        
        # Step 3: Verify setup
        print("\n📋 Step 3: Verifying setup...")
        
        # Check groups
        total_groups = Group.objects.count()
        kebele_groups = Group.objects.filter(name__contains='kebele').count()
        print(f"   📊 Total groups: {total_groups}")
        print(f"   📊 Kebele-related groups: {kebele_groups}")
        
        # Check workflow configurations
        total_configs = TenantWorkflowConfig.objects.count()
        autonomous_configs = TenantWorkflowConfig.objects.filter(workflow_type='autonomous').count()
        centralized_configs = TenantWorkflowConfig.objects.filter(workflow_type='centralized').count()
        
        print(f"   📊 Total workflow configurations: {total_configs}")
        print(f"   📊 Autonomous workflows: {autonomous_configs}")
        print(f"   📊 Centralized workflows: {centralized_configs}")
        
        # Step 4: Display summary
        print("\n📋 Step 4: Setup Summary")
        print("="*50)
        print("✅ Workflow switching system is ready!")
        print("\n🎯 Next Steps:")
        print("1. Access the Kebele User Management page as a subcity admin")
        print("2. Look for the 'Workflow Management' section")
        print("3. Click 'Switch Workflow' on any kebele card")
        print("4. Test switching between centralized and autonomous workflows")
        
        print("\n📚 Documentation:")
        print("- See WORKFLOW_SWITCHING_GUIDE.md for detailed information")
        print("- Run test_workflow_switching.py to verify functionality")
        
        print("\n🔧 Management Commands:")
        print("- python manage.py setup_workflow_rbac --help")
        print("- python manage.py switch_workflow --help")
        
    except Exception as e:
        print(f"❌ Error during setup: {str(e)}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure Django is properly configured")
        print("2. Check database connectivity")
        print("3. Verify all migrations are applied")
        print("4. Check the error message above for specific issues")
        sys.exit(1)

if __name__ == "__main__":
    main()
