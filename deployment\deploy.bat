@echo off
REM GoID Deployment Script for Windows - Deploy to production server

setlocal enabledelayedexpansion

REM Default values
set ACTION=deploy
set ENV_FILE=deployment\.env.production
set COMPOSE_FILE=
set SERVICES=
set SERVICE=
set FOLLOW=false

REM Parse action if provided
if not "%~1"=="" if not "%~1:~0,1%"=="-" (
    set ACTION=%~1
    shift
)

REM Parse command line arguments
:parse_args
if "%~1"=="" goto end_parse
if "%~1"=="-e" (
    set ENV_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--env-file" (
    set ENV_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-f" (
    set COMPOSE_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--compose-file" (
    set COMPOSE_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-s" (
    set SERVICES=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--services" (
    set SERVICES=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--service" (
    set SERVICE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--follow" (
    set FOLLOW=true
    shift
    goto parse_args
)
if "%~1"=="-h" goto show_usage
if "%~1"=="--help" goto show_usage

echo [ERROR] Unknown option: %~1
goto show_usage

:end_parse

REM Check if environment file exists for deploy action
if "%ACTION%"=="deploy" (
    if not exist "%ENV_FILE%" (
        echo [WARNING] Environment file %ENV_FILE% not found
        echo [INFO] Creating sample environment file...
        
        REM Create sample environment file
        (
            echo # GoID Production Environment Configuration
            echo.
            echo # Docker Hub Configuration
            echo DOCKER_HUB_USERNAME=your-dockerhub-username
            echo.
            echo # Service Versions (will be auto-populated from versions.json^)
            echo BACKEND_VERSION=latest
            echo FRONTEND_VERSION=latest
            echo BIOMETRIC_VERSION=latest
            echo.
            echo # Database Configuration
            echo DB_PASSWORD=your-secure-db-password
            echo DB_PORT=5432
            echo.
            echo # Redis Configuration
            echo REDIS_PORT=6379
            echo.
            echo # Application Configuration
            echo SECRET_KEY=your-very-secure-secret-key-change-this-in-production
            echo ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
            echo CORS_ALLOWED_ORIGINS=http://localhost:3000,https://your-domain.com
            echo.
            echo # API URLs
            echo REACT_APP_API_URL=http://localhost:8000
            echo REACT_APP_BIOMETRIC_SERVICE_URL=http://localhost:8002
            echo.
            echo # Admin Configuration
            echo PGADMIN_EMAIL=<EMAIL>
            echo PGADMIN_PASSWORD=admin123
            echo PGADMIN_PORT=5050
        ) > "%ENV_FILE%"
        
        echo [WARNING] Please edit %ENV_FILE% with your configuration before deploying
        echo [INFO] Especially update: DOCKER_HUB_USERNAME, DB_PASSWORD, SECRET_KEY, ALLOWED_HOSTS
        exit /b 1
    )
)

if "%ACTION%"=="generate" (
    if not exist "%ENV_FILE%" (
        echo [WARNING] Environment file %ENV_FILE% not found
        echo [INFO] Creating sample environment file...
        
        REM Create sample environment file (same as above)
        (
            echo # GoID Production Environment Configuration
            echo.
            echo # Docker Hub Configuration
            echo DOCKER_HUB_USERNAME=your-dockerhub-username
            echo.
            echo # Service Versions
            echo BACKEND_VERSION=latest
            echo FRONTEND_VERSION=latest
            echo BIOMETRIC_VERSION=latest
            echo.
            echo # Database Configuration
            echo DB_PASSWORD=your-secure-db-password
            echo DB_PORT=5432
            echo.
            echo # Application Configuration
            echo SECRET_KEY=your-very-secure-secret-key-change-this-in-production
            echo ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
        ) > "%ENV_FILE%"
        
        echo [WARNING] Please edit %ENV_FILE% with your configuration before deploying
        exit /b 1
    )
)

REM Build deployment command
set DEPLOY_CMD=python deployment\deploy.py %ACTION%

if not "%ENV_FILE%"=="" (
    set DEPLOY_CMD=!DEPLOY_CMD! --env-file %ENV_FILE%
)

if not "%COMPOSE_FILE%"=="" (
    set DEPLOY_CMD=!DEPLOY_CMD! --compose-file %COMPOSE_FILE%
)

if not "%SERVICES%"=="" (
    set DEPLOY_CMD=!DEPLOY_CMD! --services %SERVICES%
)

if not "%SERVICE%"=="" (
    set DEPLOY_CMD=!DEPLOY_CMD! --service %SERVICE%
)

if "%FOLLOW%"=="true" (
    set DEPLOY_CMD=!DEPLOY_CMD! --follow
)

REM Execute action
echo [INFO] Executing: %ACTION%

if "%ACTION%"=="deploy" (
    echo [INFO] Deploying GoID services...
    if not "%SERVICES%"=="" (
        echo [INFO] Services: %SERVICES%
    ) else (
        echo [INFO] Services: all
    )
)
if "%ACTION%"=="stop" echo [INFO] Stopping GoID services...
if "%ACTION%"=="status" echo [INFO] Checking service status...
if "%ACTION%"=="logs" (
    if not "%SERVICE%"=="" (
        echo [INFO] Showing logs for: %SERVICE%
    ) else (
        echo [INFO] Showing logs for all services
    )
)
if "%ACTION%"=="pull" echo [INFO] Pulling latest images...
if "%ACTION%"=="generate" echo [INFO] Generating docker-compose file...

%DEPLOY_CMD%
if errorlevel 1 (
    echo [ERROR] Action failed!
    exit /b 1
)

if "%ACTION%"=="deploy" (
    echo [SUCCESS] Deployment completed successfully!
    echo [INFO] Services are starting up. Use '%0 status' to check status.
    echo [INFO] Use '%0 logs' to view logs.
)
if "%ACTION%"=="stop" echo [SUCCESS] Services stopped successfully!
if "%ACTION%"=="generate" echo [SUCCESS] Docker compose file generated successfully!

exit /b 0

:show_usage
echo Usage: %0 [ACTION] [OPTIONS]
echo.
echo Actions:
echo   deploy                  Deploy services (default^)
echo   stop                    Stop all services
echo   status                  Show service status
echo   logs                    Show service logs
echo   pull                    Pull latest images
echo   generate                Generate docker-compose file
echo.
echo Options:
echo   -e, --env-file FILE     Environment file [default: deployment\.env.production]
echo   -f, --compose-file FILE Docker compose file to use
echo   -s, --services SERVICES Specific services to deploy (space-separated^)
echo   --service SERVICE       Service for logs action
echo   --follow               Follow logs (for logs action^)
echo   -h, --help             Show this help message
echo.
echo Examples:
echo   %0                                    # Deploy all services
echo   %0 deploy -s "backend frontend"      # Deploy specific services
echo   %0 status                             # Show service status
echo   %0 logs --service backend --follow    # Follow backend logs
echo   %0 stop                               # Stop all services
exit /b 0
