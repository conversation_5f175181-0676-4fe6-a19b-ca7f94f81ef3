#!/usr/bin/env python3
"""
Fix print_id_cards role permissions for printing queue access.
This script adds the required permissions to users with print_id_cards role.
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from users.models import User
from tenants.models import Tenant
from django_tenants.utils import schema_context

def fix_print_id_cards_permissions():
    """Add required permissions for print_id_cards role users."""
    print("🔧 Fixing print_id_cards role permissions...")
    
    # Step 1: Find users with print_id_cards role
    print("\n1️⃣ Finding users with print_id_cards role...")
    
    # Check in public schema first
    print_users = User.objects.filter(role='print_id_cards')
    print(f"Found {print_users.count()} users with print_id_cards role in public schema")
    
    # Also check in tenant schemas
    tenants = Tenant.objects.exclude(schema_name='public')
    all_print_users = []
    
    for tenant in tenants:
        try:
            with schema_context(tenant.schema_name):
                tenant_print_users = User.objects.filter(role='print_id_cards')
                if tenant_print_users.exists():
                    print(f"Found {tenant_print_users.count()} print_id_cards users in {tenant.name} ({tenant.schema_name})")
                    for user in tenant_print_users:
                        all_print_users.append((user, tenant))
        except Exception as e:
            print(f"❌ Error checking {tenant.schema_name}: {e}")
    
    if not all_print_users:
        print("❌ No users with print_id_cards role found")
        return
    
    # Step 2: Get or create required permissions
    print("\n2️⃣ Setting up required permissions...")
    
    user_ct = ContentType.objects.get_for_model(User)
    
    # Required permissions for printing queue access
    required_permissions = [
        ('print_id_cards', 'Can print ID cards'),
        ('print_idcards', 'Can print ID cards (alt)'),
        ('view_printing_queue', 'Can view printing queue'),
        ('print_approved_id_cards', 'Can print approved ID cards'),
    ]
    
    permissions = []
    for codename, name in required_permissions:
        permission, created = Permission.objects.get_or_create(
            codename=codename,
            content_type=user_ct,
            defaults={'name': name}
        )
        permissions.append(permission)
        status = "✅ Created" if created else "✅ Found"
        print(f"  {status} permission: {codename}")
    
    # Step 3: Get or create print_id_cards group
    print("\n3️⃣ Setting up print_id_cards group...")
    
    print_group, created = Group.objects.get_or_create(name='print_id_cards')
    status = "✅ Created" if created else "✅ Found"
    print(f"  {status} group: print_id_cards")
    
    # Add permissions to group
    print("  Adding permissions to group...")
    for permission in permissions:
        print_group.permissions.add(permission)
        print(f"    ✅ Added: {permission.codename}")
    
    # Step 4: Add users to group and fix their permissions
    print("\n4️⃣ Adding users to print_id_cards group...")
    
    for user, tenant in all_print_users:
        try:
            with schema_context(tenant.schema_name):
                # Add user to print_id_cards group
                user.groups.add(print_group)
                
                # Also add permissions directly to user
                for permission in permissions:
                    user.user_permissions.add(permission)
                
                print(f"  ✅ Fixed permissions for: {user.email} in {tenant.name}")
                
        except Exception as e:
            print(f"  ❌ Error fixing {user.email}: {e}")
    
    print("\n✅ Permission fix completed!")
    
    # Step 5: Test permissions
    print("\n5️⃣ Testing permissions...")
    
    for user, tenant in all_print_users:
        try:
            with schema_context(tenant.schema_name):
                print(f"\nTesting {user.email} in {tenant.name}:")
                
                # Test group membership
                is_in_group = user.groups.filter(name='print_id_cards').exists()
                print(f"  📋 In print_id_cards group: {is_in_group}")
                
                # Test specific permissions
                test_permissions = ['print_id_cards', 'print_idcards', 'view_printing_queue']
                for perm in test_permissions:
                    has_perm = user.has_perm(f'users.{perm}')
                    status = "✅" if has_perm else "❌"
                    print(f"  {status} {perm}: {has_perm}")
                
        except Exception as e:
            print(f"  ❌ Error testing {user.email}: {e}")

if __name__ == '__main__':
    fix_print_id_cards_permissions()
