"""
Management command to fix subcity admin permissions.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django_tenants.utils import schema_context, get_public_schema_name
from users.models import User
from users.models_groups import GroupMembership
from tenants.models import Tenant


class Command(BaseCommand):
    help = 'Fix subcity admin permissions by ensuring they have required group-based permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-email',
            type=str,
            help='Specific user email to verify after fix',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Fixing subcity admin permissions...'))
        self.stdout.write('='*50)
        
        try:
            # Step 1: Create required permissions
            self.create_required_permissions(options['dry_run'])
            
            # Step 2: Create/update subcity admin group
            group = self.create_subcity_admin_group(options['dry_run'])
            
            # Step 3: Assign users to group
            if group:
                self.assign_subcity_admins_to_group(group, options['dry_run'])
            
            # Step 4: Verify specific user if provided
            if options['user_email']:
                self.verify_user_permissions(options['user_email'])
            
            self.stdout.write('')
            self.stdout.write('='*50)
            if options['dry_run']:
                self.stdout.write(self.style.WARNING('🔍 This was a dry run - no changes were made'))
            else:
                self.stdout.write(self.style.SUCCESS('✅ Subcity admin permissions fix completed!'))
            
            self.stdout.write('')
            self.stdout.write('Next steps:')
            self.stdout.write('1. Users need to log out and log back in to refresh permissions')
            self.stdout.write('2. Test access to Citizens, ID Cards, and User Management pages')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error during fix: {e}'))
            import traceback
            traceback.print_exc()

    def create_required_permissions(self, dry_run=False):
        """Create the required permissions if they don't exist."""
        self.stdout.write('🔧 Creating required permissions...')
        
        # Get User content type
        user_content_type = ContentType.objects.get_for_model(User)
        
        required_permissions = [
            ('manage_users', 'Can manage users'),
            ('manage_citizens', 'Can manage citizens'),
            ('manage_idcards', 'Can manage ID cards'),
            ('view_citizens', 'Can view citizens'),
            ('view_idcards', 'Can view ID cards'),
            ('view_reports', 'Can view reports'),
            ('manage_tenants', 'Can manage tenants'),
            ('view_all_data', 'Can view all data'),
            ('view_child_kebeles_data', 'Can view child kebeles data'),
            ('create_kebele_users', 'Can create kebele users'),
            ('print_id_cards', 'Can print ID cards'),
            ('approve_id_cards', 'Can approve ID cards'),
        ]
        
        created_count = 0
        for codename, name in required_permissions:
            if dry_run:
                exists = Permission.objects.filter(codename=codename, content_type=user_content_type).exists()
                if not exists:
                    self.stdout.write(f'  Would create permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  Permission already exists: {codename}')
            else:
                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    content_type=user_content_type,
                    defaults={'name': name}
                )
                if created:
                    self.stdout.write(f'  ✅ Created permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  ℹ️ Permission already exists: {codename}')
        
        self.stdout.write(f'📊 {"Would create" if dry_run else "Created"} {created_count} permissions')

    def create_subcity_admin_group(self, dry_run=False):
        """Create or update subcity admin group with proper permissions."""
        self.stdout.write('🏗️ Creating/updating subcity admin group...')
        
        with schema_context(get_public_schema_name()):
            group_name = "SubCity Administrators"
            
            if dry_run:
                exists = Group.objects.filter(name=group_name).exists()
                if not exists:
                    self.stdout.write(f'  Would create group: {group_name}')
                else:
                    self.stdout.write(f'  Group already exists: {group_name}')
                return None
            
            # Create or get the subcity admin group
            group, created = Group.objects.get_or_create(name=group_name)
            
            if created:
                self.stdout.write(f'  ✅ Created group: {group_name}')
            else:
                self.stdout.write(f'  ℹ️ Using existing group: {group_name}')
            
            # Define required permissions for subcity admin
            required_permission_codenames = [
                'manage_users',
                'manage_citizens', 
                'manage_idcards',
                'view_citizens',
                'view_idcards',
                'view_reports',
                'view_child_kebeles_data',
                'create_kebele_users',
                'print_id_cards',
                'approve_id_cards',
                'view_citizens_list',
                'view_citizen_details',
                'register_citizens',
                'generate_id_cards',
                'view_id_cards_list',
                'verify_documents',
                'view_kebele_reports',
                'view_subcity_reports',
                'navigate_to_dashboard',
                'view_subcity_dashboard',
                'navigate_to_citizens',
                'navigate_to_id_cards',
                'view_user_management',
                'view_subcity_management',
                'view_kebele_management',
                'change_user',
                'view_user',
            ]
            
            # Get existing permissions
            existing_permissions = set(group.permissions.values_list('codename', flat=True))
            
            # Add missing permissions
            added_count = 0
            for codename in required_permission_codenames:
                if codename not in existing_permissions:
                    try:
                        permission = Permission.objects.get(codename=codename)
                        group.permissions.add(permission)
                        self.stdout.write(f'  ➕ Added permission: {codename}')
                        added_count += 1
                    except Permission.DoesNotExist:
                        self.stdout.write(f'  ⚠️ Permission not found: {codename}')
            
            self.stdout.write(f'📊 Added {added_count} permissions to {group_name}')
            return group

    def assign_subcity_admins_to_group(self, group, dry_run=False):
        """Assign all subcity admin users to the group."""
        self.stdout.write('👥 Assigning subcity admin users to group...')
        
        # Find all subcity admin users
        subcity_admin_users = []
        
        # Check public schema first
        with schema_context(get_public_schema_name()):
            public_users = User.objects.filter(role='subcity_admin', is_active=True)
            subcity_admin_users.extend(public_users)
        
        # Check tenant schemas
        for tenant in Tenant.objects.filter(type='subcity'):
            try:
                with schema_context(tenant.schema_name):
                    tenant_users = User.objects.filter(role='subcity_admin', is_active=True)
                    subcity_admin_users.extend(tenant_users)
            except Exception as e:
                self.stdout.write(f'  ⚠️ Error checking tenant {tenant.name}: {e}')
        
        self.stdout.write(f'  Found {len(subcity_admin_users)} subcity admin users')
        
        if dry_run:
            for user in subcity_admin_users:
                self.stdout.write(f'  Would assign {user.email} to group')
            return len(subcity_admin_users)
        
        # Assign users to group
        assigned_count = 0
        with schema_context(get_public_schema_name()):
            for user in subcity_admin_users:
                try:
                    # Add user to Django group
                    group.user_set.add(user)
                    
                    # Also create GroupMembership record for our custom system
                    membership, created = GroupMembership.objects.get_or_create(
                        user_email=user.email,
                        group=group,
                        defaults={
                            'user_tenant_id': user.tenant.id if user.tenant else None,
                            'user_tenant_schema': user.tenant.schema_name if user.tenant else None,
                            'is_primary': True,
                            'assigned_by_email': 'system',
                            'reason': 'Automatic assignment for subcity admin role'
                        }
                    )
                    
                    if created:
                        self.stdout.write(f'  ✅ Assigned {user.email} to group')
                        assigned_count += 1
                    else:
                        self.stdout.write(f'  ℹ️ {user.email} already in group')
                        
                except Exception as e:
                    self.stdout.write(f'  ❌ Error assigning {user.email}: {e}')
        
        self.stdout.write(f'📊 Assigned {assigned_count} users to group')

    def verify_user_permissions(self, user_email):
        """Verify that a specific user has the required permissions."""
        self.stdout.write(f'🔍 Verifying permissions for {user_email}...')
        
        try:
            # Try to find user in public schema first
            user = None
            with schema_context(get_public_schema_name()):
                try:
                    user = User.objects.get(email=user_email)
                    self.stdout.write(f'  Found user in public schema')
                except User.DoesNotExist:
                    pass
            
            # If not found in public, check tenant schemas
            if not user:
                for tenant in Tenant.objects.all():
                    try:
                        with schema_context(tenant.schema_name):
                            user = User.objects.get(email=user_email)
                            self.stdout.write(f'  Found user in {tenant.name} schema')
                            break
                    except User.DoesNotExist:
                        continue
            
            if not user:
                self.stdout.write(f'  ❌ User {user_email} not found')
                return False
            
            self.stdout.write(f'  User role: {user.role}')
            self.stdout.write(f'  User tenant: {user.tenant.name if user.tenant else "None"}')
            
            # Check group memberships
            with schema_context(get_public_schema_name()):
                user_groups = user.groups.all()
                self.stdout.write(f'  User groups: {[g.name for g in user_groups]}')
                
                # Check specific permissions
                required_permissions = ['manage_users', 'manage_citizens', 'manage_idcards']
                for perm in required_permissions:
                    has_perm = user.has_group_permission(perm)
                    status = "✅" if has_perm else "❌"
                    self.stdout.write(f'  {status} {perm}: {has_perm}')
            
            return True
            
        except Exception as e:
            self.stdout.write(f'  ❌ Error verifying user: {e}')
            return False
