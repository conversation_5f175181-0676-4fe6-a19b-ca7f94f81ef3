import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Box, Typography, Paper, Chip } from '@mui/material';

const UserRoleDebug = () => {
  const { user } = useAuth();

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h6" gutterBottom>
        User Role Debug Information
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Email:</strong> {user?.email || 'Not available'}
        </Typography>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Role:</strong> <Chip label={user?.role || 'Not available'} size="small" />
        </Typography>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Is Superuser:</strong> {user?.is_superuser ? 'Yes' : 'No'}
        </Typography>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Tenant:</strong> {user?.tenant?.name || 'Not available'} ({user?.tenant?.type || 'Unknown type'})
        </Typography>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Groups:</strong>
        </Typography>
        <Box sx={{ mt: 1 }}>
          {user?.groups?.map((group, index) => (
            <Chip key={index} label={group} size="small" sx={{ mr: 1, mb: 1 }} />
          )) || <Typography variant="body2" color="text.secondary">No groups</Typography>}
        </Box>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Permissions:</strong>
        </Typography>
        <Box sx={{ mt: 1, maxHeight: 200, overflow: 'auto' }}>
          {user?.permissions?.map((permission, index) => (
            <Chip key={index} label={permission} size="small" sx={{ mr: 1, mb: 1 }} />
          )) || <Typography variant="body2" color="text.secondary">No permissions</Typography>}
        </Box>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Role Check Results:</strong>
        </Typography>
        <Box sx={{ mt: 1 }}>
          <Typography variant="body2">
            user?.role !== 'subcity_admin': {user?.role !== 'subcity_admin' ? 'true' : 'false'}
          </Typography>
          <Typography variant="body2">
            user?.role !== 'subcity_system_admin': {user?.role !== 'subcity_system_admin' ? 'true' : 'false'}
          </Typography>
          <Typography variant="body2">
            user?.role !== 'superadmin': {user?.role !== 'superadmin' ? 'true' : 'false'}
          </Typography>
          <Typography variant="body2">
            !user?.is_superuser: {!user?.is_superuser ? 'true' : 'false'}
          </Typography>
          <Typography variant="body2" color={
            (user?.role !== 'subcity_admin' && user?.role !== 'subcity_system_admin' && user?.role !== 'superadmin' && !user?.is_superuser) 
              ? 'error.main' : 'success.main'
          }>
            <strong>Should show access denied:</strong> {
              (user?.role !== 'subcity_admin' && user?.role !== 'subcity_system_admin' && user?.role !== 'superadmin' && !user?.is_superuser) 
                ? 'YES' : 'NO'
            }
          </Typography>
        </Box>
      </Box>
    </Paper>
  );
};

export default UserRoleDebug;
