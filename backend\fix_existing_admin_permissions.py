#!/usr/bin/env python3
"""
Fix permissions for existing admin users that were created without proper role groups.
This script finds all admin users and ensures they have the correct permissions.
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

def fix_admin_permissions():
    """Fix permissions for all existing admin users."""
    from django.contrib.auth import get_user_model
    from django.contrib.auth.models import Group, Permission
    from django.contrib.contenttypes.models import ContentType
    from tenants.models.tenant import Tenant
    from django_tenants.utils import schema_context
    
    User = get_user_model()
    
    print("🔧 Fixing permissions for existing admin users...")
    
    # Define role-specific permissions
    role_permissions = {
        'city_admin': [
            'create_subcity_users',
            'view_child_subcities_data',
            'view_city_dashboard',
            'view_city_reports',
            'manage_tenants'
        ],
        'subcity_admin': [
            'create_kebele_users',
            'view_child_kebeles_data',
            'view_subcity_dashboard',
            'view_subcity_reports',
            'approve_id_cards',
            'manage_workflows',
            'view_workflows',
            'view_user_management'
        ],
        'kebele_leader': [
            'approve_id_cards',
            'verify_documents',
            'view_kebele_dashboard',
            'view_kebele_reports',
            'create_transfers',
            'approve_transfer_requests',
            'create_clearances',
            'view_clearances'
        ]
    }
    
    # Get all tenants
    tenants = Tenant.objects.exclude(schema_name='public')
    
    for tenant in tenants:
        print(f"\n🔍 Processing tenant: {tenant.name} ({tenant.type})")
        
        try:
            with schema_context(tenant.schema_name):
                # Find admin users in this tenant
                admin_roles = ['city_admin', 'subcity_admin', 'kebele_leader', 'kebele_admin']
                admin_users = User.objects.filter(role__in=admin_roles)
                
                print(f"   Found {admin_users.count()} admin users")
                
                for user in admin_users:
                    print(f"   🔧 Processing user: {user.email} (role: {user.role})")
                    
                    if user.role not in role_permissions:
                        print(f"      ⚠️ No predefined permissions for role: {user.role}")
                        continue
                    
                    # Create or get the role group
                    group, created = Group.objects.get_or_create(name=user.role)
                    if created:
                        print(f"      ✅ Created group: {user.role}")
                    
                    # Add user to group if not already in it
                    if not user.groups.filter(name=user.role).exists():
                        user.groups.add(group)
                        print(f"      ✅ Added {user.email} to {user.role} group")
                    else:
                        print(f"      ℹ️ User already in {user.role} group")
                    
                    # Add permissions to group
                    content_type = ContentType.objects.get_for_model(User)
                    required_perms = role_permissions[user.role]
                    permissions_added = 0
                    
                    for perm_codename in required_perms:
                        try:
                            perm = Permission.objects.get(codename=perm_codename)
                            if not group.permissions.filter(codename=perm_codename).exists():
                                group.permissions.add(perm)
                                permissions_added += 1
                        except Permission.DoesNotExist:
                            # Create the permission if it doesn't exist
                            perm = Permission.objects.create(
                                codename=perm_codename,
                                name=f'Can {perm_codename.replace("_", " ")}',
                                content_type=content_type
                            )
                            group.permissions.add(perm)
                            permissions_added += 1
                            print(f"      ✅ Created permission: {perm_codename}")
                    
                    if permissions_added > 0:
                        print(f"      ✅ Added {permissions_added} permissions to {user.role} group")
                    else:
                        print(f"      ℹ️ All permissions already exist for {user.role} group")
                    
                    # Verify final state
                    final_groups = [g.name for g in user.groups.all()]
                    has_required_perm = user.groups.filter(
                        permissions__codename__in=required_perms
                    ).exists()
                    
                    print(f"      📊 Final state: Groups={final_groups}, Has permissions={has_required_perm}")
        
        except Exception as e:
            print(f"   ❌ Error processing tenant {tenant.name}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 Completed fixing admin permissions for all tenants!")

if __name__ == '__main__':
    fix_admin_permissions()
