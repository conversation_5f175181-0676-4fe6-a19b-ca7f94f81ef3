#!/usr/bin/env python
"""
Test RBAC function directly
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context
from common.multi_tenant_rbac import MultiTenantRBAC

User = get_user_model()

def test_rbac():
    print("🔧 Testing RBAC for city admin creating city system admin...")
    
    # Get the user and tenant
    target_tenant = Tenant.objects.get(id=15)
    print(f"Target tenant: {target_tenant.name} (type: {target_tenant.type}, ID: {target_tenant.id})")
    
    with schema_context(target_tenant.schema_name):
        user = User.objects.get(email='<EMAIL>')
        print(f"User: {user.email}")
        print(f"User role: {user.role}")
        print(f"User tenant: {user.tenant}")
        print(f"User tenant type: {user.tenant.type}")
        print(f"User tenant ID: {user.tenant.id}")
        
        print(f"\n🔍 Testing RBAC conditions:")
        print(f"  user_tenant.id == target_tenant.id: {user.tenant.id} == {target_tenant.id} = {user.tenant.id == target_tenant.id}")
        print(f"  user_tenant.type == 'city': {user.tenant.type} == 'city' = {user.tenant.type == 'city'}")
        print(f"  user.role == 'city_admin': {user.role} == 'city_admin' = {user.role == 'city_admin'}")
        
        # Test the RBAC function directly
        can_manage = MultiTenantRBAC.can_manage_users_in_tenant(user, target_tenant)
        print(f"\n✅ Final result: can_manage_users_in_tenant = {can_manage}")

if __name__ == '__main__':
    test_rbac()
