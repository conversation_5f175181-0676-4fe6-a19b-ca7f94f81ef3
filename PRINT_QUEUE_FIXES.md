# Print Queue Access and Image Display Fixes

## Issues Fixed

### 1. Centralized Workflow - Print ID Cards Role Access Issue

**Problem**: Users with `print_id_cards` role could see the filtered ID card list correctly, but when they clicked "View & Print" buttons, they got 403/404 errors.

**Root Cause**: The `cross_tenant_detail` and `cross_tenant_list` endpoints only allowed `subcity_admin`, `city_admin`, and `superadmin` roles, but excluded `print_id_cards` and `designated_printer` roles.

**Solution**: Modified permission checks in both endpoints to include printing roles.

#### Files Modified:
- `backend/tenants/views/tenant_idcard_views.py`

#### Changes Made:

**1. Fixed `cross_tenant_detail` method (line ~833):**
```python
# Before:
if request.user.role not in ['subcity_admin', 'city_admin', 'superadmin'] and not request.user.is_superuser:

# After:
allowed_roles = ['subcity_admin', 'city_admin', 'superadmin', 'print_id_cards', 'designated_printer']
if request.user.role not in allowed_roles and not request.user.is_superuser:
```

**2. Fixed `cross_tenant_list` method (line ~1172):**
```python
# Before:
if not (request.user.role in ['subcity_admin', 'city_admin', 'superadmin'] or ...

# After:
if not (request.user.role in ['subcity_admin', 'city_admin', 'superadmin', 'print_id_cards', 'designated_printer'] or
        request.user.has_group_permission('print_id_cards') or ...
```

### 2. Autonomous Workflow - Missing Images Issue

**Problem**: In autonomous workflow, when users clicked the print button, the ID card details were displayed successfully but without logos, mayor signatures, and security pattern images.

**Root Cause**: The autonomous printing queue and ID card detail endpoints were returning simplified data without the complete tenant hierarchy information that the frontend needs to fetch images via the `/api/tenants/get-tenant-data/` endpoint.

**Solution**: Enhanced all ID card detail endpoints to include complete tenant hierarchy information.

#### Changes Made:

**1. Enhanced `_get_autonomous_printing_queue` method (line ~1053):**
```python
# Added complete tenant hierarchy info to each card
card_data['kebele_tenant'] = {
    'id': kebele_tenant.id,
    'name': kebele_tenant.name,
    'schema_name': kebele_tenant.schema_name,
    'workflow_type': 'autonomous',
    'parent_id': kebele_tenant.parent.id if kebele_tenant.parent else None,
    'parent_name': kebele_tenant.parent.name if kebele_tenant.parent else None
}

# Add tenant hierarchy for image fetching (needed by frontend)
card_data['tenant_hierarchy'] = {
    'kebele_tenant_id': kebele_tenant.id,
    'subcity_tenant_id': kebele_tenant.parent.id if kebele_tenant.parent else None,
    'city_tenant_id': kebele_tenant.parent.parent.id if kebele_tenant.parent and kebele_tenant.parent.parent else None
}
```

**2. Enhanced `retrieve` method (line ~290):**
```python
# Add tenant hierarchy information for image fetching
card_data['kebele_tenant'] = {
    'id': tenant.id,
    'name': tenant.name,
    'schema_name': tenant.schema_name,
    'parent_id': tenant.parent.id if tenant.parent else None,
    'parent_name': tenant.parent.name if tenant.parent else None
}

card_data['tenant_hierarchy'] = {
    'kebele_tenant_id': tenant.id,
    'subcity_tenant_id': tenant.parent.id if tenant.parent else None,
    'city_tenant_id': tenant.parent.parent.id if tenant.parent and tenant.parent.parent else None
}
```

**3. Enhanced `cross_tenant_detail` method (line ~893):**
```python
# Add tenant hierarchy for image fetching (needed by frontend)
card_data['tenant_hierarchy'] = {
    'kebele_tenant_id': kebele.id,
    'subcity_tenant_id': kebele.parent.id if kebele.parent else None,
    'city_tenant_id': kebele.parent.parent.id if kebele.parent and kebele.parent.parent else None
}
```

## How the Frontend Uses This Data

The frontend `IDCardTemplate.jsx` component uses the `tenant_hierarchy` data to call the `/api/tenants/get-tenant-data/` endpoint:

```javascript
const tenantDataResponse = await axios.post('/api/tenants/get-tenant-data/', {
    kebele_tenant_id: card_data.tenant_hierarchy.kebele_tenant_id,
    subcity_tenant_id: card_data.tenant_hierarchy.subcity_tenant_id,
    city_tenant_id: card_data.tenant_hierarchy.city_tenant_id
});
```

This endpoint returns the logos, signatures, and pattern images needed for ID card display.

## Frontend Changes

### 3. Fixed Cross-Tenant Endpoint URL Logic

**Problem**: The frontend was using the kebele tenant ID from the URL to call the cross-tenant endpoint, but it should use the user's subcity tenant ID.

**Root Cause**: When a subcity user clicks on an ID card from kebele tenant 7, the URL becomes `/idcards/12?tenantId=7`. The frontend was incorrectly using tenant 7 to call `/api/tenants/7/idcards/12/cross_tenant_detail/`, but kebele tenants can't call cross-tenant endpoints - only subcity/city tenants can.

**Solution**: Modified the frontend to use the user's tenant ID for cross-tenant calls, not the URL tenant ID.

#### Files Modified:
- `frontend/src/pages/idcards/IDCardView.jsx`
- `frontend/src/pages/idcards/IDCardPrintPreview.jsx`

#### Changes Made:

**Before (Incorrect):**
```javascript
// Used kebele tenant ID from URL for cross-tenant call
const effectiveTenantId = tenantId || getTenantId(); // tenantId = 7 (kebele)
response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/cross_tenant_detail/`);
// ❌ Calls: /api/tenants/7/idcards/12/cross_tenant_detail/ (kebele can't do cross-tenant)
```

**After (Correct):**
```javascript
// Use user's subcity tenant ID for cross-tenant call
const userTenantId = getTenantId(); // userTenantId = 6 (subcity)
response = await axios.get(`/api/tenants/${userTenantId}/idcards/${id}/cross_tenant_detail/`);
// ✅ Calls: /api/tenants/6/idcards/12/cross_tenant_detail/ (subcity searches child kebeles)
```

### 4. Fixed Frontend to Use ID Card Tenant Hierarchy

**Problem**: The frontend was still using the user's tenant data instead of the ID card's tenant hierarchy data when calling the `/api/tenants/get-tenant-data/` endpoint.

**Solution**: Modified the frontend `IDCardTemplate.jsx` component to use the `tenant_hierarchy` data from the ID card response.

#### Files Modified:
- `frontend/src/components/idcards/IDCardTemplate.jsx`

#### Changes Made:

**1. Enhanced tenant data API calls to use ID card hierarchy (line ~877):**
```javascript
// Before:
const tenantDataResponse = await axios.post('/api/tenants/get-tenant-data/', {
    kebele_tenant_id: user?.tenant_id,
    subcity_tenant_id: user?.parent_tenant_id,
    city_tenant_id: user?.city_tenant_id
});

// After:
const tenantHierarchyData = idCard.tenant_hierarchy || {};
const tenantDataResponse = await axios.post('/api/tenants/get-tenant-data/', {
    kebele_tenant_id: tenantHierarchyData.kebele_tenant_id || user?.tenant_id,
    subcity_tenant_id: tenantHierarchyData.subcity_tenant_id || user?.parent_tenant_id,
    city_tenant_id: tenantHierarchyData.city_tenant_id || user?.city_tenant_id
});
```

**2. Applied same fix to all tenant data API calls in the component:**
- Line ~1048: Print ID cards user context
- Line ~1143: Fallback context

## Expected Results

After these fixes:

### Centralized Workflow:
1. ✅ Users with `print_id_cards` role can access the printing queue list
2. ✅ Users with `print_id_cards` role can click "View & Print" buttons without 403/404 errors
3. ✅ Cross-tenant detail access works for printing roles

### Autonomous Workflow:
1. ✅ ID card details include complete tenant hierarchy information
2. ✅ Frontend uses correct tenant hierarchy to fetch images
3. ✅ ID cards display with logos, signatures, and security patterns
4. ✅ Both printing queue and individual card views work properly

## Testing

## Additional Fixes Applied

### 4. Fixed Permission Class Action List

**Problem**: The `CanManageIDCardsInTenantContext` permission class didn't include `cross_tenant_detail` and `cross_tenant_list` in its allowed actions list.

**Solution**: Added the missing actions to the permission check.

#### Changes Made:
```python
# Before:
if view.action in ['update_status', 'approval_action', 'print', 'list', 'retrieve', ...]

# After:
if view.action in ['update_status', 'approval_action', 'print', 'list', 'retrieve', ..., 'cross_tenant_detail', 'cross_tenant_list']
```

### 5. Enhanced Debug Logging

**Problem**: Difficult to debug permission and ID card location issues.

**Solution**: Added comprehensive debug logging to the `cross_tenant_detail` method.

### Management Commands for Testing:

#### Test Tenant Hierarchy Data:
```bash
# Test specific ID card
python manage.py test_tenant_hierarchy --tenant-id 7 --id-card-id 11

# Test all ID cards in a tenant
python manage.py test_tenant_hierarchy --tenant-id 7

# Test all tenants
python manage.py test_tenant_hierarchy
```

#### Debug Cross-Tenant Access Issues:
```bash
# Debug specific access issue
python manage.py debug_cross_tenant_access --user-email <EMAIL> --tenant-id 7 --id-card-id 12
```

### Manual Testing:

1. **Test centralized workflow**:
   - Login as user with `print_id_cards` role in subcity
   - Access printing queue - should see list of cards
   - Click "View & Print" - should open card details without errors

2. **Test autonomous workflow**:
   - Login as user with `print_id_cards` role in autonomous kebele
   - Access printing queue - should see list of cards
   - Click "View & Print" - should show card with logos, signatures, and patterns

Both workflows should now work correctly with proper image display and access permissions.
