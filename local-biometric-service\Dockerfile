# Production Dockerfile for GoID Biometric Service
FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set work directory
WORKDIR /app

# Install system dependencies for USB device access
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        libusb-1.0-0-dev \
        pkg-config \
        build-essential \
        udev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy biometric service files
COPY . /app/

# Create non-root user
RUN adduser --disabled-password --gecos '' biometric
RUN chown -R biometric:biometric /app
USER biometric

# Expose port
EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/api/health || exit 1

# Start the biometric service
CMD ["python", "minimal_capture_service.py"]
