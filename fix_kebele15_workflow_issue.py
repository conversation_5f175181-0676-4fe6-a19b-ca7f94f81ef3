#!/usr/bin/env python3
"""
Fix Kebele 15 Workflow Inconsistency Issue

This script diagnoses and fixes the workflow inconsistency where:
- Kebele 15 is configured as centralized
- But ID cards are behaving like autonomous workflow
- Subcity admin can't approve cards that are already "fully approved"
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from idcards.models import IDCard, IDCardStatus
from django.utils import timezone

def diagnose_kebele15_workflow():
    """Diagnose the workflow issue for Kebele 15."""
    print("🔍 Diagnosing Kebele 15 Workflow Issue")
    print("=" * 50)
    
    try:
        # Get Kebele 15 tenant (ID 18 based on actual data)
        kebele15 = Tenant.objects.get(id=18, name='Kebele15', type='kebele')
        print(f"📍 Found Kebele: {kebele15.name} (ID: {kebele15.id})")
        
        # Check workflow configuration
        try:
            workflow_config = kebele15.workflow_config
            print(f"✅ Current workflow type: {workflow_config.workflow_type}")
            print(f"📋 ID card processing config: {workflow_config.id_card_processing}")
        except Exception as e:
            print(f"❌ No workflow config found: {e}")
            return False
        
        # Check ID cards in the kebele schema
        with schema_context(kebele15.schema_name):
            # Get all ID cards and their statuses
            all_cards = IDCard.objects.all()
            print(f"\n📊 ID Card Status Analysis:")
            print(f"   Total ID cards: {all_cards.count()}")
            
            # Group by status
            status_counts = {}
            for status_choice in IDCardStatus.choices:
                status_code = status_choice[0]
                count = all_cards.filter(status=status_code).count()
                if count > 0:
                    status_counts[status_code] = count
                    print(f"   {status_choice[1]}: {count}")
            
            # Check for problematic cards (approved without subcity approval in centralized workflow)
            if workflow_config.workflow_type == 'centralized':
                problematic_cards = all_cards.filter(
                    status=IDCardStatus.APPROVED,
                    subcity_approved_by__isnull=True
                )
                
                print(f"\n🚨 Problematic Cards (Approved without subcity approval in centralized workflow):")
                print(f"   Count: {problematic_cards.count()}")
                
                for card in problematic_cards[:5]:  # Show first 5
                    print(f"   - Card #{card.card_number}: {card.citizen.get_full_name()}")
                    print(f"     Status: {card.status}")
                    print(f"     Kebele approved: {card.kebele_approved_at}")
                    print(f"     Subcity approved: {card.subcity_approved_at}")
                    print(f"     Has kebele pattern: {card.has_kebele_pattern}")
                    print(f"     Has subcity pattern: {card.has_subcity_pattern}")
                
                return problematic_cards.count() > 0
            
        return False
        
    except Tenant.DoesNotExist:
        print("❌ Kebele 15 not found")
        return False
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        return False

def fix_kebele15_workflow():
    """Fix the workflow inconsistency for Kebele 15."""
    print("\n🔧 Fixing Kebele 15 Workflow Issue")
    print("=" * 50)
    
    try:
        with transaction.atomic():
            # Get Kebele 15 tenant (ID 18 based on actual data)
            kebele15 = Tenant.objects.get(id=18, name='Kebele15', type='kebele')
            
            # Ensure workflow configuration is properly set to centralized
            workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                tenant=kebele15,
                defaults={'workflow_type': 'centralized'}
            )
            
            if workflow_config.workflow_type != 'centralized':
                print(f"🔄 Switching workflow from {workflow_config.workflow_type} to centralized")
                workflow_config.workflow_type = 'centralized'
                workflow_config.id_card_processing = {
                    'can_print_locally': False,
                    'requires_higher_approval': True,
                    'approval_levels': ['kebele_leader', 'subcity_admin'],
                    'printing_authority': 'subcity',
                    'quality_control': 'centralized'
                }
                workflow_config.save()
                print("✅ Workflow configuration updated")
            
            # Fix problematic ID cards
            with schema_context(kebele15.schema_name):
                # Find cards that are approved without subcity approval (autonomous behavior in centralized workflow)
                problematic_cards = IDCard.objects.filter(
                    status=IDCardStatus.APPROVED,
                    subcity_approved_by__isnull=True,
                    kebele_approved_by__isnull=False
                )
                
                print(f"🔧 Found {problematic_cards.count()} problematic cards to fix")
                
                fixed_count = 0
                for card in problematic_cards:
                    print(f"   Fixing Card #{card.card_number}: {card.citizen.get_full_name()}")
                    
                    # Reset to kebele_approved status (waiting for subcity approval)
                    card.status = IDCardStatus.KEBELE_APPROVED
                    card.has_subcity_pattern = False  # Remove subcity pattern until subcity approves
                    card.submitted_to_subcity_at = timezone.now()  # Mark as submitted to subcity
                    card.save()
                    
                    print(f"     ✅ Reset to KEBELE_APPROVED status")
                    fixed_count += 1
                
                print(f"✅ Fixed {fixed_count} ID cards")
                
                # Clear any cache that might be holding old workflow data
                from django.core.cache import cache
                cache_keys = [
                    f'tenant_workflow_{kebele15.id}',
                    f'workflow_config_{kebele15.id}',
                    'biometric_templates_all'
                ]
                
                for key in cache_keys:
                    cache.delete(key)
                    print(f"🗑️ Cleared cache key: {key}")
                
                return fixed_count
                
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        return 0

def verify_fix():
    """Verify that the fix was successful."""
    print("\n✅ Verifying Fix")
    print("=" * 50)
    
    try:
        kebele15 = Tenant.objects.get(id=18, name='Kebele15', type='kebele')
        workflow_config = kebele15.workflow_config
        
        print(f"📍 Kebele: {kebele15.name}")
        print(f"🔧 Workflow type: {workflow_config.workflow_type}")
        
        with schema_context(kebele15.schema_name):
            # Check current status distribution
            all_cards = IDCard.objects.all()
            
            kebele_approved_cards = all_cards.filter(status=IDCardStatus.KEBELE_APPROVED)
            fully_approved_cards = all_cards.filter(
                status=IDCardStatus.APPROVED,
                subcity_approved_by__isnull=False
            )
            problematic_cards = all_cards.filter(
                status=IDCardStatus.APPROVED,
                subcity_approved_by__isnull=True
            )
            
            print(f"📊 Current Status:")
            print(f"   Kebele Approved (waiting for subcity): {kebele_approved_cards.count()}")
            print(f"   Fully Approved (with subcity approval): {fully_approved_cards.count()}")
            print(f"   Problematic (approved without subcity): {problematic_cards.count()}")
            
            if problematic_cards.count() == 0:
                print("✅ All ID cards are now in correct workflow state!")
                return True
            else:
                print("❌ Some problematic cards still exist")
                return False
                
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Kebele 15 Workflow Fix Script")
    print("=" * 50)
    
    # Step 1: Diagnose the issue
    has_issues = diagnose_kebele15_workflow()
    
    if has_issues:
        # Step 2: Fix the issue
        fixed_count = fix_kebele15_workflow()
        
        if fixed_count > 0:
            # Step 3: Verify the fix
            success = verify_fix()
            
            if success:
                print("\n🎉 Kebele 15 workflow issue has been successfully fixed!")
                print("\nNext steps:")
                print("1. Kebele leader approvals will now send ID cards to subcity admin")
                print("2. Subcity admin can approve the kebele-approved cards")
                print("3. Only fully approved cards (with both kebele and subcity approval) can be printed")
            else:
                print("\n⚠️ Fix partially successful, manual intervention may be needed")
        else:
            print("\n⚠️ No cards were fixed, issue may be elsewhere")
    else:
        print("\n✅ No workflow issues detected for Kebele 15")
