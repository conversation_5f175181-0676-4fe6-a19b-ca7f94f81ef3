from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django_tenants.utils import schema_context, get_public_schema_name
from users.models_groups import TenantGroup, GroupMembership
from users.models import User
from tenants.models import Tenant
from django.db import connection

class Command(BaseCommand):
    help = 'Fix predefined roles system from scratch using direct database operations'

    def handle(self, *args, **options):
        self.stdout.write("🚀 Starting complete predefined roles fix using direct database operations...")

        try:
            # Step 1: Create all permissions directly
            self.fix_permissions_directly()

            # Step 2: Create predefined roles directly
            self.create_roles_directly()

            # Step 3: Assign users to groups directly
            self.assign_users_directly()

            # Step 4: Verify the fix
            self.verify_fix()

            self.stdout.write(self.style.SUCCESS("\n🎉 Complete predefined roles fix completed successfully!"))
            self.stdout.write("📋 Please restart the application and clear browser cache.")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error during fix: {e}"))
            import traceback
            traceback.print_exc()

    def fix_permissions_directly(self):
        """Fix permissions directly using raw SQL"""
        self.stdout.write("🔧 Creating permissions directly in database...")

        with connection.cursor() as cursor:
            # Get or create content type for users.user
            cursor.execute("""
                INSERT INTO django_content_type (app_label, model)
                VALUES ('users', 'user')
                ON CONFLICT (app_label, model) DO NOTHING
                RETURNING id
            """)
            result = cursor.fetchone()
            if result:
                content_type_id = result[0]
                self.stdout.write(f"  ✅ Created content type with ID: {content_type_id}")
            else:
                cursor.execute("""
                    SELECT id FROM django_content_type
                    WHERE app_label = 'users' AND model = 'user'
                """)
                content_type_id = cursor.fetchone()[0]
                self.stdout.write(f"  ℹ️ Using existing content type ID: {content_type_id}")

            # Define all required permissions
            permissions = [
                ('view_kebele_dashboard', 'Can view kebele dashboard'),
                ('view_subcity_dashboard', 'Can view subcity dashboard'),
                ('view_city_dashboard', 'Can view city dashboard'),
                ('register_citizens', 'Can register citizens'),
                ('view_citizens_list', 'Can view citizens list'),
                ('view_citizen_details', 'Can view citizen details'),
                ('view_own_kebele_data', 'Can view own kebele data'),
                ('view_child_kebeles_data', 'Can view child kebeles data'),
                ('view_child_subcities_data', 'Can view child subcities data'),
                ('generate_id_cards', 'Can generate ID cards'),
                ('view_id_cards_list', 'Can view ID cards list'),
                ('approve_id_cards', 'Can approve ID cards'),
                ('print_id_cards', 'Can print ID cards'),
                ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
                ('verify_documents', 'Can verify documents'),
                ('create_kebele_users', 'Can create kebele users'),
                ('create_subcity_users', 'Can create subcity users'),
                ('view_kebele_reports', 'Can view kebele reports'),
                ('view_subcity_reports', 'Can view subcity reports'),
                ('view_city_reports', 'Can view city reports'),
                ('view_all_reports', 'Can view all reports'),
                ('create_transfers', 'Can create transfer requests'),
                ('approve_transfer_requests', 'Can approve transfer requests'),
                ('create_clearances', 'Can create clearance requests'),
                ('view_clearances', 'Can view clearances'),
                ('manage_tenants', 'Can manage tenants'),
            ]

            # Insert permissions
            created_count = 0
            for codename, name in permissions:
                cursor.execute("""
                    INSERT INTO auth_permission (name, content_type_id, codename)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (content_type_id, codename) DO NOTHING
                    RETURNING id
                """, [name, content_type_id, codename])

                if cursor.fetchone():
                    created_count += 1
                    self.stdout.write(f"    ✅ Created: {codename}")
                else:
                    self.stdout.write(f"    ℹ️ Exists: {codename}")

            self.stdout.write(f"  📊 Created {created_count} new permissions")

    def create_roles_directly(self):
        """Create roles and assign permissions directly"""
        self.stdout.write("\n🔧 Creating roles and assigning permissions...")

        # Define role configurations with exact permissions
        roles_config = {
            'clerk': [
                'register_citizens', 'view_citizens_list', 'view_citizen_details',
                'generate_id_cards', 'view_id_cards_list', 'view_kebele_dashboard'
            ],
            'kebele_leader': [
                'view_own_kebele_data', 'view_kebele_dashboard', 'view_kebele_reports',
                'view_citizens_list', 'view_citizen_details', 'view_id_cards_list',
                'approve_id_cards', 'verify_documents', 'create_transfers',
                'approve_transfer_requests', 'create_clearances', 'view_clearances'
            ],
            'subcity_admin': [
                'view_child_kebeles_data', 'view_subcity_dashboard', 'view_subcity_reports',
                'approve_id_cards', 'print_id_cards', 'send_id_cards_to_higher_level',
                'verify_documents', 'create_kebele_users'
            ],
            'subcity_system_admin': ['create_kebele_users'],
            'city_admin': [
                'view_child_subcities_data', 'view_city_dashboard', 'view_city_reports',
                'manage_tenants', 'view_all_reports', 'create_subcity_users'
            ],
            'city_system_admin': ['create_subcity_users'],
            'print_id_cards': ['print_id_cards']
        }

        with connection.cursor() as cursor:
            # Get content type ID
            cursor.execute("""
                SELECT id FROM django_content_type
                WHERE app_label = 'users' AND model = 'user'
            """)
            content_type_id = cursor.fetchone()[0]

            for role_name, permission_codenames in roles_config.items():
                self.stdout.write(f"\n  🔧 Processing role: {role_name}")

                # Create group if it doesn't exist
                cursor.execute("""
                    INSERT INTO auth_group (name) VALUES (%s)
                    ON CONFLICT (name) DO NOTHING
                    RETURNING id
                """, [role_name])

                result = cursor.fetchone()
                if result:
                    group_id = result[0]
                    self.stdout.write(f"    ✅ Created group: {role_name}")
                else:
                    # Get existing group ID
                    cursor.execute("SELECT id FROM auth_group WHERE name = %s", [role_name])
                    group_id = cursor.fetchone()[0]
                    self.stdout.write(f"    ℹ️ Using existing group: {role_name}")

                # Clear existing permissions for this group
                cursor.execute("DELETE FROM auth_group_permissions WHERE group_id = %s", [group_id])

                # Add permissions to group
                permissions_added = 0
                for perm_codename in permission_codenames:
                    cursor.execute("""
                        SELECT id FROM auth_permission
                        WHERE codename = %s AND content_type_id = %s
                    """, [perm_codename, content_type_id])

                    perm_result = cursor.fetchone()
                    if perm_result:
                        perm_id = perm_result[0]
                        cursor.execute("""
                            INSERT INTO auth_group_permissions (group_id, permission_id)
                            VALUES (%s, %s)
                            ON CONFLICT DO NOTHING
                        """, [group_id, perm_id])
                        permissions_added += 1
                        self.stdout.write(f"      ✅ Added: {perm_codename}")
                    else:
                        self.stdout.write(f"      ❌ Not found: {perm_codename}")

                self.stdout.write(f"    📊 Added {permissions_added} permissions to {role_name}")

    def assign_users_directly(self):
        """Assign users to their correct groups directly"""
        self.stdout.write("\n👥 Assigning users to correct groups...")

        with connection.cursor() as cursor:
            # Get all users with their roles
            cursor.execute("""
                SELECT DISTINCT
                    u.id as user_id,
                    u.email,
                    u.role
                FROM auth_user u
                WHERE u.role IS NOT NULL
            """)

            users = cursor.fetchall()
            self.stdout.write(f"  Found {len(users)} users with roles")

            role_mapping = {
                'clerk': 'clerk',
                'kebele_leader': 'kebele_leader',
                'subcity_admin': 'subcity_admin',
                'subcity_system_admin': 'subcity_system_admin',
                'city_admin': 'city_admin',
                'city_system_admin': 'city_system_admin'
            }

            assigned_count = 0
            for user_id, email, role in users:
                group_name = role_mapping.get(role)
                if not group_name:
                    self.stdout.write(f"    ⚠️ No mapping for role: {role} (user: {email})")
                    continue

                # Get group ID
                cursor.execute("SELECT id FROM auth_group WHERE name = %s", [group_name])
                group_result = cursor.fetchone()
                if not group_result:
                    self.stdout.write(f"    ❌ Group not found: {group_name}")
                    continue

                group_id = group_result[0]

                # Clear existing group memberships for this user
                cursor.execute("DELETE FROM auth_user_groups WHERE user_id = %s", [user_id])

                # Add user to correct group
                cursor.execute("""
                    INSERT INTO auth_user_groups (user_id, group_id)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, [user_id, group_id])

                assigned_count += 1
                self.stdout.write(f"    ✅ Assigned {email} ({role}) to {group_name}")

            self.stdout.write(f"  📊 Assigned {assigned_count} users to groups")

    def verify_fix(self):
        """Verify the fix worked"""
        self.stdout.write("\n🔍 Verifying the fix...")

        with connection.cursor() as cursor:
            # Check kebele_leader group permissions
            cursor.execute("""
                SELECT p.codename, p.name
                FROM auth_permission p
                JOIN auth_group_permissions gp ON p.id = gp.permission_id
                JOIN auth_group g ON gp.group_id = g.id
                WHERE g.name = 'kebele_leader'
                ORDER BY p.codename
            """)

            kebele_leader_perms = cursor.fetchall()
            self.stdout.write(f"  kebele_leader group has {len(kebele_leader_perms)} permissions:")

            transfer_clearance_count = 0
            for codename, name in kebele_leader_perms:
                self.stdout.write(f"    - {codename}")
                if any(keyword in codename for keyword in ['transfer', 'clearance']):
                    transfer_clearance_count += 1

            if transfer_clearance_count > 0:
                self.stdout.write(f"  ✅ Found {transfer_clearance_count} Transfer/Clearance permissions")
                return True
            else:
                self.stdout.write("  ❌ No Transfer/Clearance permissions found")
                return False
                app_label='users',
                model='user'
            )
            
            # Define all permissions needed
            all_permissions = [
                # Basic navigation and dashboard
                ('view_kebele_dashboard', 'Can view kebele dashboard'),
                ('view_subcity_dashboard', 'Can view subcity dashboard'),
                ('view_city_dashboard', 'Can view city dashboard'),
                
                # Citizens permissions
                ('register_citizens', 'Can register citizens'),
                ('view_citizens_list', 'Can view citizens list'),
                ('view_citizen_details', 'Can view citizen details'),
                ('view_own_kebele_data', 'Can view own kebele data'),
                ('view_child_kebeles_data', 'Can view child kebeles data'),
                ('view_child_subcities_data', 'Can view child subcities data'),
                
                # ID Cards permissions
                ('generate_id_cards', 'Can generate ID cards'),
                ('view_id_cards_list', 'Can view ID cards list'),
                ('approve_id_cards', 'Can approve ID cards'),
                ('print_id_cards', 'Can print ID cards'),
                ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
                ('verify_documents', 'Can verify documents'),
                
                # User management permissions
                ('create_kebele_users', 'Can create kebele users'),
                ('create_subcity_users', 'Can create subcity users'),
                
                # Reports permissions
                ('view_kebele_reports', 'Can view kebele reports'),
                ('view_subcity_reports', 'Can view subcity reports'),
                ('view_city_reports', 'Can view city reports'),
                ('view_all_reports', 'Can view all reports'),
                
                # Transfer and Clearance permissions
                ('create_transfers', 'Can create transfer requests'),
                ('approve_transfer_requests', 'Can approve transfer requests'),
                ('create_clearances', 'Can create clearance requests'),
                ('view_clearances', 'Can view clearances'),
                
                # System permissions
                ('manage_tenants', 'Can manage tenants'),
            ]
            
            created_count = 0
            for codename, name in all_permissions:
                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    content_type=content_type,
                    defaults={'name': name}
                )
                if created:
                    self.stdout.write(f"  ✅ Created: {codename}")
                    created_count += 1
                else:
                    self.stdout.write(f"  ℹ️ Exists: {codename}")
            
            self.stdout.write(f"📊 Created {created_count} new permissions")

    def create_predefined_roles(self):
        """Create predefined role groups with exact permissions"""
        self.stdout.write("\n🏗️ Creating predefined role groups...")
        
        # Define roles with exact permissions as specified
        roles_config = {
            'clerk': {
                'description': 'Clerk role for kebele level tenants',
                'tenant_types': ['kebele'],
                'level': 10,
                'group_type': 'operational',
                'permissions': [
                    'register_citizens',
                    'view_citizens_list', 
                    'view_citizen_details',
                    'generate_id_cards',
                    'view_id_cards_list',
                    'view_kebele_dashboard'
                ]
            },
            'kebele_leader': {
                'description': 'Kebele leader role for kebele level tenants',
                'tenant_types': ['kebele'],
                'level': 20,
                'group_type': 'administrative',
                'permissions': [
                    'view_own_kebele_data',
                    'view_kebele_dashboard',
                    'view_kebele_reports',
                    'view_citizens_list',
                    'view_citizen_details',
                    'view_id_cards_list',
                    'approve_id_cards',
                    'verify_documents',
                    'create_transfers',
                    'approve_transfer_requests',
                    'create_clearances',
                    'view_clearances'
                ]
            },
            'subcity_admin': {
                'description': 'Subcity admin role for subcity level tenants',
                'tenant_types': ['subcity'],
                'level': 30,
                'group_type': 'administrative',
                'permissions': [
                    'view_child_kebeles_data',
                    'view_subcity_dashboard',
                    'view_subcity_reports',
                    'approve_id_cards',
                    'print_id_cards',
                    'send_id_cards_to_higher_level',
                    'verify_documents',
                    'create_kebele_users'
                ]
            },
            'subcity_system_admin': {
                'description': 'Subcity system admin for managing kebele users',
                'tenant_types': ['subcity'],
                'level': 35,
                'group_type': 'administrative',
                'permissions': [
                    'create_kebele_users'
                ]
            },
            'city_admin': {
                'description': 'City admin role for city level tenants',
                'tenant_types': ['city'],
                'level': 40,
                'group_type': 'administrative',
                'permissions': [
                    'view_child_subcities_data',
                    'view_city_dashboard',
                    'view_city_reports',
                    'manage_tenants',
                    'view_all_reports',
                    'create_subcity_users'
                ]
            },
            'city_system_admin': {
                'description': 'City system admin for managing subcity users',
                'tenant_types': ['city'],
                'level': 45,
                'group_type': 'administrative',
                'permissions': [
                    'create_subcity_users'
                ]
            },
            'print_id_cards': {
                'description': 'ID card printing role for autonomous kebeles or centralized subcities',
                'tenant_types': ['kebele', 'subcity'],
                'level': 15,
                'group_type': 'operational',
                'permissions': [
                    'print_id_cards'
                ]
            }
        }
        
        with schema_context(get_public_schema_name()):
            created_count = 0
            updated_count = 0
            
            for role_name, config in roles_config.items():
                self.stdout.write(f"\n🔧 Processing role: {role_name}")
                
                # Create Django group
                group, created = Group.objects.get_or_create(name=role_name)
                if created:
                    created_count += 1
                    self.stdout.write(f"  ✅ Created group: {role_name}")
                else:
                    self.stdout.write(f"  ℹ️ Group exists: {role_name}")
                
                # Clear existing permissions and add new ones
                group.permissions.clear()
                permissions_added = 0
                
                for perm_codename in config['permissions']:
                    try:
                        permission = Permission.objects.get(codename=perm_codename)
                        group.permissions.add(permission)
                        permissions_added += 1
                        self.stdout.write(f"    ✅ Added permission: {perm_codename}")
                    except Permission.DoesNotExist:
                        self.stdout.write(f"    ❌ Permission not found: {perm_codename}")
                
                self.stdout.write(f"  📊 Added {permissions_added} permissions to {role_name}")
                
                # Create or update TenantGroup
                tenant_group, tg_created = TenantGroup.objects.get_or_create(
                    group=group,
                    defaults={
                        'name': role_name,
                        'description': config['description'],
                        'group_type': config['group_type'],
                        'level': config['level'],
                        'allowed_tenant_types': config['tenant_types'],
                        'is_active': True,
                        'tenant': None
                    }
                )
                
                if not tg_created:
                    # Update existing TenantGroup
                    tenant_group.allowed_tenant_types = config['tenant_types']
                    tenant_group.level = config['level']
                    tenant_group.description = config['description']
                    tenant_group.group_type = config['group_type']
                    tenant_group.save()
                    updated_count += 1
                    self.stdout.write(f"  🔄 Updated TenantGroup: {role_name}")
            
            self.stdout.write(f"\n📊 Summary: {created_count} groups created, {updated_count} groups updated")

    def reassign_existing_users(self):
        """Reassign existing users to their correct role groups"""
        self.stdout.write("\n👥 Reassigning existing users to role groups...")
        
        # Get all users with roles
        all_users = []
        
        with schema_context(get_public_schema_name()):
            tenants = Tenant.objects.all()
            
            for tenant in tenants:
                with schema_context(tenant.schema_name):
                    users = User.objects.filter(role__isnull=False).select_related('tenant')
                    for user in users:
                        all_users.append((user, tenant))
        
        self.stdout.write(f"  Found {len(all_users)} users with roles")
        
        role_mapping = {
            'clerk': 'clerk',
            'kebele_leader': 'kebele_leader',
            'subcity_admin': 'subcity_admin',
            'subcity_system_admin': 'subcity_system_admin',
            'city_admin': 'city_admin',
            'city_system_admin': 'city_system_admin'
        }
        
        assigned_count = 0
        with schema_context(get_public_schema_name()):
            for user, user_tenant in all_users:
                group_name = role_mapping.get(user.role)
                if not group_name:
                    self.stdout.write(f"    ⚠️ No mapping for role: {user.role} (user: {user.email})")
                    continue
                
                try:
                    group = Group.objects.get(name=group_name)
                    tenant_group = TenantGroup.objects.get(group=group)
                    
                    # Check tenant type compatibility
                    if user_tenant.type not in tenant_group.allowed_tenant_types:
                        self.stdout.write(f"    ⚠️ User {user.email} role {user.role} not compatible with {user_tenant.type} tenant")
                        continue
                    
                    # Clear existing group memberships and add to correct group
                    user.groups.clear()
                    group.user_set.add(user)
                    
                    # Update GroupMembership
                    GroupMembership.objects.filter(user_email=user.email).delete()
                    GroupMembership.objects.create(
                        user_email=user.email,
                        user_tenant_id=user_tenant.id,
                        user_tenant_schema=user_tenant.schema_name,
                        group=tenant_group,
                        assigned_by_email='system',
                        reason=f'Automatic reassignment for role: {user.role}',
                        is_primary=True,
                        is_active=True
                    )
                    
                    assigned_count += 1
                    self.stdout.write(f"    ✅ Assigned {user.email} ({user.role}) to {group_name}")
                    
                except Exception as e:
                    self.stdout.write(f"    ❌ Error assigning {user.email}: {e}")
        
        self.stdout.write(f"📊 Reassigned {assigned_count} users")
