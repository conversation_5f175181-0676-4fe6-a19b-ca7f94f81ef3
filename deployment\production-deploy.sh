#!/bin/bash
# GoID Complete Production Deployment Script
# This script handles the entire deployment process from build to deploy

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_HUB_USERNAME=""
VERSION_TYPE="patch"
SERVICES="all"
SKIP_BUILD=false
SKIP_DEPLOY=false
ENV_FILE="deployment/.env.production"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to show usage
show_usage() {
    echo "GoID Complete Production Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --username USERNAME  Docker Hub username (required)"
    echo "  -t, --type TYPE         Version increment type (major|minor|patch) [default: patch]"
    echo "  -s, --services SERVICES Services to deploy (backend|frontend|biometric-service|all) [default: all]"
    echo "  -e, --env-file FILE     Environment file [default: deployment/.env.production]"
    echo "  --skip-build           Skip building images (use existing)"
    echo "  --skip-deploy          Skip deployment (build only)"
    echo "  --build-only           Same as --skip-deploy"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --username myuser                    # Complete deployment with patch increment"
    echo "  $0 -u myuser -t minor                   # Deploy with minor version increment"
    echo "  $0 -u myuser --skip-build               # Deploy using existing images"
    echo "  $0 -u myuser --build-only               # Build images only, don't deploy"
    echo "  $0 -u myuser -s \"backend frontend\"     # Deploy specific services only"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--username)
            DOCKER_HUB_USERNAME="$2"
            shift 2
            ;;
        -t|--type)
            VERSION_TYPE="$2"
            shift 2
            ;;
        -s|--services)
            SERVICES="$2"
            shift 2
            ;;
        -e|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-deploy|--build-only)
            SKIP_DEPLOY=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$DOCKER_HUB_USERNAME" ]]; then
    print_error "Docker Hub username is required"
    print_warning "Use --username option to specify your Docker Hub username"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if required scripts exist
if [[ ! -f "deployment/build.sh" ]]; then
    print_error "Build script not found: deployment/build.sh"
    exit 1
fi

if [[ ! -f "deployment/deploy.sh" ]]; then
    print_error "Deploy script not found: deployment/deploy.sh"
    exit 1
fi

print_header "GoID Production Deployment"
print_status "Docker Hub Username: $DOCKER_HUB_USERNAME"
print_status "Version Type: $VERSION_TYPE"
print_status "Services: $SERVICES"
print_status "Environment File: $ENV_FILE"
print_status "Skip Build: $SKIP_BUILD"
print_status "Skip Deploy: $SKIP_DEPLOY"

# Step 1: Build and push images (if not skipped)
if [[ "$SKIP_BUILD" == false ]]; then
    print_header "Step 1: Building and Pushing Images"
    
    BUILD_CMD="./deployment/build.sh --username $DOCKER_HUB_USERNAME --type $VERSION_TYPE"
    
    if [[ "$SERVICES" != "all" ]]; then
        # Build each service individually
        for service in $SERVICES; do
            print_status "Building service: $service"
            if ! ./deployment/build.sh --service "$service" --username "$DOCKER_HUB_USERNAME" --type "$VERSION_TYPE"; then
                print_error "Failed to build service: $service"
                exit 1
            fi
        done
    else
        print_status "Building all services..."
        if ! eval $BUILD_CMD; then
            print_error "Failed to build services"
            exit 1
        fi
    fi
    
    print_success "All images built and pushed successfully!"
else
    print_warning "Skipping build step - using existing images"
fi

# Step 2: Generate environment file if it doesn't exist
if [[ ! -f "$ENV_FILE" ]]; then
    print_header "Step 2: Generating Environment Configuration"
    print_status "Environment file not found, generating template..."
    
    if ! ./deployment/deploy.sh generate --env-file "$ENV_FILE"; then
        print_error "Failed to generate environment file"
        exit 1
    fi
    
    print_warning "Please edit $ENV_FILE with your production settings"
    print_warning "Especially update: DB_PASSWORD, SECRET_KEY, ALLOWED_HOSTS"
    print_status "After editing the file, run this script again to continue deployment"
    exit 0
fi

# Step 3: Update environment file with Docker Hub username
print_header "Step 3: Updating Environment Configuration"
print_status "Updating Docker Hub username in environment file..."

# Update DOCKER_HUB_USERNAME in env file
if grep -q "DOCKER_HUB_USERNAME=" "$ENV_FILE"; then
    sed -i.bak "s/DOCKER_HUB_USERNAME=.*/DOCKER_HUB_USERNAME=$DOCKER_HUB_USERNAME/" "$ENV_FILE"
else
    echo "DOCKER_HUB_USERNAME=$DOCKER_HUB_USERNAME" >> "$ENV_FILE"
fi

print_success "Environment configuration updated"

# Step 4: Deploy services (if not skipped)
if [[ "$SKIP_DEPLOY" == false ]]; then
    print_header "Step 4: Deploying Services"
    
    # Generate docker-compose file
    print_status "Generating docker-compose configuration..."
    if ! ./deployment/deploy.sh generate --env-file "$ENV_FILE"; then
        print_error "Failed to generate docker-compose file"
        exit 1
    fi
    
    # Deploy services
    DEPLOY_CMD="./deployment/deploy.sh deploy --env-file $ENV_FILE"
    
    if [[ "$SERVICES" != "all" ]]; then
        DEPLOY_CMD="$DEPLOY_CMD --services \"$SERVICES\""
    fi
    
    print_status "Deploying services..."
    if ! eval $DEPLOY_CMD; then
        print_error "Failed to deploy services"
        exit 1
    fi
    
    print_success "Services deployed successfully!"
    
    # Step 5: Show deployment status
    print_header "Step 5: Deployment Status"
    print_status "Checking service status..."
    ./deployment/deploy.sh status --env-file "$ENV_FILE"
    
    print_success "Deployment completed successfully!"
    print_status "Your GoID system is now running in production mode"
    print_status ""
    print_status "Useful commands:"
    print_status "  Check status: ./deployment/deploy.sh status"
    print_status "  View logs:    ./deployment/deploy.sh logs"
    print_status "  Stop services: ./deployment/deploy.sh stop"
    
else
    print_warning "Skipping deployment step - images built only"
    print_status "To deploy later, run: ./deployment/deploy.sh deploy --env-file $ENV_FILE"
fi

print_header "Deployment Complete"
