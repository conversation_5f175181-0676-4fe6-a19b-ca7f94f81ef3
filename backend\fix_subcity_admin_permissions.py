#!/usr/bin/env python
"""
Fix subcity admin permissions by ensuring they have the required group-based permissions.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django_tenants.utils import schema_context, get_public_schema_name
from users.models import User
from users.models_groups import TenantGroup, GroupMembership
from users.utils.group_permissions import create_management_group, get_permission_sets
from tenants.models import Tenant


def create_required_permissions():
    """Create the required permissions if they don't exist."""
    print("🔧 Creating required permissions...")
    
    # Get User content type
    user_content_type = ContentType.objects.get_for_model(User)
    
    required_permissions = [
        ('manage_users', 'Can manage users'),
        ('manage_citizens', 'Can manage citizens'),
        ('manage_idcards', 'Can manage ID cards'),
        ('view_citizens', 'Can view citizens'),
        ('view_idcards', 'Can view ID cards'),
        ('view_reports', 'Can view reports'),
        ('manage_tenants', 'Can manage tenants'),
        ('view_all_data', 'Can view all data'),
        ('view_child_kebeles_data', 'Can view child kebeles data'),
        ('create_kebele_users', 'Can create kebele users'),
        ('print_id_cards', 'Can print ID cards'),
        ('approve_id_cards', 'Can approve ID cards'),
    ]
    
    created_count = 0
    for codename, name in required_permissions:
        permission, created = Permission.objects.get_or_create(
            codename=codename,
            content_type=user_content_type,
            defaults={'name': name}
        )
        if created:
            print(f"  ✅ Created permission: {codename}")
            created_count += 1
        else:
            print(f"  ℹ️ Permission already exists: {codename}")
    
    print(f"📊 Created {created_count} new permissions")
    return created_count


def create_subcity_admin_group():
    """Create or update subcity admin group with proper permissions."""
    print("🏗️ Creating/updating subcity admin group...")
    
    with schema_context(get_public_schema_name()):
        # Create or get the subcity admin group
        group_name = "SubCity Administrators"
        group, created = Group.objects.get_or_create(name=group_name)
        
        if created:
            print(f"  ✅ Created group: {group_name}")
        else:
            print(f"  ℹ️ Using existing group: {group_name}")
        
        # Define required permissions for subcity admin
        required_permission_codenames = [
            'manage_users',
            'manage_citizens', 
            'manage_idcards',
            'view_citizens',
            'view_idcards',
            'view_reports',
            'view_child_kebeles_data',
            'create_kebele_users',
            'print_id_cards',
            'approve_id_cards',
            'view_citizens_list',
            'view_citizen_details',
            'register_citizens',
            'generate_id_cards',
            'view_id_cards_list',
            'verify_documents',
            'view_kebele_reports',
            'view_subcity_reports',
            'navigate_to_dashboard',
            'view_subcity_dashboard',
            'navigate_to_citizens',
            'navigate_to_id_cards',
            'view_user_management',
            'view_subcity_management',
            'view_kebele_management',
            'change_user',
            'view_user',
        ]
        
        # Get existing permissions
        existing_permissions = set(group.permissions.values_list('codename', flat=True))
        
        # Add missing permissions
        added_count = 0
        for codename in required_permission_codenames:
            if codename not in existing_permissions:
                try:
                    permission = Permission.objects.get(codename=codename)
                    group.permissions.add(permission)
                    print(f"  ➕ Added permission: {codename}")
                    added_count += 1
                except Permission.DoesNotExist:
                    print(f"  ⚠️ Permission not found: {codename}")
        
        print(f"📊 Added {added_count} permissions to {group_name}")
        return group


def assign_subcity_admins_to_group(group):
    """Assign all subcity admin users to the group."""
    print("👥 Assigning subcity admin users to group...")
    
    # Find all subcity admin users
    subcity_admin_users = []
    
    # Check public schema first
    with schema_context(get_public_schema_name()):
        public_users = User.objects.filter(role='subcity_admin', is_active=True)
        subcity_admin_users.extend(public_users)
    
    # Check tenant schemas
    for tenant in Tenant.objects.filter(type='subcity'):
        try:
            with schema_context(tenant.schema_name):
                tenant_users = User.objects.filter(role='subcity_admin', is_active=True)
                subcity_admin_users.extend(tenant_users)
        except Exception as e:
            print(f"  ⚠️ Error checking tenant {tenant.name}: {e}")
    
    print(f"  Found {len(subcity_admin_users)} subcity admin users")
    
    # Assign users to group
    assigned_count = 0
    with schema_context(get_public_schema_name()):
        for user in subcity_admin_users:
            try:
                # Add user to Django group
                group.user_set.add(user)
                
                # Also create GroupMembership record for our custom system
                membership, created = GroupMembership.objects.get_or_create(
                    user_email=user.email,
                    group=group,
                    defaults={
                        'user_tenant_id': user.tenant.id if user.tenant else None,
                        'user_tenant_schema': user.tenant.schema_name if user.tenant else None,
                        'is_primary': True,
                        'assigned_by_email': 'system',
                        'reason': 'Automatic assignment for subcity admin role'
                    }
                )
                
                if created:
                    print(f"  ✅ Assigned {user.email} to group")
                    assigned_count += 1
                else:
                    print(f"  ℹ️ {user.email} already in group")
                    
            except Exception as e:
                print(f"  ❌ Error assigning {user.email}: {e}")
    
    print(f"📊 Assigned {assigned_count} users to group")
    return assigned_count


def verify_user_permissions(user_email):
    """Verify that a specific user has the required permissions."""
    print(f"🔍 Verifying permissions for {user_email}...")
    
    try:
        # Try to find user in public schema first
        with schema_context(get_public_schema_name()):
            try:
                user = User.objects.get(email=user_email)
                print(f"  Found user in public schema")
            except User.DoesNotExist:
                user = None
        
        # If not found in public, check tenant schemas
        if not user:
            for tenant in Tenant.objects.all():
                try:
                    with schema_context(tenant.schema_name):
                        user = User.objects.get(email=user_email)
                        print(f"  Found user in {tenant.name} schema")
                        break
                except User.DoesNotExist:
                    continue
        
        if not user:
            print(f"  ❌ User {user_email} not found")
            return False
        
        print(f"  User role: {user.role}")
        print(f"  User tenant: {user.tenant.name if user.tenant else 'None'}")
        
        # Check group memberships
        with schema_context(get_public_schema_name()):
            user_groups = user.groups.all()
            print(f"  User groups: {[g.name for g in user_groups]}")
            
            # Check specific permissions
            required_permissions = ['manage_users', 'manage_citizens', 'manage_idcards']
            for perm in required_permissions:
                has_perm = user.has_group_permission(perm)
                status = "✅" if has_perm else "❌"
                print(f"  {status} {perm}: {has_perm}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error verifying user: {e}")
        return False


def main():
    """Main function to fix subcity admin permissions."""
    print("🚀 Fixing subcity admin permissions...")
    print("="*50)
    
    try:
        # Step 1: Create required permissions
        create_required_permissions()
        print()
        
        # Step 2: Create/update subcity admin group
        group = create_subcity_admin_group()
        print()
        
        # Step 3: Assign users to group
        assign_subcity_admins_to_group(group)
        print()
        
        # Step 4: Verify a specific user (you can change this email)
        test_user_email = input("Enter subcity admin email to verify (or press Enter to skip): ").strip()
        if test_user_email:
            verify_user_permissions(test_user_email)
        
        print()
        print("="*50)
        print("✅ Subcity admin permissions fix completed!")
        print()
        print("Next steps:")
        print("1. Log out and log back in to refresh permissions")
        print("2. Test access to Citizens, ID Cards, and User Management pages")
        print("3. If issues persist, check the browser console for errors")
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
