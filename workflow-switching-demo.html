<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kebele Workflow Switching Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 40px;
        }
        .workflow-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .workflow-card {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 25px;
            transition: all 0.3s ease;
        }
        .workflow-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .workflow-card.centralized {
            border-color: #2196F3;
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
        }
        .workflow-card.autonomous {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
        }
        .workflow-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .workflow-title .icon {
            font-size: 1.2em;
            margin-right: 10px;
        }
        .workflow-flow {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .roles-section {
            margin-top: 20px;
        }
        .roles-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .role-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .role-name {
            font-weight: bold;
            color: #1976D2;
        }
        .permissions {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196F3;
        }
        .feature h3 {
            margin-top: 0;
            color: #1976D2;
        }
        .demo-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
            text-align: center;
        }
        .demo-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }
        .demo-button.secondary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        .demo-button.secondary:hover {
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }
        .setup-steps {
            background: #fff3e0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ff9800;
            margin: 20px 0;
        }
        .setup-steps h3 {
            margin-top: 0;
            color: #f57c00;
        }
        .setup-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .setup-steps li {
            margin: 10px 0;
        }
        .code {
            background: #263238;
            color: #4fc3f7;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Kebele Workflow Switching System</h1>
            <p>Dynamic workflow management for ID card processing</p>
        </div>
        
        <div class="content">
            <h2>🎯 Workflow Types Comparison</h2>
            
            <div class="workflow-comparison">
                <div class="workflow-card centralized">
                    <div class="workflow-title">
                        <span class="icon">🏢</span>
                        Centralized Workflow
                    </div>
                    <div class="workflow-flow">
                        Clerk → Kebele Leader → Subcity Admin → Print
                    </div>
                    <div class="roles-section">
                        <div class="roles-title">Available Roles:</div>
                        <div class="role-item">
                            <div class="role-name">Clerk (Kebele)</div>
                            <div class="permissions">Register citizens, generate ID cards, send for approval</div>
                        </div>
                        <div class="role-item">
                            <div class="role-name">Kebele Leader (Kebele)</div>
                            <div class="permissions">Approve ID cards, manage transfers & clearances</div>
                        </div>
                        <div class="role-item">
                            <div class="role-name">Subcity Admin (Subcity)</div>
                            <div class="permissions">Manage kebeles, create users</div>
                        </div>
                        <div class="role-item">
                            <div class="role-name">Print ID Cards (Subcity)</div>
                            <div class="permissions">Centralized printing management</div>
                        </div>
                    </div>
                </div>
                
                <div class="workflow-card autonomous">
                    <div class="workflow-title">
                        <span class="icon">🏠</span>
                        Autonomous Workflow
                    </div>
                    <div class="workflow-flow">
                        Clerk → Kebele Leader → Print
                    </div>
                    <div class="roles-section">
                        <div class="roles-title">Available Roles:</div>
                        <div class="role-item">
                            <div class="role-name">Clerk (Kebele)</div>
                            <div class="permissions">Register citizens, generate ID cards, send for approval</div>
                        </div>
                        <div class="role-item">
                            <div class="role-name">Kebele Leader (Kebele)</div>
                            <div class="permissions">Approve ID cards, apply security patterns & signatures</div>
                        </div>
                        <div class="role-item">
                            <div class="role-name">Print ID Cards (Kebele)</div>
                            <div class="permissions">Local printing capability</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="features">
                <div class="feature">
                    <h3>🔄 Dynamic Switching</h3>
                    <p>Kebeles can switch between centralized and autonomous workflows based on their capabilities and requirements.</p>
                </div>
                <div class="feature">
                    <h3>🔒 Role-Based Security</h3>
                    <p>Permissions automatically adjust based on workflow type, ensuring proper access control and security.</p>
                </div>
                <div class="feature">
                    <h3>📊 Audit Trail</h3>
                    <p>All workflow changes are logged with timestamps, reasons, and user information for compliance.</p>
                </div>
                <div class="feature">
                    <h3>⚡ Performance</h3>
                    <p>Autonomous workflows reduce processing time by eliminating the subcity approval step.</p>
                </div>
            </div>
            
            <div class="setup-steps">
                <h3>🚀 Quick Setup</h3>
                <ol>
                    <li>Run the setup script: <code class="code">python setup_workflow_system.py</code></li>
                    <li>Access Kebele User Management as a subcity admin</li>
                    <li>Look for the "Workflow Management" section</li>
                    <li>Click "Switch Workflow" on any kebele card</li>
                    <li>Test switching between workflows</li>
                </ol>
            </div>
            
            <div class="demo-section">
                <h2>🎮 Try the System</h2>
                <p>Experience the workflow switching functionality in action</p>
                
                <a href="http://localhost:3000/users/kebele-management" class="demo-button" target="_blank">
                    🔧 Open Kebele Management
                </a>
                
                <a href="WORKFLOW_SWITCHING_GUIDE.md" class="demo-button secondary" target="_blank">
                    📚 Read Documentation
                </a>
                
                <div style="margin-top: 20px;">
                    <div class="code">
                        # Run setup script
                        python setup_workflow_system.py
                        
                        # Test the system
                        python test_workflow_switching.py
                    </div>
                </div>
            </div>
            
            <div class="feature">
                <h3>🎯 Key Benefits</h3>
                <ul>
                    <li><strong>Flexibility</strong>: Adapt workflows to local capabilities</li>
                    <li><strong>Efficiency</strong>: Reduce processing time with autonomous workflows</li>
                    <li><strong>Control</strong>: Maintain quality with centralized workflows</li>
                    <li><strong>Security</strong>: Role-based permissions ensure proper access</li>
                    <li><strong>Audit</strong>: Complete tracking of all workflow changes</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
