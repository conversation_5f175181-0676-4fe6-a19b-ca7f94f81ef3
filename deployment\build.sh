#!/bin/bash
# GoID Build Script - Build and push Docker images

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
SERVICE="all"
VERSION_TYPE="patch"
PUSH=true
DOCKER_HUB_USERNAME="aragawmebratu"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --service SERVICE     Service to build (backend|frontend|biometric-service|all) [default: all]"
    echo "  -v, --version VERSION     Specific version to use (otherwise auto-increment)"
    echo "  -t, --type TYPE          Version increment type (major|minor|patch) [default: patch]"
    echo "  -u, --username USERNAME  Docker Hub username"
    echo "  --no-push               Build only, don't push to Docker Hub"
    echo "  --list                  List current versions"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --username myuser                    # Build all services with patch increment"
    echo "  $0 -s backend -t minor --username myuser # Build backend with minor version increment"
    echo "  $0 --list                               # List current versions"
    echo "  $0 --no-push --username myuser          # Build without pushing to Docker Hub"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--service)
            SERVICE="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -t|--type)
            VERSION_TYPE="$2"
            shift 2
            ;;
        -u|--username)
            DOCKER_HUB_USERNAME="$2"
            shift 2
            ;;
        --no-push)
            PUSH=false
            shift
            ;;
        --list)
            # Detect Python command for list operation
            if command -v python3 &> /dev/null; then
                python3 deployment/build-and-push.py --list
            elif command -v python &> /dev/null; then
                python deployment/build-and-push.py --list
            else
                echo "Python is not installed or not in PATH"
                exit 1
            fi
            exit 0
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker Hub username is provided
if [[ -z "$DOCKER_HUB_USERNAME" && "$PUSH" == true ]]; then
    print_error "Docker Hub username is required for pushing images"
    print_warning "Use --username option or --no-push to build without pushing"
    exit 1
fi

# Detect Python command
PYTHON_CMD=""
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    print_error "Python is not installed or not in PATH"
    exit 1
fi

# Update versions.json with Docker Hub username
if [[ -n "$DOCKER_HUB_USERNAME" ]]; then
    print_status "Updating Docker Hub repositories in configuration..."

    $PYTHON_CMD -c "
import json
with open('deployment/versions.json', 'r') as f:
    config = json.load(f)

config['services']['backend']['docker_hub_repo'] = '${DOCKER_HUB_USERNAME}/goid-production'
config['services']['frontend']['docker_hub_repo'] = '${DOCKER_HUB_USERNAME}/goid-production'

with open('deployment/versions.json', 'w') as f:
    json.dump(config, f, indent=2)
"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Login to Docker Hub if pushing
if [[ "$PUSH" == true ]]; then
    print_status "Logging in to Docker Hub..."
    if ! docker login; then
        print_error "Failed to login to Docker Hub"
        exit 1
    fi
fi

# Build command
BUILD_CMD="$PYTHON_CMD deployment/build-and-push.py --service $SERVICE --version-type $VERSION_TYPE"

if [[ -n "$VERSION" ]]; then
    BUILD_CMD="$BUILD_CMD --version $VERSION"
fi

if [[ "$PUSH" == false ]]; then
    BUILD_CMD="$BUILD_CMD --no-push"
fi

print_status "Starting build process..."
print_status "Service: $SERVICE"
print_status "Version type: $VERSION_TYPE"
print_status "Push to Docker Hub: $PUSH"

# Execute build
if eval $BUILD_CMD; then
    print_success "Build completed successfully!"
    
    if [[ "$PUSH" == true ]]; then
        print_success "Images pushed to Docker Hub"
        print_status "You can now deploy using: ./deployment/deploy.sh"
    else
        print_warning "Images built locally only (not pushed to Docker Hub)"
    fi
else
    print_error "Build failed!"
    exit 1
fi
