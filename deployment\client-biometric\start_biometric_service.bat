@echo off
REM GoID Client Biometric Service Startup Script for Windows

echo ========================================
echo GoID Client Biometric Service
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.10 or higher
    pause
    exit /b 1
)

REM Check if Java is installed
java -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java is not installed or not in PATH
    echo Please install Java Runtime Environment (JRE) 8 or higher
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "enhanced_capture_service.py" (
    echo [ERROR] Service file not found: enhanced_capture_service.py
    pause
    exit /b 1
)

if not exist "config.json" (
    echo [WARNING] Configuration file not found: config.json
    echo Creating default configuration...
    copy config.json.template config.json
)

if not exist "fingerPrint\GonderFingerPrint.jar" (
    echo [ERROR] Biometric JAR file not found: fingerPrint\GonderFingerPrint.jar
    echo Please ensure the fingerPrint folder contains the required JAR file
    pause
    exit /b 1
)

REM Install dependencies if requirements.txt exists
if exist "requirements.txt" (
    echo [INFO] Installing Python dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [WARNING] Some dependencies failed to install
        echo Service may not work properly
    )
)

REM Check if port 8002 is available
netstat -an | find "8002" >nul
if not errorlevel 1 (
    echo [WARNING] Port 8002 appears to be in use
    echo The service may fail to start
)

echo [INFO] Starting GoID Client Biometric Service...
echo [INFO] Service will be available at: http://localhost:8002
echo [INFO] Press Ctrl+C to stop the service
echo.

REM Start the service
python enhanced_capture_service.py

echo.
echo [INFO] Service stopped
pause
