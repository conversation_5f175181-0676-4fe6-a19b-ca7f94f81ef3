#!/usr/bin/env python3
"""
Fix designated_printer role permissions.

This script will:
1. Ensure designated_printer role has correct permissions
2. Update group permissions for print_id_cards group
3. Test the permissions
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from users.models_groups import TenantGroup
from django.contrib.auth import get_user_model

User = get_user_model()

def fix_designated_printer_permissions():
    """Fix designated_printer role permissions."""
    
    print("🔧 Fixing Designated Printer Permissions")
    print("="*50)
    
    try:
        # Step 1: Get the print_id_cards group
        print("\n1️⃣ Getting print_id_cards group...")
        
        print_group = Group.objects.filter(name='print_id_cards').first()
        if not print_group:
            print("❌ print_id_cards group not found!")
            return
        
        print(f"✅ Found print_id_cards group (ID: {print_group.id})")
        
        # Step 2: Add required permissions for designated_printer role
        print("\n2️⃣ Adding required permissions...")
        
        # Get content types
        user_ct = ContentType.objects.get_for_model(User)
        
        # Required permissions for designated_printer
        required_permissions = [
            ('print_id_cards', 'Can print ID cards'),
            ('print_idcards', 'Can print ID cards (alt)'),
            ('view_printing_queue', 'Can view printing queue'),
            ('view_kebele_dashboard', 'Can view kebele dashboard'),
            ('view_id_cards_list', 'Can view ID cards list'),
        ]
        
        for codename, name in required_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                defaults={
                    'name': name,
                    'content_type': user_ct
                }
            )
            
            # Add permission to group
            print_group.permissions.add(permission)
            
            if created:
                print(f"✅ Created permission: {codename}")
            else:
                print(f"⏭️  Permission exists: {codename}")
        
        print(f"✅ print_id_cards group now has {print_group.permissions.count()} permissions")
        
        # Step 3: Test with a designated_printer user
        print("\n3️⃣ Testing with designated_printer user...")
        
        # Find a designated_printer user
        test_user = User.objects.filter(role='designated_printer').first()
        if test_user:
            print(f"Testing with user: {test_user.email}")
            print(f"User role: {test_user.role}")
            print(f"User groups: {[g.name for g in test_user.groups.all()]}")
            
            # Check if user has print permissions
            has_print_permission = test_user.has_perm('users.print_id_cards')
            print(f"Has print_id_cards permission: {has_print_permission}")
            
            # Check group membership
            is_in_print_group = test_user.groups.filter(name='print_id_cards').exists()
            print(f"Is in print_id_cards group: {is_in_print_group}")
            
            if not is_in_print_group:
                print("⚠️ User not in print_id_cards group, adding...")
                print_group.user_set.add(test_user)
                print("✅ Added user to print_id_cards group")
        else:
            print("ℹ️ No designated_printer users found to test")
        
        # Step 4: Verify TenantGroup configuration
        print("\n4️⃣ Verifying TenantGroup configuration...")
        
        tenant_group = TenantGroup.objects.filter(group=print_group).first()
        if tenant_group:
            print(f"TenantGroup ID: {tenant_group.id}")
            print(f"Description: {tenant_group.description}")
            print(f"Active: {tenant_group.is_active}")
            print(f"Allowed tenant types: {tenant_group.allowed_tenant_types}")
            
            # Ensure it's active and allows kebele tenants
            if not tenant_group.is_active:
                tenant_group.is_active = True
                tenant_group.save()
                print("✅ Activated TenantGroup")
            
            if 'kebele' not in tenant_group.allowed_tenant_types:
                tenant_group.allowed_tenant_types.append('kebele')
                tenant_group.save()
                print("✅ Added kebele to allowed tenant types")
        else:
            print("❌ TenantGroup for print_id_cards not found!")
        
        print("\n✅ Designated Printer Permissions Fix Complete!")
        print("\n🎯 Next Steps:")
        print("1. Restart Django server")
        print("2. Login as designated_printer user")
        print("3. Should only see Print Queue navigation")
        print("4. Should be able to access /idcards/printing-queue")
        
    except Exception as e:
        print(f"❌ Error fixing designated printer permissions: {e}")
        import traceback
        traceback.print_exc()

def test_navigation_permissions():
    """Test navigation permissions for designated_printer."""
    print("\n🧪 Testing Navigation Permissions...")
    
    # Find designated_printer user
    test_user = User.objects.filter(role='designated_printer').first()
    if not test_user:
        print("❌ No designated_printer user found for testing")
        return
    
    print(f"Testing navigation for: {test_user.email}")
    
    # Test permissions
    permissions_to_test = [
        'print_id_cards',
        'print_idcards', 
        'view_printing_queue',
        'view_kebele_dashboard',
        'view_citizens',  # Should NOT have this
        'view_id_cards_list'
    ]
    
    for perm in permissions_to_test:
        has_perm = test_user.has_perm(f'users.{perm}')
        status = "✅" if has_perm else "❌"
        print(f"  {status} {perm}: {has_perm}")
    
    # Test group membership
    groups = [g.name for g in test_user.groups.all()]
    print(f"User groups: {groups}")
    
    # Expected: should only be in print_id_cards group
    if groups == ['print_id_cards']:
        print("✅ User has correct group membership")
    else:
        print(f"⚠️ Unexpected groups: {groups}")

if __name__ == "__main__":
    fix_designated_printer_permissions()
    test_navigation_permissions()
