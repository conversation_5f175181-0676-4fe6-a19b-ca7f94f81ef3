#!/usr/bin/env python3
"""
GoID Production Deployment Script
Generates docker-compose files and manages deployments
"""

import json
import os
import sys
import subprocess
import argparse
from pathlib import Path
from string import Template

class DeploymentManager:
    def __init__(self, config_path="deployment/versions.json"):
        self.config_path = config_path
        self.config = self.load_config()
        self.root_dir = Path(__file__).parent.parent
        self.deployment_dir = Path(__file__).parent
        
    def load_config(self):
        """Load version configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Configuration file {self.config_path} not found")
            sys.exit(1)
    
    def load_env_file(self, env_file):
        """Load environment variables from file"""
        env_vars = {}
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value
        return env_vars
    
    def generate_docker_compose(self, env_file=None, output_file=None):
        """Generate docker-compose.yml from template"""
        template_path = self.deployment_dir / "docker-compose.production.template.yml"
        
        if not template_path.exists():
            print(f"❌ Template file {template_path} not found")
            return False
            
        # Load environment variables
        env_vars = {}
        if env_file:
            env_vars = self.load_env_file(env_file)
        
        # Set default values from config
        env_vars.setdefault('DOCKER_HUB_USERNAME', 'your-dockerhub-username')
        env_vars.setdefault('BACKEND_VERSION', self.config['services']['backend']['current_version'])
        env_vars.setdefault('FRONTEND_VERSION', self.config['services']['frontend']['current_version'])
        env_vars.setdefault('BIOMETRIC_VERSION', self.config['services']['biometric-service']['current_version'])
        
        # Read template
        with open(template_path, 'r') as f:
            template_content = f.read()
        
        # Replace variables
        template = Template(template_content)
        try:
            docker_compose_content = template.substitute(env_vars)
        except KeyError as e:
            print(f"❌ Missing environment variable: {e}")
            return False
        
        # Write output file
        if output_file is None:
            output_file = self.deployment_dir / "docker-compose.production.yml"
        
        with open(output_file, 'w') as f:
            f.write(docker_compose_content)
        
        print(f"✅ Generated {output_file}")
        return True
    
    def run_command(self, command, cwd=None):
        """Run shell command"""
        print(f"🔧 Running: {command}")
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                check=True, 
                cwd=cwd or self.root_dir
            )
            return result.returncode == 0
        except subprocess.CalledProcessError as e:
            print(f"❌ Command failed: {e}")
            return False
    
    def pull_images(self, docker_compose_file=None):
        """Pull latest images from Docker Hub"""
        if docker_compose_file is None:
            docker_compose_file = self.deployment_dir / "docker-compose.production.yml"
            
        print("📥 Pulling latest images...")
        return self.run_command(f"docker-compose -f {docker_compose_file} pull")
    
    def deploy(self, docker_compose_file=None, services=None):
        """Deploy services using docker-compose"""
        if docker_compose_file is None:
            docker_compose_file = self.deployment_dir / "docker-compose.production.yml"
            
        if not os.path.exists(docker_compose_file):
            print(f"❌ Docker compose file {docker_compose_file} not found")
            return False
        
        # Pull latest images first
        if not self.pull_images(docker_compose_file):
            print("⚠️  Failed to pull some images, continuing with deployment...")
        
        # Deploy services
        service_list = " ".join(services) if services else ""
        print(f"🚀 Deploying services: {service_list or 'all'}")
        
        return self.run_command(f"docker-compose -f {docker_compose_file} up -d {service_list}")
    
    def stop(self, docker_compose_file=None):
        """Stop all services"""
        if docker_compose_file is None:
            docker_compose_file = self.deployment_dir / "docker-compose.production.yml"
            
        print("🛑 Stopping services...")
        return self.run_command(f"docker-compose -f {docker_compose_file} down")
    
    def status(self, docker_compose_file=None):
        """Show service status"""
        if docker_compose_file is None:
            docker_compose_file = self.deployment_dir / "docker-compose.production.yml"
            
        print("📊 Service status:")
        return self.run_command(f"docker-compose -f {docker_compose_file} ps")
    
    def logs(self, service=None, docker_compose_file=None, follow=False):
        """Show service logs"""
        if docker_compose_file is None:
            docker_compose_file = self.deployment_dir / "docker-compose.production.yml"
            
        follow_flag = "-f" if follow else ""
        service_name = service or ""
        
        print(f"📋 Showing logs for {service or 'all services'}:")
        return self.run_command(f"docker-compose -f {docker_compose_file} logs {follow_flag} {service_name}")

def main():
    parser = argparse.ArgumentParser(description="Deploy GoID to production")
    parser.add_argument("action", choices=["generate", "deploy", "stop", "status", "logs", "pull"], 
                       help="Action to perform")
    parser.add_argument("--env-file", help="Environment file to use")
    parser.add_argument("--output", help="Output file for generated docker-compose")
    parser.add_argument("--services", nargs="+", help="Specific services to deploy")
    parser.add_argument("--service", help="Service for logs")
    parser.add_argument("--follow", action="store_true", help="Follow logs")
    parser.add_argument("--compose-file", help="Docker compose file to use")
    
    args = parser.parse_args()
    
    manager = DeploymentManager()
    
    if args.action == "generate":
        manager.generate_docker_compose(args.env_file, args.output)
    elif args.action == "deploy":
        manager.deploy(args.compose_file, args.services)
    elif args.action == "stop":
        manager.stop(args.compose_file)
    elif args.action == "status":
        manager.status(args.compose_file)
    elif args.action == "logs":
        manager.logs(args.service, args.compose_file, args.follow)
    elif args.action == "pull":
        manager.pull_images(args.compose_file)

if __name__ == "__main__":
    main()
