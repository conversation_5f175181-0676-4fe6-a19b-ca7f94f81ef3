# Autonomous Workflow Fix

## Problem
In the autonomous workflow, when a kebele leader approves an ID card, it should go directly to the local print queue and NOT be sent to the parent subcity. However, the system was still showing autonomous kebele ID cards to subcity admins, breaking the isolation principle.

## Root Cause
The issue was in the subcity admin views in `backend/tenants/views/tenant_idcard_views.py`. These methods were showing ID cards from ALL child kebeles without checking their workflow type:

1. **`cross_tenant_list` method** (line ~930): Used by subcity admins to view pending ID cards from child kebeles
2. **`_get_centralized_printing_queue` method** (line ~1092): Used for centralized printing queue
3. **Regular list method** (line ~1205): Used for general ID card listing

## Solution
Modified all three methods to check each kebele's workflow type and skip autonomous kebeles:

### 1. Fixed `cross_tenant_list` method
```python
# Before: Showed all kebele cards
for kebele in child_kebeles:
    # ... show all KEBELE_APPROVED cards

# After: Skip autonomous kebeles
for kebele in child_kebeles:
    workflow_type = self._get_tenant_workflow_type(kebele)
    if workflow_type == 'autonomous':
        print(f"⏭️ Skipping autonomous kebele {kebele.name}")
        continue
    # ... only show centralized kebele cards
```

### 2. Fixed `_get_centralized_printing_queue` method
```python
# Added workflow type check before processing each kebele
for kebele in child_kebeles:
    workflow_type = self._get_tenant_workflow_type(kebele)
    if workflow_type == 'autonomous':
        print(f"⏭️ Skipping autonomous kebele {kebele.name}")
        continue
    # ... only process centralized kebeles
```

### 3. Fixed regular list method
```python
# Added workflow type check in the main listing logic
for kebele in child_kebeles:
    workflow_type = self._get_tenant_workflow_type(kebele)
    if workflow_type == 'autonomous':
        print(f"⏭️ Skipping autonomous kebele {kebele.name}")
        continue
    # ... only show centralized kebele cards
```

## Workflow Comparison

### Autonomous Workflow (FIXED)
```
Clerk → Creates → DRAFT
  ↓
Clerk → Submits → PENDING_APPROVAL
  ↓
Kebele Leader → Approves → APPROVED (FINAL!)
  ↓ (kebele + subcity patterns applied locally)
  ↓ (NO subcity involvement)
Print Queue → Ready for Local Printing
```

### Centralized Workflow (Unchanged)
```
Clerk → Creates → DRAFT
  ↓
Clerk → Submits → PENDING_APPROVAL
  ↓
Kebele Leader → Approves → KEBELE_APPROVED
  ↓ (sent to subcity)
Subcity Admin → Approves → APPROVED (FINAL!)
  ↓ (subcity pattern + signature applied)
Print Queue → Ready for Centralized Printing
```

## Key Changes Made

1. **Added workflow type checking** in all subcity admin views
2. **Skip autonomous kebeles** when subcity admins query for ID cards
3. **Maintain isolation** between autonomous and centralized workflows
4. **Added debug logging** to track which kebeles are being processed

## Files Modified

- `backend/tenants/views/tenant_idcard_views.py`
  - `cross_tenant_list` method (lines ~923-955)
  - `_get_centralized_printing_queue` method (lines ~1092-1115)
  - Regular list method (lines ~1205-1244)

## Testing

Created a management command to test the fix:
- `backend/tenants/management/commands/test_autonomous_isolation.py`

Run the test with:
```bash
python manage.py test_autonomous_isolation
```

## Expected Results

After this fix:

1. **Autonomous kebeles**: ID cards stay within the kebele, subcity admins cannot see them
2. **Centralized kebeles**: ID cards flow to subcity as before
3. **Mixed environments**: Each kebele works according to its configured workflow type
4. **Print queues**: Autonomous kebeles have their own print queue, centralized kebeles use subcity print queue

## Verification

To verify the fix is working:

1. Check that autonomous kebele cards have `status=APPROVED` (not `KEBELE_APPROVED`)
2. Verify subcity admins cannot see autonomous kebele cards in their lists
3. Confirm autonomous kebeles have their own isolated print queue
4. Ensure centralized kebeles still work as before

The autonomous workflow now properly maintains kebele independence while preserving the centralized workflow for kebeles that need it.
