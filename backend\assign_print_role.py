#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to assign print_id_cards additional role to a user for testing.
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from users.models import User

def assign_print_role_to_user(email):
    """Assign print_id_cards additional role to a user."""
    try:
        user = User.objects.get(email=email)
        print(f"Found user: {user.email}")
        print(f"Current role: {user.role}")
        print(f"Current additional_roles: {user.additional_roles}")
        
        # Add print_id_cards to additional_roles if not already present
        additional_roles = user.additional_roles or []
        if 'print_id_cards' not in additional_roles:
            additional_roles.append('print_id_cards')
            user.additional_roles = additional_roles
            user.save()
            print(f"✅ Added print_id_cards role to {user.email}")
            print(f"Updated additional_roles: {user.additional_roles}")
        else:
            print(f"ℹ️  User {user.email} already has print_id_cards role")
            
    except User.DoesNotExist:
        print(f"❌ User with email {email} not found")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    # List all users to see available emails
    print("Available users:")
    for user in User.objects.all()[:10]:  # Show first 10 users
        print(f"  - {user.email} (role: {user.role}, additional_roles: {user.additional_roles})")
    
    print("\n" + "="*50)
    
    # Try to find a clerk user to assign print role to
    clerk_users = User.objects.filter(role='clerk')
    if clerk_users.exists():
        clerk = clerk_users.first()
        print(f"Assigning print_id_cards role to clerk: {clerk.email}")
        assign_print_role_to_user(clerk.email)
    else:
        print("No clerk users found")
