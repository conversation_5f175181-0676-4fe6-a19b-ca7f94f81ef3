# GoID Deployment Guide for University of Gondar

This guide is specifically configured for deploying GoID to the UoG server.

## 🎯 **Deployment Configuration**

### **Server Details**
- **IP Address**: `************`
- **Domain**: `goid.uog.edu.et`
- **Docker Hub**: `aragawmebratu/goid-production`
- **Platform**: Linux Server

### **Architecture**
```
┌─────────────────────────────────────────────────────────────┐
│                UoG Server (************)                   │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │  Frontend   │  │   Backend   │  │     Database        │ │
│  │   :3000     │  │    :8000    │  │   (PostgreSQL)      │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐                         │
│  │   Redis     │  │   pgAdmin   │                         │ │
│  │   :6379     │  │    :5050    │                         │ │
│  └─────────────┘  └─────────────┘                         │
└─────────────────────────────────────────────────────────────┘
                             │
                    ┌─────────────────┐
                    │ Internet/Network│
                    └─────────────────┘
                             │
┌─────────────────────────────────────────────────────────────┐
│                    Kebele Offices                          │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Browser   │  │ Biometric   │  │    USB Device       │ │
│  │goid.uog.edu.et│ │localhost:8002│ │   Futronic FS88H    │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 **Quick Deployment**

### **Option 1: Automated Deployment**
```bash
# Run the UoG-specific deployment script
./deployment/deploy-to-uog-server.sh
```

### **Option 2: Manual Deployment**
```bash
# 1. Build and push images
./deployment/build.sh --service backend --username aragawmebratu
./deployment/build.sh --service frontend --username aragawmebratu

# 2. Generate configuration
./deployment/deploy.sh generate

# 3. Deploy services
./deployment/deploy.sh deploy
```

## 📋 **Service Configuration**

### **Docker Images**
- **Backend**: `aragawmebratu/goid-production:backend-latest`
- **Frontend**: `aragawmebratu/goid-production:frontend-latest`

### **Access URLs**
- **Frontend (IP)**: http://************:3000
- **Frontend (Domain)**: https://goid.uog.edu.et
- **Backend API**: http://************:8000
- **Database**: ************:5432
- **pgAdmin**: http://************:5050

### **Environment Configuration**
```env
# Server Configuration
ALLOWED_HOSTS=************,goid.uog.edu.et,localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://************:3000,https://goid.uog.edu.et

# API URLs
REACT_APP_API_URL=http://************:8000
REACT_APP_BIOMETRIC_SERVICE_URL=http://localhost:8002
```

## 🖥️ **Client Installation**

### **Biometric Service Setup**
Each kebele office needs:

1. **Hardware Requirements**:
   - Windows PC
   - Futronic FS88H fingerprint scanner
   - USB connection
   - Internet access to UoG server

2. **Software Installation**:
   ```bash
   # Build client installer
   cd deployment/client-biometric
   python build_installer.py
   
   # Distribute GoID-Biometric-Service-Installer.zip to each kebele
   # Run installer on each PC
   ```

3. **Client Configuration**:
   - Server URL: `http://************:8000`
   - Local service: `localhost:8002`
   - Auto-start with Windows

## 🔧 **Management Commands**

### **Service Management**
```bash
# Check service status
./deployment/deploy.sh status

# View logs
./deployment/deploy.sh logs
./deployment/deploy.sh logs --service backend --follow

# Restart services
./deployment/deploy.sh stop
./deployment/deploy.sh deploy

# Update services
./deployment/build.sh --username aragawmebratu
./deployment/deploy.sh deploy
```

### **Database Management**
```bash
# Backup database
docker-compose -f deployment/docker-compose.production.yml exec db \
  pg_dump -U goid_user goid_db > backup_$(date +%Y%m%d).sql

# Access database via pgAdmin
# URL: http://************:5050
# Email: <EMAIL>
# Password: (set in .env.production)
```

## 🔐 **Security Configuration**

### **Firewall Rules**
```bash
# Allow required ports
sudo ufw allow 3000  # Frontend
sudo ufw allow 8000  # Backend API
sudo ufw allow 5432  # Database (restrict to internal network)
sudo ufw allow 5050  # pgAdmin (restrict to admin IPs)
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
```

### **SSL Certificate Setup**
```bash
# Install SSL certificate for goid.uog.edu.et
# Update nginx configuration for HTTPS
# Redirect HTTP to HTTPS
```

### **Production Security Checklist**
- [ ] Change default passwords in `.env.production`
- [ ] Configure SSL certificate for `goid.uog.edu.et`
- [ ] Set up firewall rules
- [ ] Configure database access restrictions
- [ ] Set up regular backups
- [ ] Monitor system logs
- [ ] Update system packages regularly

## 📊 **Monitoring and Maintenance**

### **Health Checks**
```bash
# Backend health
curl http://************:8000/api/health/

# Frontend health
curl http://************:3000/

# Database connection
docker-compose exec db psql -U goid_user -d goid_db -c "SELECT 1;"
```

### **Log Monitoring**
```bash
# Application logs
./deployment/deploy.sh logs --service backend
./deployment/deploy.sh logs --service frontend

# System logs
sudo journalctl -u docker
sudo journalctl -f
```

### **Performance Monitoring**
```bash
# Resource usage
docker stats

# Disk usage
df -h
docker system df

# Network connections
netstat -tulpn | grep -E ':(3000|8000|5432|5050)'
```

## 🔄 **Backup and Recovery**

### **Automated Backup Script**
```bash
#!/bin/bash
# Daily backup script
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/goid"

# Database backup
docker-compose -f deployment/docker-compose.production.yml exec -T db \
  pg_dump -U goid_user goid_db > "$BACKUP_DIR/db_backup_$DATE.sql"

# Media files backup
tar -czf "$BACKUP_DIR/media_backup_$DATE.tar.gz" backend/media/

# Configuration backup
cp -r deployment/ "$BACKUP_DIR/config_backup_$DATE/"
```

### **Recovery Procedures**
```bash
# Restore database
docker-compose -f deployment/docker-compose.production.yml exec -T db \
  psql -U goid_user goid_db < backup_file.sql

# Restore media files
tar -xzf media_backup.tar.gz -C backend/

# Restart services
./deployment/deploy.sh deploy
```

## 📞 **Support and Troubleshooting**

### **Common Issues**

1. **Services won't start**:
   ```bash
   # Check Docker status
   sudo systemctl status docker
   
   # Check logs
   ./deployment/deploy.sh logs
   ```

2. **Database connection issues**:
   ```bash
   # Check database logs
   ./deployment/deploy.sh logs --service db
   
   # Test connection
   docker-compose exec db psql -U goid_user -d goid_db
   ```

3. **Client can't connect to server**:
   - Verify server IP and ports
   - Check firewall settings
   - Test network connectivity

### **Contact Information**
- **System Administrator**: UoG IT Department
- **Technical Support**: GoID Development Team
- **Emergency Contact**: [Your contact information]

## 📈 **Scaling and Performance**

### **Horizontal Scaling**
```bash
# Scale backend service
docker-compose -f deployment/docker-compose.production.yml up -d --scale backend=3

# Load balancer configuration
# Configure nginx for load balancing
```

### **Performance Optimization**
- Database connection pooling
- Redis caching optimization
- Static file serving via CDN
- Image optimization
- Database indexing

This deployment guide ensures a robust, secure, and scalable GoID system for the University of Gondar.
