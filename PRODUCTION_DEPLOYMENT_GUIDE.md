# GoID Production Deployment System

This document provides a complete overview of the GoID production deployment system using Docker Hub and Docker Compose.

## 🚀 Quick Start

### One-Command Deployment

```bash
# Linux/Mac
./deployment/production-deploy.sh --username your-dockerhub-username

# Windows
deployment\production-deploy.bat --username your-dockerhub-username
```

This single command will:
1. Build all services with version tagging
2. Push images to Docker Hub
3. Generate production configuration
4. Deploy all services
5. Verify deployment status

## 📁 System Overview

### Architecture

#### Server Components (Centralized)
- **Backend Service**: Django application with multi-tenant support + FMatcher
- **Frontend Service**: React application with <PERSON>inx (for admin access)
- **Database**: PostgreSQL with persistent storage
- **Cache**: Redis for sessions and caching
- **Reverse Proxy**: Nginx (optional)

#### Client Components (Each Kebele Office)
- **Biometric Service**: Local fingerprint capture service (localhost:8002)
- **Frontend Access**: <PERSON><PERSON><PERSON> connects to server frontend + local biometric service
- **USB Device**: Futronic FS88H fingerprint scanner

### Deployment Flow
```
Development → Build Server Images → Push to Docker Hub → Deploy Server → Install Client Services
     ↓              ↓                      ↓               ↓                    ↓
Local Code → Docker Images → Docker Hub → Production → Kebele Machines
                                          Server      (Biometric Service)
```

#### Two-Part Deployment:
1. **Server Deployment**: Centralized server with backend, frontend, database
2. **Client Installation**: Biometric service on each kebele machine

## 🛠️ Available Tools

### 1. Build and Push Scripts
- `deployment/build.sh` (Linux/Mac)
- `deployment/build.bat` (Windows)
- `deployment/build-and-push.py` (Python core)

**Features:**
- Automatic version management
- Selective service building
- Docker Hub integration
- Version increment control

### 2. Deployment Scripts
- `deployment/deploy.sh` (Linux/Mac)
- `deployment/deploy.bat` (Windows)
- `deployment/deploy.py` (Python core)

**Features:**
- Environment configuration
- Service orchestration
- Status monitoring
- Log management

### 3. Complete Deployment Scripts
- `deployment/production-deploy.sh` (Linux/Mac)
- `deployment/production-deploy.bat` (Windows)

**Features:**
- End-to-end automation
- Error handling
- Status reporting
- Rollback support

### 4. Version Management
- `deployment/version-manager.py`

**Features:**
- Version tracking
- Deployment history
- Version comparison
- Export capabilities

## 📋 Configuration Files

### 1. Version Configuration (`deployment/versions.json`)
```json
{
  "services": {
    "backend": {
      "current_version": "1.0.0",
      "docker_hub_repo": "your-username/goid-backend"
    },
    "frontend": {
      "current_version": "1.0.0", 
      "docker_hub_repo": "your-username/goid-frontend"
    }
  }
}
```

### 2. Environment Configuration (`deployment/.env.production`)
```env
DOCKER_HUB_USERNAME=your-dockerhub-username
DB_PASSWORD=your-secure-password
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=your-domain.com
```

### 3. Docker Compose Template (`deployment/docker-compose.production.template.yml`)
- Production-ready service definitions
- Resource limits and health checks
- Network and volume configurations
- Environment variable substitution

## 🔧 Usage Examples

### Building Services

```bash
# Build all services
./deployment/build.sh --username myuser

# Build specific service with version increment
./deployment/build.sh --service backend --type minor --username myuser

# Build with specific version
./deployment/build.sh --service frontend --version 2.1.0 --username myuser

# List current versions
./deployment/build.sh --list
```

### Deploying Services

```bash
# Deploy all services
./deployment/deploy.sh

# Deploy specific services
./deployment/deploy.sh deploy --services "backend frontend"

# Check status
./deployment/deploy.sh status

# View logs
./deployment/deploy.sh logs --service backend --follow
```

### Version Management

```bash
# List versions
python deployment/version-manager.py list

# Increment version
python deployment/version-manager.py increment --service backend --type minor

# Show deployment history
python deployment/version-manager.py history

# Update Docker Hub repositories
python deployment/version-manager.py update-repos --username myuser
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Change all default passwords
- [ ] Use strong SECRET_KEY (50+ characters)
- [ ] Configure proper ALLOWED_HOSTS
- [ ] Set up SSL/TLS certificates
- [ ] Restrict database access
- [ ] Configure firewall rules
- [ ] Set up backup strategy
- [ ] Enable monitoring and alerting

### Environment Variables
Ensure these are properly configured in `.env.production`:
- `DB_PASSWORD`: Strong database password
- `SECRET_KEY`: Django secret key
- `ALLOWED_HOSTS`: Comma-separated list of allowed domains
- `CORS_ALLOWED_ORIGINS`: Frontend domains

## 📊 Monitoring and Maintenance

### Health Checks
All services include health checks:
- Backend: Django health check endpoint
- Frontend: Nginx status check
- Database: PostgreSQL connection test
- Redis: Service availability check

### Log Management
```bash
# View all logs
./deployment/deploy.sh logs

# Follow specific service logs
./deployment/deploy.sh logs --service backend --follow

# Export logs
docker-compose -f deployment/docker-compose.production.yml logs > system.log
```

### Backup Procedures
```bash
# Database backup
docker-compose -f deployment/docker-compose.production.yml exec db \
  pg_dump -U goid_user goid_db > backup_$(date +%Y%m%d).sql

# Media files backup
tar -czf media_backup_$(date +%Y%m%d).tar.gz backend/media/
```

## 🚨 Troubleshooting

### Common Issues

1. **Docker Hub Authentication**
   ```bash
   docker login
   ```

2. **Service Won't Start**
   ```bash
   ./deployment/deploy.sh logs --service <service-name>
   ```

3. **Database Connection Issues**
   - Check credentials in `.env.production`
   - Verify database service status
   - Check network connectivity

4. **Image Pull Failures**
   - Verify Docker Hub repository names
   - Check image tags and versions
   - Ensure images were pushed successfully

### Emergency Procedures

#### Rollback Deployment
```bash
# Set previous version
python deployment/version-manager.py set --service backend --version 1.0.0

# Regenerate and deploy
./deployment/deploy.sh generate
./deployment/deploy.sh deploy
```

#### Service Recovery
```bash
# Restart specific service
docker-compose -f deployment/docker-compose.production.yml restart backend

# Full system restart
./deployment/deploy.sh stop
./deployment/deploy.sh deploy
```

## 📈 Scaling and Performance

### Horizontal Scaling
```bash
# Scale backend service
docker-compose -f deployment/docker-compose.production.yml up -d --scale backend=3
```

### Resource Monitoring
- CPU and memory usage via Docker stats
- Database performance monitoring
- Application performance metrics
- Log analysis and alerting

## 🔄 CI/CD Integration

The deployment system can be integrated with CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Build and Deploy
  run: |
    ./deployment/build.sh --username ${{ secrets.DOCKER_HUB_USERNAME }}
    ./deployment/deploy.sh deploy
```

## 📞 Support

For deployment issues:
1. Check the deployment logs
2. Review the troubleshooting section
3. Verify configuration files
4. Check Docker Hub repository status
5. Review system resources

## 📝 Version History

Track your deployments using the version manager:
```bash
python deployment/version-manager.py record --services backend frontend --notes "Initial production deployment"
```

This comprehensive deployment system provides everything needed for professional production deployment of the GoID system with proper versioning, monitoring, and maintenance capabilities.
