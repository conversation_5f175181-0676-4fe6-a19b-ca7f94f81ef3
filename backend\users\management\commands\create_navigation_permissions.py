"""
Management command to create navigation-specific permissions for groups.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.db import connection
from django_tenants.utils import schema_context, get_public_schema_name


class Command(BaseCommand):
    help = 'Create navigation permissions for management groups'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-missing',
            action='store_true',
            help='Create missing navigation permissions',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Creating Navigation Permissions'))
        self.stdout.write('=' * 50)

        # Ensure we're in public schema
        with schema_context(get_public_schema_name()):
            if options['create_missing']:
                self.create_missing_permissions()
            
            self.show_navigation_permissions()

    def create_missing_permissions(self):
        """Create missing navigation permissions."""
        self.stdout.write('\n📝 Creating Missing Navigation Permissions...')
        
        # Get User content type
        user_ct = ContentType.objects.get(app_label='users', model='user')
        
        # Define navigation permissions we need
        navigation_permissions = [
            # User Management Navigation
            ('view_user_management', 'Can view user management navigation'),
            ('view_kebele_management', 'Can view kebele management navigation'),
            ('view_subcity_management', 'Can view subcity management navigation'),
            ('view_city_management', 'Can view city management navigation'),
            
            # Navigation Menu Access
            ('access_admin_menu', 'Can access admin navigation menu'),
            ('access_management_menu', 'Can access management navigation menu'),
            ('access_reports_menu', 'Can access reports navigation menu'),
            ('access_settings_menu', 'Can access settings navigation menu'),
            
            # Dashboard Navigation
            ('navigate_to_dashboard', 'Can navigate to dashboard'),
            ('navigate_to_citizens', 'Can navigate to citizens section'),
            ('navigate_to_id_cards', 'Can navigate to ID cards section'),
            ('navigate_to_reports', 'Can navigate to reports section'),
            ('navigate_to_workflows', 'Can navigate to workflows section'),
            
            # Management-Specific Navigation
            ('manage_kebele_navigation', 'Can manage kebele-level navigation'),
            ('manage_subcity_navigation', 'Can manage subcity-level navigation'),
            ('manage_city_navigation', 'Can manage city-level navigation'),
        ]
        
        created_count = 0
        for codename, name in navigation_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=user_ct,
                defaults={'name': name}
            )
            
            if created:
                self.stdout.write(f'  ✅ Created: {codename}')
                created_count += 1
            else:
                self.stdout.write(f'  ♻️ Exists: {codename}')
        
        self.stdout.write(f'\n📊 Created {created_count} new navigation permissions')

    def show_navigation_permissions(self):
        """Show all navigation permissions."""
        self.stdout.write('\n📋 Current Navigation Permissions:')
        self.stdout.write('-' * 40)
        
        # Get all navigation-related permissions
        nav_keywords = ['view', 'access', 'navigate', 'manage', 'dashboard']
        nav_perms = Permission.objects.filter(
            content_type__app_label='users'
        ).filter(
            codename__iregex=r'(' + '|'.join(nav_keywords) + ')'
        ).order_by('codename')
        
        for perm in nav_perms:
            self.stdout.write(f'  - users.{perm.codename}: {perm.name}')
        
        self.stdout.write(f'\n📊 Total navigation permissions: {nav_perms.count()}')


def get_management_permission_sets():
    """
    Define permission sets for different management levels.
    
    Returns:
        dict: Permission sets for different management types
    """
    return {
        'kebele_management': {
            'description': 'Kebele User Management',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                'users.navigate_to_citizens',
                'users.view_citizens_list',
                'users.view_citizen_details',
                
                # User management
                'users.view_user_management',
                'users.view_kebele_management',
                'users.create_kebele_users',
                'users.view_user',
                'users.change_user',
                
                # ID Cards
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.generate_id_cards',
                'users.send_id_cards_for_approval',
                
                # Reports
                'users.view_kebele_reports',
                'users.view_own_kebele_data',
                
                # Navigation menus
                'users.access_management_menu',
                'users.manage_kebele_navigation',
            ]
        },
        
        'subcity_management': {
            'description': 'Subcity User Management',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_subcity_dashboard',
                'users.navigate_to_citizens',
                'users.view_citizens_list',
                'users.view_citizen_details',
                
                # User management
                'users.view_user_management',
                'users.view_subcity_management',
                'users.view_kebele_management',
                'users.create_kebele_users',
                'users.create_subcity_users',
                'users.view_user',
                'users.change_user',
                
                # ID Cards
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.approve_id_cards',
                'users.print_id_cards',
                'users.send_id_cards_to_higher_level',
                
                # Data access
                'users.view_child_kebeles_data',
                'users.view_subcity_reports',
                
                # Workflows
                'users.navigate_to_workflows',
                'users.view_workflows',
                'users.manage_workflows',
                
                # Navigation menus
                'users.access_management_menu',
                'users.access_admin_menu',
                'users.manage_subcity_navigation',
            ]
        },
        
        'city_management': {
            'description': 'City User Management',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_city_dashboard',
                'users.navigate_to_citizens',
                'users.view_citizens_list',
                'users.view_citizen_details',
                
                # User management
                'users.view_user_management',
                'users.view_city_management',
                'users.view_subcity_management',
                'users.create_subcity_users',
                'users.view_user',
                'users.change_user',
                
                # Data access
                'users.view_child_subcities_data',
                'users.view_city_reports',
                'users.view_all_reports',
                
                # Navigation menus
                'users.access_management_menu',
                'users.access_admin_menu',
                'users.access_reports_menu',
                'users.manage_city_navigation',
                
                # Reports
                'users.navigate_to_reports',
            ]
        },
        
        'printer_group': {
            'description': 'ID Card Printing',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                
                # ID Cards only
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.print_id_cards',
            ]
        }
    }


def assign_permissions_to_group(group_name, permission_set_name):
    """
    Assign a permission set to a group.
    
    Args:
        group_name (str): Name of the Django group
        permission_set_name (str): Name of the permission set
        
    Returns:
        tuple: (success, message)
    """
    try:
        with schema_context(get_public_schema_name()):
            # Get the group
            group = Group.objects.get(name=group_name)
            
            # Get permission set
            permission_sets = get_management_permission_sets()
            if permission_set_name not in permission_sets:
                return False, f"Permission set '{permission_set_name}' not found"
            
            permission_set = permission_sets[permission_set_name]
            
            # Get permissions
            permissions_to_add = []
            for perm_string in permission_set['permissions']:
                app_label, codename = perm_string.split('.', 1)
                try:
                    permission = Permission.objects.get(
                        content_type__app_label=app_label,
                        codename=codename
                    )
                    permissions_to_add.append(permission)
                except Permission.DoesNotExist:
                    print(f"Warning: Permission {perm_string} not found")
            
            # Add permissions to group
            group.permissions.add(*permissions_to_add)
            
            return True, f"Added {len(permissions_to_add)} permissions to group '{group_name}'"
            
    except Group.DoesNotExist:
        return False, f"Group '{group_name}' not found"
    except Exception as e:
        return False, f"Error: {str(e)}"
