"""
Workflow Group Manager Service

Manages dynamic group assignments based on workflow type (centralized vs autonomous).
Handles switching between workflow modes and updating user permissions accordingly.
"""

from django.db import transaction
from django.contrib.auth.models import Group
from django_tenants.utils import schema_context, get_public_schema_name
from users.models import User
from users.models_groups import TenantGroup, GroupMembership
from users.utils.group_permissions import create_management_group, get_permission_sets
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
import logging

logger = logging.getLogger(__name__)


class WorkflowGroupManager:
    """
    Manages group assignments based on workflow configuration.
    """
    
    # Workflow-specific group mappings
    WORKFLOW_GROUP_MAPPINGS = {
        'centralized': {
            'clerk': 'centralized_clerk',
            'kebele_leader': 'centralized_kebele_leader',
            'subcity_admin': 'subcity_management',
        },
        'autonomous': {
            'clerk': 'autonomous_clerk', 
            'kebele_leader': 'autonomous_kebele_leader',
            'designated_printer': 'designated_printer',
        }
    }
    
    def __init__(self, tenant):
        self.tenant = tenant
        self.workflow_config = self._get_workflow_config()
    
    def _get_workflow_config(self):
        """Get or create workflow configuration for tenant."""
        try:
            return TenantWorkflowConfig.objects.get(tenant=self.tenant)
        except TenantWorkflowConfig.DoesNotExist:
            # Default to centralized workflow
            return TenantWorkflowConfig.objects.create(
                tenant=self.tenant,
                workflow_type='centralized'
            )
    
    def switch_workflow(self, new_workflow_type, switched_by=None, reason=""):
        """
        Switch tenant workflow and update all user group assignments.
        
        Args:
            new_workflow_type (str): 'centralized' or 'autonomous'
            switched_by (User): User who initiated the switch
            reason (str): Reason for the switch
            
        Returns:
            dict: Results of the workflow switch
        """
        if new_workflow_type not in ['centralized', 'autonomous']:
            raise ValueError("Workflow type must be 'centralized' or 'autonomous'")
        
        old_workflow_type = self.workflow_config.workflow_type
        
        if old_workflow_type == new_workflow_type:
            return {
                'success': False,
                'message': f'Tenant is already using {new_workflow_type} workflow',
                'changes': []
            }
        
        logger.info(f"🔄 Switching {self.tenant.name} from {old_workflow_type} to {new_workflow_type}")
        
        try:
            with transaction.atomic():
                # Update workflow configuration
                self.workflow_config.workflow_type = new_workflow_type
                self.workflow_config.save()
                
                # Create workflow-specific groups if they don't exist
                self._ensure_workflow_groups_exist(new_workflow_type)
                
                # Update user group assignments
                changes = self._update_user_groups(old_workflow_type, new_workflow_type)
                
                # Log the workflow change
                self._log_workflow_change(old_workflow_type, new_workflow_type, switched_by, reason)
                
                return {
                    'success': True,
                    'message': f'Successfully switched to {new_workflow_type} workflow',
                    'old_workflow': old_workflow_type,
                    'new_workflow': new_workflow_type,
                    'changes': changes,
                    'affected_users': len(changes)
                }
                
        except Exception as e:
            logger.error(f"❌ Error switching workflow: {e}")
            raise
    
    def _ensure_workflow_groups_exist(self, workflow_type):
        """Ensure all required groups for the workflow exist."""
        required_groups = self.WORKFLOW_GROUP_MAPPINGS.get(workflow_type, {})
        
        for role, group_template in required_groups.items():
            group_name = f"{self.tenant.name}_{group_template}"
            
            # Check if group exists
            with schema_context(get_public_schema_name()):
                try:
                    existing_group = Group.objects.get(name=group_name)
                    logger.info(f"✅ Group '{group_name}' already exists")
                except Group.DoesNotExist:
                    # Create the group
                    success, result, tenant_group = create_management_group(
                        group_name=group_name,
                        permission_set_name=group_template,
                        tenant=self.tenant,
                        description=f"{group_template.replace('_', ' ').title()} for {self.tenant.name}"
                    )
                    
                    if success:
                        logger.info(f"✅ Created group '{group_name}'")
                    else:
                        logger.error(f"❌ Failed to create group '{group_name}': {result}")
    
    def _update_user_groups(self, old_workflow, new_workflow):
        """Update user group assignments based on new workflow."""
        changes = []
        
        # Get users in this tenant
        with schema_context(self.tenant.schema_name):
            tenant_users = User.objects.filter(tenant=self.tenant, is_active=True)
        
        for user in tenant_users:
            user_changes = self._update_user_workflow_groups(user, old_workflow, new_workflow)
            if user_changes:
                changes.extend(user_changes)
        
        return changes
    
    def _update_user_workflow_groups(self, user, old_workflow, new_workflow):
        """Update a single user's workflow-specific groups."""
        changes = []
        user_role = user.role
        
        # Get old and new group templates
        old_group_template = self.WORKFLOW_GROUP_MAPPINGS.get(old_workflow, {}).get(user_role)
        new_group_template = self.WORKFLOW_GROUP_MAPPINGS.get(new_workflow, {}).get(user_role)
        
        if not new_group_template:
            logger.warning(f"⚠️ No group template for role '{user_role}' in {new_workflow} workflow")
            return changes
        
        with schema_context(get_public_schema_name()):
            # Remove from old workflow group
            if old_group_template:
                old_group_name = f"{self.tenant.name}_{old_group_template}"
                try:
                    old_group = Group.objects.get(name=old_group_name)
                    user.remove_from_group(old_group)
                    changes.append({
                        'user': user.email,
                        'action': 'removed_from_group',
                        'group': old_group_name,
                        'workflow': old_workflow
                    })
                    logger.info(f"➖ Removed {user.email} from {old_group_name}")
                except Group.DoesNotExist:
                    pass
            
            # Add to new workflow group
            new_group_name = f"{self.tenant.name}_{new_group_template}"
            try:
                new_group = Group.objects.get(name=new_group_name)
                user.add_to_group(
                    group=new_group,
                    assigned_by=None,
                    reason=f"Workflow switch to {new_workflow}",
                    is_primary=True
                )
                changes.append({
                    'user': user.email,
                    'action': 'added_to_group',
                    'group': new_group_name,
                    'workflow': new_workflow
                })
                logger.info(f"➕ Added {user.email} to {new_group_name}")
            except Group.DoesNotExist:
                logger.error(f"❌ Group {new_group_name} does not exist")
        
        return changes
    
    def _log_workflow_change(self, old_workflow, new_workflow, switched_by, reason):
        """Log the workflow change for audit purposes."""
        from workflows.models import WorkflowLog, ApprovalAction
        
        with schema_context(self.tenant.schema_name):
            WorkflowLog.objects.create(
                action=ApprovalAction.WORKFLOW_SWITCH,
                performed_by=switched_by,
                tenant=self.tenant,
                details={
                    'old_workflow': old_workflow,
                    'new_workflow': new_workflow,
                    'reason': reason,
                    'timestamp': timezone.now().isoformat()
                },
                comment=f"Workflow switched from {old_workflow} to {new_workflow}: {reason}"
            )
    
    def get_current_workflow_status(self):
        """Get current workflow status and user assignments."""
        workflow_type = self.workflow_config.workflow_type
        
        # Get users by workflow groups
        with schema_context(self.tenant.schema_name):
            users = User.objects.filter(tenant=self.tenant, is_active=True)
        
        user_assignments = []
        for user in users:
            primary_group = user.get_primary_group()
            user_assignments.append({
                'email': user.email,
                'role': user.role,
                'primary_group': primary_group.name if primary_group else None,
                'workflow_aligned': self._is_user_workflow_aligned(user, workflow_type)
            })
        
        return {
            'tenant': self.tenant.name,
            'workflow_type': workflow_type,
            'users': user_assignments,
            'total_users': len(user_assignments),
            'aligned_users': len([u for u in user_assignments if u['workflow_aligned']])
        }
    
    def _is_user_workflow_aligned(self, user, workflow_type):
        """Check if user's group assignment aligns with current workflow."""
        expected_template = self.WORKFLOW_GROUP_MAPPINGS.get(workflow_type, {}).get(user.role)
        if not expected_template:
            return True  # No specific requirement
        
        expected_group_name = f"{self.tenant.name}_{expected_template}"
        primary_group = user.get_primary_group()
        
        return primary_group and primary_group.name == expected_group_name
    
    def assign_designated_printer(self, user_email):
        """Assign designated printer role to a user (autonomous workflow only)."""
        if self.workflow_config.workflow_type != 'autonomous':
            raise ValueError("Designated printer can only be assigned in autonomous workflow")
        
        with schema_context(self.tenant.schema_name):
            try:
                user = User.objects.get(email=user_email, tenant=self.tenant)
            except User.DoesNotExist:
                raise ValueError(f"User {user_email} not found in {self.tenant.name}")
        
        # Add designated_printer to additional_roles
        if 'designated_printer' not in user.additional_roles:
            user.additional_roles.append('designated_printer')
            user.save()
        
        # Add to designated printer group
        printer_group_name = f"{self.tenant.name}_designated_printer"
        with schema_context(get_public_schema_name()):
            try:
                printer_group = Group.objects.get(name=printer_group_name)
                user.add_to_group(
                    group=printer_group,
                    reason="Assigned as designated printer",
                    is_primary=False  # Secondary group
                )
                logger.info(f"✅ Assigned {user_email} as designated printer")
                return True
            except Group.DoesNotExist:
                logger.error(f"❌ Designated printer group {printer_group_name} does not exist")
                return False
