/**
 * Dynamic Role Detection Based on Permissions
 * 
 * Instead of using static user.role, detect roles dynamically from permissions
 */

/**
 * Role definitions based on permission combinations
 */
export const ROLE_DEFINITIONS = {
  superadmin: {
    name: 'Super Admin',
    requiredPermissions: ['full_system_access', 'manage_all_tenants'],
    anyPermission: true // User needs ANY of these permissions
  },
  
  city_admin: {
    name: 'City Admin', 
    requiredPermissions: ['view_child_subcities_data', 'create_subcity_users'],
    anyPermission: true
  },
  
  subcity_admin: {
    name: 'Subcity Admin',
    requiredPermissions: ['view_child_kebeles_data', 'create_kebele_users'],
    anyPermission: true
  },
  
  kebele_leader: {
    name: '<PERSON><PERSON><PERSON> Leader',
    requiredPermissions: ['approve_id_cards', 'verify_documents'],
    anyPermission: true
  },
  
  clerk: {
    name: 'Clerk',
    requiredPermissions: ['register_citizens', 'generate_id_cards'],
    anyPermission: true
  },
  
  printer: {
    name: 'Printer',
    requiredPermissions: ['print_id_cards', 'print_idcards'],
    anyPermission: true
  },
  
  viewer: {
    name: 'Viewer',
    requiredPermissions: ['view_citizens_list', 'view_id_cards_list'],
    anyPermission: true
  }
};

// Dynamic role detection removed - using static roles only

// Dynamic role functions removed - using static roles only

/**
 * Get all roles a user qualifies for (user might have multiple roles)
 * @param {Object} user - User object
 * @returns {Array} - Array of role keys user qualifies for
 */
// getAllUserRoles function removed - using static roles only

// getRoleBadge function removed - using static roles only

/**
 * Navigation generation based on dynamic roles and permissions
 * @param {Object} user - User object
 * @returns {Array} - Navigation items
 */
export const generateDynamicNavigation = (user) => {
  console.log('🚀 DYNAMIC NAVIGATION: Function called!');
  console.log('🔍 Generating navigation for user:', user?.email);
  console.log('🔍 User role:', user?.role);
  console.log('🔍 User permissions:', user?.permissions);

  const navigationItems = [];

  // Special case: Users with designated_printer or print_id_cards role should ONLY see Print Queue
  if (user?.role === 'designated_printer' || user?.role === 'print_id_cards') {
    console.log(`🖨️ SPECIAL CASE: ${user?.role} role - showing only Print Queue`);
    navigationItems.push({
      id: 'print-queue',
      label: 'Print Queue',
      path: '/idcards/printing-queue',
      icon: 'Print'
    });
    return navigationItems; // Return early, don't show other menus
  }

  // Dashboard - always show for other roles
  navigationItems.push({
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'Dashboard'
  });

  // Permission-based navigation (not role-based)
  const permissionNavMap = {
    // System Administration (Superadmin)
    'manage_tenants': {
      id: 'tenants',
      label: 'Tenants',
      path: '/tenants',
      icon: 'Business'
    },
    'manage_all_tenants': {
      id: 'tenants',
      label: 'Tenants',
      path: '/tenants',
      icon: 'Business'
    },
    'full_system_access': {
      id: 'tenants',
      label: 'Tenants',
      path: '/tenants',
      icon: 'Business'
    },
    'manage_system_settings': {
      id: 'system-settings',
      label: 'System Settings',
      path: '/system/settings',
      icon: 'Settings'
    },

    // City Level - Citizen Dictionary
    'view_child_subcities_data': {
      id: 'citizen-dictionary',
      label: 'Citizen Dictionary',
      path: '/citizens/citizen-book',
      icon: 'People'
    },

    // Subcity Level - Citizens (All Kebeles)
    'view_child_kebeles_data': {
      id: 'citizens',
      label: 'Citizens',
      path: '/citizens/all-kebeles',
      icon: 'People'
    },

    // Kebele Level - Citizens (Local)
    'view_citizens_list': {
      id: 'citizens',
      label: 'Citizens',
      path: '/citizens',
      icon: 'People'
    },
    'register_citizens': {
      id: 'citizens',
      label: 'Citizens',
      path: '/citizens',
      icon: 'People'
    },

    // Subcity Level - ID Cards (All Kebeles)
    'print_id_cards': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards/all-kebeles',
      icon: 'Badge'
    },

    // Kebele Level - ID Cards (Local)
    'view_id_cards_list': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },
    'generate_id_cards': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },
    'approve_id_cards': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },
    'verify_documents': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },

    // Print Queue
    'print_idcards': {
      id: 'print-queue',
      label: 'Print Queue',
      path: '/idcards/printing-queue',
      icon: 'Print'
    },
    'print_id_cards': {
      id: 'print-queue',
      label: 'Print Queue',
      path: '/idcards/printing-queue',
      icon: 'Print'
    },

    // User Management - Generic (for roles that manage multiple types)
    'view_user_management': {
      id: 'user-management',
      label: 'User Management',
      path: '/users/kebele-management',
      icon: 'SupervisorAccount',
      // Only show for roles that don't have more specific management permissions
      excludeIfHasPermissions: ['create_kebele_users', 'create_subcity_users']
    },
    'manage_all_users': {
      id: 'system-users',
      label: 'System Users',
      path: '/users',
      icon: 'SupervisorAccount'
    },
    'create_kebele_users': {
      id: 'kebele-management',
      label: 'Kebele Management',
      path: '/users/kebele-management',
      icon: 'SupervisorAccount'
    },
    'create_subcity_users': {
      id: 'subcity-management',
      label: 'Subcity Management',
      path: '/users/city-management',
      icon: 'SupervisorAccount'
    },

    // Reports
    'view_all_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },
    'view_subcity_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },
    'view_kebele_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },

    // Workflow Management
    'manage_workflows': {
      id: 'workflow-management',
      label: 'Workflow Management',
      path: '/workflows',
      icon: 'Timeline'
    },
    'view_workflows': {
      id: 'workflow-management',
      label: 'Workflow Management',
      path: '/workflows',
      icon: 'Timeline'
    },
    'view_city_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },

    // Transfer and Clearance workflows
    'create_transfers': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'approve_transfer_requests': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'view_transfers': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'manage_transfers': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'create_clearances': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },
    'view_clearances': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },
    'manage_clearances': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },
    'approve_clearance_requests': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },

    // Fallback for kebele leaders only - if they have approve_id_cards AND are kebele level, they should have Transfer/Clearance
    // This will be handled in the navigation generation logic to check tenant type
  };

  // Add navigation items based on permissions
  if (user.permissions) {
    console.log('🔍 Processing permissions for navigation...');
    console.log('🔍 User role:', user.role);
    console.log('🔍 User permissions:', user.permissions);

    // Special case: Superadmin should NOT have Citizens/ID Cards menus (system admin only)
    const isSuperAdmin = user.role === 'superadmin' || user.is_superuser;
    console.log('🔍 Is superadmin:', isSuperAdmin);

    // Special case: Subcity System Admin should NOT have Citizens/Workflow Management menus
    const isSubcitySystemAdmin = user.role === 'subcity_system_admin';
    console.log('🔍 Is subcity system admin:', isSubcitySystemAdmin);

    // Special case: City System Admin should NOT have Citizens menus (only Subcity Management)
    const isCitySystemAdmin = user.role === 'city_system_admin';
    console.log('🔍 Is city system admin:', isCitySystemAdmin);

    if (isCitySystemAdmin) {
      console.log('🔍 City system admin permissions check:');
      console.log('  - Has create_subcity_users?', user.permissions?.includes('create_subcity_users'));
      console.log('  - All permissions:', user.permissions);
    }

    // Declare userTenantType outside the if block so it's available later
    const userTenantType = user.tenant?.type || user.tenant_type;

    if (!isSuperAdmin) {
      // Special handling for ID Cards - choose the right path based on user tenant type
      const hasIDCardPermissions = user.permissions.some(p =>
        ['print_id_cards', 'approve_id_cards', 'view_id_cards_list', 'generate_id_cards'].includes(p)
      );

      // Also check if user is a clerk (clerks should have ID cards access even without specific permissions)
      const isClerk = user.role === 'clerk';

      console.log('🔍 ID CARDS NAVIGATION DEBUG:');
      console.log('🔍 User permissions:', user.permissions);
      console.log('🔍 Has ID card permissions:', hasIDCardPermissions);
      console.log('🔍 Is clerk:', isClerk);
      console.log('🔍 User tenant type:', userTenantType);
      console.log('🔍 User role:', user.role);
      console.log('🔍 Should show ID cards?', hasIDCardPermissions || isClerk);

      if (hasIDCardPermissions || isClerk) {
        console.log('✅ User has ID card permissions or is clerk, determining correct path...');

        if (userTenantType === 'subcity' && user.permissions.includes('print_id_cards')) {
          // Subcity admins should use all-kebeles path
          navigationItems.push({
            id: 'idcards',
            label: 'ID Cards',
            path: '/idcards/all-kebeles',
            icon: 'Badge'
          });
          console.log('✅ Added subcity ID cards navigation: /idcards/all-kebeles');
        } else if (userTenantType === 'kebele' || user.role === 'kebele_leader' || user.role === 'clerk') {
          // Kebele users (including clerks) should use local path
          navigationItems.push({
            id: 'idcards',
            label: 'ID Cards',
            path: '/idcards',
            icon: 'Badge'
          });
          console.log('✅ Added kebele ID cards navigation: /idcards');
        }
      } else {
        console.log('❌ User has no ID card permissions and is not a clerk');
      }
    } else {
      console.log('🚫 Superadmin: Skipping Citizens/ID Cards menus (system admin only)');
    }

    // Process other permissions (excluding ID card ones to avoid conflicts)
    for (const permission of user.permissions) {
      console.log(`🔍 Checking permission: ${permission}`);

      // Special debug for city_system_admin
      if (isCitySystemAdmin && permission === 'create_subcity_users') {
        console.log(`🎯 FOUND create_subcity_users permission for city_system_admin!`);
      }

      // Skip ID card permissions as they're handled above
      if (['print_id_cards', 'approve_id_cards', 'view_id_cards_list', 'generate_id_cards'].includes(permission)) {
        console.log(`ℹ️ Skipping ID card permission: ${permission} (handled separately)`);
        continue;
      }

      // Skip operational permissions for superadmin (they should only see system admin menus)
      if (isSuperAdmin && [
        'register_citizens', 'view_citizens_list', 'view_citizen_details',
        'verify_documents', 'approve_id_cards', 'generate_id_cards', 'view_id_cards_list',
        'view_all_reports', 'view_city_reports', 'view_subcity_reports', 'view_kebele_reports'
      ].includes(permission)) {
        console.log(`🚫 Skipping operational permission for superadmin: ${permission}`);
        continue;
      }

      // Skip specific navigation permissions for subcity_system_admin (hide Citizens and Workflow Management menus)
      if (isSubcitySystemAdmin && [
        'view_child_kebeles_data', 'view_citizens_list', 'view_citizen_details', 'register_citizens',
        'manage_workflows', 'view_workflows', 'view_reports', 'view_subcity_reports'
      ].includes(permission)) {
        console.log(`🚫 Skipping navigation permission for subcity_system_admin: ${permission}`);
        continue;
      }

      // Skip specific navigation permissions for city_system_admin (hide Citizens menus, but allow Subcity Management)
      if (isCitySystemAdmin && [
        'view_child_subcities_data', 'view_citizens_list', 'view_citizen_details', 'register_citizens',
        'view_child_kebeles_data', 'manage_workflows', 'view_workflows', 'view_reports', 'view_city_reports'
      ].includes(permission)) {
        console.log(`🚫 Skipping navigation permission for city_system_admin: ${permission}`);
        continue;
      }

      // Skip Tenants navigation for city admins (they should not see tenant management)
      const isCityAdmin = user.role === 'city_admin';
      if (isCityAdmin && [
        'manage_tenants', 'manage_all_tenants', 'full_system_access'
      ].includes(permission)) {
        console.log(`🚫 Skipping tenant management permission for city_admin: ${permission}`);
        continue;
      }

      const navMapping = permissionNavMap[permission];
      if (navMapping) {
        console.log(`✅ Found navigation mapping for ${permission}:`, navMapping);
        // Check if this navigation item should be excluded based on other permissions
        if (navMapping.excludeIfHasPermissions) {
          const hasExcludingPermissions = navMapping.excludeIfHasPermissions.some(excludePerm =>
            user.permissions.includes(excludePerm)
          );
          if (hasExcludingPermissions) {
            console.log(`🚫 Excluding navigation item ${navMapping.label} due to more specific permissions`);
            continue;
          }
        }

        // Handle normal single nav item mapping
        console.log(`✅ Found navigation item for ${permission}:`, navMapping);
        // Avoid duplicates
        if (!navigationItems.find(item => item.id === navMapping.id)) {
          navigationItems.push(navMapping);
          console.log(`✅ Added navigation item: ${navMapping.label}`);
        } else {
          console.log(`ℹ️ Skipped duplicate navigation item: ${navMapping.label}`);
        }
      } else {
        console.log(`❌ No navigation item found for permission: ${permission}`);
      }
    }

    // Special case: Add Transfer and Clearance for kebele leaders only
    // userTenantType already declared above
    const isKebeleUser = userTenantType === 'kebele' || user.role === 'kebele_leader';
    const hasApprovalPermission = user.permissions.includes('approve_id_cards');

    if (hasApprovalPermission && isKebeleUser) {
      // Add Transfer if not already present
      if (!navigationItems.find(item => item.id === 'transfers')) {
        navigationItems.push({
          id: 'transfers',
          label: 'Transfer',
          path: '/transfers',
          icon: 'Timeline'
        });
      }

      // Add Clearance if not already present
      if (!navigationItems.find(item => item.id === 'clearances')) {
        navigationItems.push({
          id: 'clearances',
          label: 'Clearance',
          path: '/clearances',
          icon: 'Assignment'
        });
      }
    }
  } else {
    console.log('❌ No permissions found for user');
  }

  console.log(`✅ Generated ${navigationItems.length} navigation items for ${user?.role || 'unknown'}`);
  console.log('🔍 Final navigation items:', navigationItems);
  return navigationItems;
};

export default {
  generateDynamicNavigation,
  ROLE_DEFINITIONS
};
