#!/usr/bin/env python3
"""
GoID Production Build and Push Script
Builds Docker images with proper versioning and pushes to Docker Hub
"""

import json
import os
import sys
import subprocess
import argparse
from datetime import datetime
from pathlib import Path

class DockerBuilder:
    def __init__(self, config_path="deployment/versions.json"):
        self.config_path = config_path
        self.config = self.load_config()
        self.root_dir = Path(__file__).parent.parent
        
    def load_config(self):
        """Load version configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Configuration file {self.config_path} not found")
            sys.exit(1)
    
    def save_config(self):
        """Save updated configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def increment_version(self, service, version_type="patch"):
        """Increment version number (major.minor.patch)"""
        current = self.config["services"][service]["current_version"]
        major, minor, patch = map(int, current.split('.'))
        
        if version_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif version_type == "minor":
            minor += 1
            patch = 0
        else:  # patch
            patch += 1
            
        new_version = f"{major}.{minor}.{patch}"
        self.config["services"][service]["current_version"] = new_version
        return new_version
    
    def run_command(self, command, cwd=None):
        """Run shell command and return result"""
        print(f"🔧 Running: {command}")
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                check=True, 
                capture_output=True, 
                text=True,
                cwd=cwd or self.root_dir
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"❌ Command failed: {e}")
            print(f"❌ Error output: {e.stderr}")
            sys.exit(1)
    
    def build_service(self, service, version=None, push=True, version_type="patch"):
        """Build and optionally push a service"""
        if service not in self.config["services"]:
            print(f"❌ Unknown service: {service}")
            return False

        service_config = self.config["services"][service]

        # Increment version if not specified
        if version is None:
            version = self.increment_version(service, version_type)
        else:
            self.config["services"][service]["current_version"] = version

        repo = service_config["docker_hub_repo"]

        # For the single repository approach, use service-specific tags
        if "goid-production" in repo and not repo.endswith(f"-{service}"):
            # Use the base repo with service tag
            base_repo = repo
            image_tag = f"{service}-{version}"
            latest_tag = f"{service}-latest"
        else:
            # Use separate repositories
            base_repo = repo
            image_tag = version
            latest_tag = "latest"
        
        # Determine build context and dockerfile
        if service == "backend":
            context = "backend"
            dockerfile = "Dockerfile.production"
        elif service == "frontend":
            context = "frontend"
            dockerfile = "Dockerfile.production"

        else:
            print(f"❌ Unknown service build configuration: {service}")
            return False
            
        # Build image
        print(f"🏗️  Building {service} v{version}...")

        # Add build arguments for frontend
        build_args = ""
        if service == "frontend":
            # Get server configuration from deployment config
            server_ip = self.config.get("deployment", {}).get("server_ip", "************")
            build_args = f"--build-arg VITE_API_URL=http://{server_ip}:8000 --build-arg VITE_BIOMETRIC_SERVICE_URL=http://localhost:8002"

        build_cmd = f"docker build {build_args} -t {base_repo}:{image_tag} -t {base_repo}:{latest_tag} -f {context}/{dockerfile} {context}"
        self.run_command(build_cmd)

        # Push to Docker Hub if requested
        if push:
            print(f"📤 Pushing {service} v{version} to Docker Hub...")
            self.run_command(f"docker push {base_repo}:{image_tag}")
            self.run_command(f"docker push {base_repo}:{latest_tag}")
            
        # Update last build timestamp
        self.config["services"][service]["last_build"] = datetime.now().isoformat()
        self.save_config()
        
        print(f"✅ Successfully built and pushed {service} v{version}")
        return True
    
    def build_all(self, push=True, version_type="patch"):
        """Build all services"""
        services = ["backend", "frontend"]  # biometric-service runs on client machines
        
        print("🚀 Building all GoID services...")
        for service in services:
            if not self.build_service(service, push=push, version_type=version_type):
                return False
                
        print("✅ All services built successfully!")
        return True
    
    def list_versions(self):
        """List current versions of all services"""
        print("📋 Current Service Versions:")
        print("-" * 50)
        for service, config in self.config["services"].items():
            version = config["current_version"]
            last_build = config.get("last_build", "Never")
            if last_build != "Never" and last_build is not None:
                try:
                    last_build = datetime.fromisoformat(last_build).strftime("%Y-%m-%d %H:%M")
                except (ValueError, TypeError):
                    last_build = "Invalid date"
            print(f"{service:20} v{version:10} (Last build: {last_build})")

def main():
    parser = argparse.ArgumentParser(description="Build and push GoID Docker images")
    parser.add_argument("--service", choices=["backend", "frontend", "all"],
                       default="all", help="Service to build")
    parser.add_argument("--version", help="Specific version to use (otherwise auto-increment)")
    parser.add_argument("--version-type", choices=["major", "minor", "patch"], 
                       default="patch", help="Version increment type")
    parser.add_argument("--no-push", action="store_true", help="Build only, don't push to Docker Hub")
    parser.add_argument("--list", action="store_true", help="List current versions")
    
    args = parser.parse_args()
    
    builder = DockerBuilder()
    
    if args.list:
        builder.list_versions()
        return
    
    push = not args.no_push
    
    if args.service == "all":
        builder.build_all(push=push, version_type=args.version_type)
    else:
        builder.build_service(args.service, version=args.version, push=push, version_type=args.version_type)

if __name__ == "__main__":
    main()
