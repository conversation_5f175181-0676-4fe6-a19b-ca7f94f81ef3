#!/usr/bin/env python3
"""
GoID Biometric Service Installer
Installs the biometric service as a Windows desktop application with auto-start
"""

import os
import sys
import json
import shutil
import winshell
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import subprocess

class BiometricServiceInstaller:
    def __init__(self):
        self.install_dir = Path("C:/Program Files/GoID Biometric Service")
        self.startup_dir = Path(winshell.startup())
        self.desktop_dir = Path(winshell.desktop())
        
    def check_requirements(self):
        """Check if all requirements are met"""
        issues = []
        
        # Check Python
        try:
            result = subprocess.run([sys.executable, '--version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                issues.append("Python is not properly installed")
        except:
            issues.append("Python is not available")
        
        # Check Java
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                issues.append("Java Runtime Environment is not installed")
        except:
            issues.append("Java Runtime Environment is not available")
        
        # Check required files
        required_files = [
            'biometric_tray_service.py',
            'enhanced_capture_service.py',
            'config.json',
            'requirements.txt'
        ]
        
        for file in required_files:
            if not Path(file).exists():
                issues.append(f"Required file missing: {file}")
        
        return issues
    
    def install_dependencies(self, progress_callback=None):
        """Install Python dependencies"""
        try:
            if progress_callback:
                progress_callback("Installing Python dependencies...")
            
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                raise Exception(f"Dependency installation failed: {result.stderr}")
            
            return True
        except Exception as e:
            raise Exception(f"Failed to install dependencies: {str(e)}")
    
    def create_install_directory(self):
        """Create installation directory"""
        try:
            self.install_dir.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            raise Exception(f"Failed to create install directory: {str(e)}")
    
    def copy_files(self, progress_callback=None):
        """Copy service files to installation directory"""
        files_to_copy = [
            'biometric_tray_service.py',
            'enhanced_capture_service.py',
            'config.json',
            'requirements.txt',
            'README.md'
        ]
        
        # Copy fingerPrint directory if it exists
        if Path('fingerPrint').exists():
            if progress_callback:
                progress_callback("Copying fingerprint SDK files...")
            shutil.copytree('fingerPrint', self.install_dir / 'fingerPrint', 
                          dirs_exist_ok=True)
        
        # Copy individual files
        for file in files_to_copy:
            if Path(file).exists():
                if progress_callback:
                    progress_callback(f"Copying {file}...")
                shutil.copy2(file, self.install_dir / file)
    
    def create_shortcuts(self):
        """Create desktop and startup shortcuts"""
        try:
            # Desktop shortcut
            desktop_shortcut = self.desktop_dir / "GoID Biometric Service.lnk"
            winshell.CreateShortcut(
                Path=str(desktop_shortcut),
                Target=sys.executable,
                Arguments=f'"{self.install_dir / "biometric_tray_service.py"}"',
                StartIn=str(self.install_dir),
                Icon=(str(self.install_dir / "biometric_tray_service.py"), 0),
                Description="GoID Biometric Service"
            )
            
            # Startup shortcut (auto-start with Windows)
            startup_shortcut = self.startup_dir / "GoID Biometric Service.lnk"
            winshell.CreateShortcut(
                Path=str(startup_shortcut),
                Target=sys.executable,
                Arguments=f'"{self.install_dir / "biometric_tray_service.py"}"',
                StartIn=str(self.install_dir),
                Icon=(str(self.install_dir / "biometric_tray_service.py"), 0),
                Description="GoID Biometric Service"
            )
            
            return True
        except Exception as e:
            raise Exception(f"Failed to create shortcuts: {str(e)}")
    
    def configure_service(self, config_data):
        """Configure the service with user settings"""
        try:
            config_file = self.install_dir / "config.json"
            
            # Load existing config
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Update with user settings
            config['server']['url'] = config_data['server_url']
            config['client_info']['kebele_name'] = config_data['kebele_name']
            config['client_info']['subcity_name'] = config_data['subcity_name']
            config['client_info']['city_name'] = config_data['city_name']
            config['client_info']['client_id'] = config_data['client_id']
            
            # Save updated config
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            return True
        except Exception as e:
            raise Exception(f"Failed to configure service: {str(e)}")
    
    def create_uninstaller(self):
        """Create uninstaller script"""
        uninstaller_content = f'''
import os
import shutil
import winshell
from pathlib import Path

def uninstall():
    try:
        # Remove installation directory
        install_dir = Path("{self.install_dir}")
        if install_dir.exists():
            shutil.rmtree(install_dir)
        
        # Remove shortcuts
        desktop_shortcut = Path("{self.desktop_dir}") / "GoID Biometric Service.lnk"
        if desktop_shortcut.exists():
            desktop_shortcut.unlink()
        
        startup_shortcut = Path("{self.startup_dir}") / "GoID Biometric Service.lnk"
        if startup_shortcut.exists():
            startup_shortcut.unlink()
        
        print("GoID Biometric Service uninstalled successfully")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"Uninstall error: {{e}}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    uninstall()
'''
        
        uninstaller_file = self.install_dir / "uninstall.py"
        with open(uninstaller_file, 'w') as f:
            f.write(uninstaller_content)

class InstallerGUI:
    def __init__(self):
        self.installer = BiometricServiceInstaller()
        self.root = tk.Tk()
        self.root.title("GoID Biometric Service Installer")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup installer UI"""
        # Title
        title_label = tk.Label(self.root, text="GoID Biometric Service Installer", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_text = """
This installer will set up the GoID Biometric Service on your computer.
The service will automatically start with Windows and run in the system tray.
        """
        desc_label = tk.Label(self.root, text=desc_text.strip(), justify=tk.LEFT)
        desc_label.pack(pady=10, padx=20)
        
        # Configuration frame
        config_frame = tk.LabelFrame(self.root, text="Configuration", padx=10, pady=10)
        config_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Server URL
        tk.Label(config_frame, text="Server URL:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.server_url_var = tk.StringVar(value="http://your-server:8000")
        tk.Entry(config_frame, textvariable=self.server_url_var, width=50).grid(row=0, column=1, pady=2)
        
        # Kebele Name
        tk.Label(config_frame, text="Kebele Name:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.kebele_name_var = tk.StringVar(value="Kebele 01")
        tk.Entry(config_frame, textvariable=self.kebele_name_var, width=50).grid(row=1, column=1, pady=2)
        
        # Subcity Name
        tk.Label(config_frame, text="Subcity Name:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.subcity_name_var = tk.StringVar(value="Subcity Name")
        tk.Entry(config_frame, textvariable=self.subcity_name_var, width=50).grid(row=2, column=1, pady=2)
        
        # City Name
        tk.Label(config_frame, text="City Name:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.city_name_var = tk.StringVar(value="City Name")
        tk.Entry(config_frame, textvariable=self.city_name_var, width=50).grid(row=3, column=1, pady=2)
        
        # Client ID
        tk.Label(config_frame, text="Client ID:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.client_id_var = tk.StringVar(value="kebele_001")
        tk.Entry(config_frame, textvariable=self.client_id_var, width=50).grid(row=4, column=1, pady=2)
        
        # Progress frame
        progress_frame = tk.Frame(self.root)
        progress_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.progress_var = tk.StringVar(value="Ready to install")
        self.progress_label = tk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # Buttons
        button_frame = tk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.install_button = tk.Button(button_frame, text="Install", 
                                       command=self.start_installation, bg="green", fg="white")
        self.install_button.pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="Cancel", command=self.root.quit).pack(side=tk.RIGHT, padx=5)
    
    def update_progress(self, message):
        """Update progress message"""
        self.progress_var.set(message)
        self.root.update()
    
    def start_installation(self):
        """Start the installation process"""
        try:
            self.install_button.config(state=tk.DISABLED)
            self.progress_bar.start()
            
            # Check requirements
            self.update_progress("Checking requirements...")
            issues = self.installer.check_requirements()
            if issues:
                messagebox.showerror("Requirements Check Failed", 
                                   "The following issues were found:\n\n" + "\n".join(issues))
                return
            
            # Create install directory
            self.update_progress("Creating installation directory...")
            self.installer.create_install_directory()
            
            # Install dependencies
            self.update_progress("Installing Python dependencies...")
            self.installer.install_dependencies(self.update_progress)
            
            # Copy files
            self.update_progress("Copying service files...")
            self.installer.copy_files(self.update_progress)
            
            # Configure service
            self.update_progress("Configuring service...")
            config_data = {
                'server_url': self.server_url_var.get(),
                'kebele_name': self.kebele_name_var.get(),
                'subcity_name': self.subcity_name_var.get(),
                'city_name': self.city_name_var.get(),
                'client_id': self.client_id_var.get()
            }
            self.installer.configure_service(config_data)
            
            # Create shortcuts
            self.update_progress("Creating shortcuts...")
            self.installer.create_shortcuts()
            
            # Create uninstaller
            self.update_progress("Creating uninstaller...")
            self.installer.create_uninstaller()
            
            self.progress_bar.stop()
            self.update_progress("Installation completed successfully!")
            
            messagebox.showinfo("Installation Complete", 
                              "GoID Biometric Service has been installed successfully!\n\n"
                              "The service will start automatically with Windows.\n"
                              "You can also start it manually from the desktop shortcut.")
            
            self.root.quit()
            
        except Exception as e:
            self.progress_bar.stop()
            self.update_progress("Installation failed!")
            messagebox.showerror("Installation Failed", f"Installation failed: {str(e)}")
        finally:
            self.install_button.config(state=tk.NORMAL)
    
    def run(self):
        """Run the installer GUI"""
        self.root.mainloop()

def main():
    """Main entry point"""
    try:
        installer_gui = InstallerGUI()
        installer_gui.run()
    except Exception as e:
        messagebox.showerror("Installer Error", f"Installer error: {str(e)}")

if __name__ == '__main__':
    main()
