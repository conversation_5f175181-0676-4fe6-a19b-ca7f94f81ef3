{"server": {"url": "http://************:8000", "api_base": "/api/biometric", "timeout": 30, "verify_ssl": false}, "service": {"host": "0.0.0.0", "port": 8002, "debug": false, "log_level": "INFO", "log_file": "biometric_service.log", "max_log_size": "10MB", "log_backup_count": 5}, "device": {"model": "Futronic FS88H", "timeout": 30, "capture_timeout": 15, "max_retries": 3, "quality_threshold": 70, "jar_path": "fingerPrint/GonderFingerPrint.jar", "working_directory": "fingerPrint"}, "security": {"allowed_origins": ["http://localhost:3000", "http://************:3000", "https://goid.uog.edu.et"], "api_key": null, "require_auth": false, "rate_limit": {"enabled": true, "requests_per_minute": 60}}, "features": {"health_check": true, "device_status": true, "capture_fingerprint": true, "template_validation": true, "image_capture": true}, "client_info": {"kebele_name": "Kebele Name", "subcity_name": "Subcity Name", "city_name": "City Name", "client_id": "kebele_001", "location": "Office Location"}}