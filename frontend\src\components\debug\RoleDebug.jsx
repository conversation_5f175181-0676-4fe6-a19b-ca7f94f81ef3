import React from 'react';
import { <PERSON>, <PERSON>, CardContent, Typography, Chip, Divider } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';

const RoleDebug = () => {
  const { user } = useAuth();
  const { userPermissions, userGroups } = usePermissions();

  if (!user) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" color="error">No user logged in</Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        🔍 Role & Permission Debug
      </Typography>

      {/* User Basic Info */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>👤 User Information</Typography>
          <Typography><strong>Email:</strong> {user.email}</Typography>
          <Typography><strong>Username:</strong> {user.username}</Typography>
          <Typography><strong>Role:</strong> {user.role}</Typography>
          <Typography><strong>Tenant:</strong> {user.tenant_name} ({user.tenant_type})</Typography>
          <Typography><strong>Tenant ID:</strong> {user.tenant?.id}</Typography>
        </CardContent>
      </Card>

      {/* Groups */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            👥 Groups ({userGroups.length})
          </Typography>
          {userGroups.length > 0 ? (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {userGroups.map((group, index) => (
                <Chip 
                  key={index} 
                  label={group.name || group} 
                  color="primary" 
                  variant="outlined"
                />
              ))}
            </Box>
          ) : (
            <Typography color="error">No groups assigned</Typography>
          )}
        </CardContent>
      </Card>

      {/* Permissions */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            🔑 Permissions ({userPermissions.length})
          </Typography>
          {userPermissions.length > 0 ? (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {userPermissions.map((permission, index) => (
                <Chip 
                  key={index} 
                  label={permission} 
                  color="secondary" 
                  size="small"
                />
              ))}
            </Box>
          ) : (
            <Typography color="error">No permissions found</Typography>
          )}
        </CardContent>
      </Card>

      {/* Raw User Object */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>🔧 Raw User Object</Typography>
          <Box sx={{ 
            backgroundColor: '#f5f5f5', 
            p: 2, 
            borderRadius: 1, 
            overflow: 'auto',
            maxHeight: 300
          }}>
            <pre style={{ margin: 0, fontSize: '12px' }}>
              {JSON.stringify(user, null, 2)}
            </pre>
          </Box>
        </CardContent>
      </Card>

      {/* Expected vs Actual */}
      <Card sx={{ mt: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>📊 Expected vs Actual</Typography>
          
          <Typography variant="subtitle1" gutterBottom>
            <strong>Expected for {user.role}:</strong>
          </Typography>
          
          {user.role === 'clerk' && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2">Groups: clerk</Typography>
              <Typography variant="body2">
                Permissions: register_citizens, view_citizens_list, view_citizen_details, 
                generate_id_cards, view_id_cards_list, view_kebele_dashboard
              </Typography>
            </Box>
          )}
          
          {user.role === 'kebele_leader' && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2">Groups: kebele_leader</Typography>
              <Typography variant="body2">
                Permissions: view_citizens_list, view_citizen_details, view_id_cards_list, 
                approve_id_cards, verify_documents, view_user_management, view_kebele_reports, 
                create_transfers, approve_transfer_requests, create_clearances, view_clearances
              </Typography>
            </Box>
          )}
          
          <Divider sx={{ my: 2 }} />
          
          <Typography variant="subtitle1" gutterBottom>
            <strong>Actual:</strong>
          </Typography>
          <Typography variant="body2">
            Groups: {userGroups.length > 0 ? userGroups.map(g => g.name || g).join(', ') : 'None'}
          </Typography>
          <Typography variant="body2">
            Permissions: {userPermissions.length > 0 ? userPermissions.join(', ') : 'None'}
          </Typography>
          
          {/* Status */}
          <Box sx={{ mt: 2 }}>
            {userGroups.length === 0 && (
              <Chip label="❌ No Groups Assigned" color="error" />
            )}
            {userPermissions.length === 0 && (
              <Chip label="❌ No Permissions Found" color="error" sx={{ ml: 1 }} />
            )}
            {userGroups.length > 0 && userPermissions.length > 0 && (
              <Chip label="✅ Groups and Permissions Found" color="success" />
            )}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default RoleDebug;
