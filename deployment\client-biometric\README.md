# GoID Client Biometric Service Deployment

This package contains everything needed to deploy the biometric capture service on client machines (kebele offices) where the Futronic FS88H fingerprint devices are connected.

## Architecture Overview

```
┌─────────────────────────────────────┐    HTTP API    ┌──────────────────┐
│           Client PC (Kebele)        │ ◄─────────────► │   Server         │
│                                     │                 │                  │
│ ┌─────────────┐ ┌─────────────────┐ │                 │ ┌──────────────┐ │
│ │ USB Device  │ │   Frontend      │ │                 │ │   Backend    │ │
│ │ FS88H       │ │   (<PERSON><PERSON><PERSON>)     │ │                 │ │   Django     │ │
│ └─────────────┘ │                 │ │                 │ │              │ │
│        │        │ localhost:3000  │ │                 │ │ ┌──────────┐ │ │
│ ┌─────────────┐ │                 │ │                 │ │ │FMatcher  │ │ │
│ │ Biometric   │ │                 │ │                 │ │ │Duplicate │ │ │
│ │ Service     │ │                 │ │                 │ │ │Detection │ │ │
│ │localhost:8002│ │                 │ │                 │ │ └──────────┘ │ │
│ └─────────────┘ └─────────────────┘ │                 │ └──────────────┘ │
│        ▲                 │          │                 │                  │
│        └─────────────────┘          │                 │                  │
│     Local communication              │                 │                  │
└─────────────────────────────────────┘                 └──────────────────┘
```

## What This Service Does

### Client Side (This Package - Runs on Kebele PC)
- **Fingerprint Capture**: Communicates directly with USB Futronic FS88H device
- **Template Generation**: Creates ANSI/ISO fingerprint templates
- **Image Capture**: Captures fingerprint images for storage
- **Local HTTP API**: Provides localhost:8002 endpoints for frontend
- **Device Management**: Handles device initialization and status
- **System Tray**: Runs as Windows desktop application with tray icon

### Server Side (Production Server - Centralized)
- **Template Processing**: Receives templates from client frontend
- **Duplicate Detection**: Uses FMatcher for fingerprint matching across all kebeles
- **Database Storage**: Stores citizen biometric data centrally
- **Business Logic**: Handles registration workflows and approvals
- **Multi-tenant**: Manages multiple cities/subcities/kebeles

## Installation Requirements

### Hardware
- Futronic FS88H fingerprint scanner
- USB port for device connection
- Windows PC (recommended) or Linux with libusb

### Software
- Python 3.10 or higher
- Java Runtime Environment (JRE) 8 or higher
- Futronic device drivers (if on Windows)

## Quick Installation

### 1. Download and Extract
```bash
# Extract the client package to desired location
# Example: C:\GoID-Client-Biometric\
```

### 2. Install Python Dependencies
```bash
cd GoID-Client-Biometric
pip install -r requirements.txt
```

### 3. Connect Device
- Connect Futronic FS88H device via USB
- Install drivers if prompted (Windows)
- Verify device is detected in Device Manager

### 4. Configure Service
Edit `config.json` with your settings:
```json
{
  "server_url": "http://your-server:8000",
  "service_port": 8002,
  "device_timeout": 30,
  "capture_timeout": 15,
  "log_level": "INFO"
}
```

### 5. Start Service
```bash
# Windows
start_biometric_service.bat

# Linux/Mac
./start_biometric_service.sh

# Manual
python minimal_capture_service.py
```

## Service Endpoints

The client biometric service provides these endpoints:

### Health Check
```
GET /api/health
Response: {"status": "healthy", "device_connected": true}
```

### Device Status
```
GET /api/device/status
Response: {"connected": true, "model": "FS88H", "ready": true}
```

### Capture Fingerprint
```
POST /api/capture/fingerprint
Body: {"thumb_type": "left|right", "timeout": 15}
Response: {
  "success": true,
  "data": {
    "template_data": "base64_encoded_template",
    "quality_score": 85,
    "capture_time": "2024-01-01T12:00:00"
  }
}
```

## Configuration Options

### config.json
```json
{
  "server_url": "http://your-production-server:8000",
  "service_port": 8002,
  "device_timeout": 30,
  "capture_timeout": 15,
  "max_retries": 3,
  "log_level": "INFO",
  "log_file": "biometric_service.log",
  "allowed_origins": ["http://your-server:3000"],
  "security": {
    "api_key": "optional_api_key",
    "require_auth": false
  }
}
```

## Troubleshooting

### Device Not Detected
1. Check USB connection
2. Verify drivers are installed
3. Try different USB port
4. Check Device Manager (Windows)

### Service Won't Start
1. Check Python installation: `python --version`
2. Check Java installation: `java -version`
3. Verify port 8002 is available
4. Check firewall settings

### Capture Fails
1. Clean fingerprint scanner surface
2. Ensure finger is properly placed
3. Check device status endpoint
4. Review service logs

### Network Issues
1. Verify server URL in config.json
2. Check firewall allows port 8002
3. Test network connectivity to server
4. Verify CORS settings

## Security Considerations

### Network Security
- Use HTTPS in production
- Configure firewall to allow only necessary traffic
- Consider VPN for remote locations

### API Security
- Enable API key authentication if needed
- Restrict allowed origins
- Monitor access logs

### Device Security
- Secure physical access to device
- Regular driver updates
- Monitor for unauthorized access

## Maintenance

### Regular Tasks
- Check service logs weekly
- Clean fingerprint scanner monthly
- Update drivers as needed
- Monitor disk space for logs

### Log Management
```bash
# View recent logs
tail -f biometric_service.log

# Rotate logs (if needed)
python rotate_logs.py
```

### Updates
1. Stop service
2. Backup configuration
3. Replace service files
4. Restore configuration
5. Restart service

## Support

### Common Issues
- Device driver problems
- Network connectivity issues
- Java runtime errors
- USB device conflicts

### Log Files
- `biometric_service.log` - Service operations
- `device_errors.log` - Device-specific errors
- `network.log` - Server communication logs

### Contact Information
For technical support, provide:
- Service version
- Operating system
- Device model
- Error logs
- Network configuration
