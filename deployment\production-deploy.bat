@echo off
REM GoID Complete Production Deployment Script for Windows
REM This script handles the entire deployment process from build to deploy

setlocal enabledelayedexpansion

REM Configuration
set DOCKER_HUB_USERNAME=
set VERSION_TYPE=patch
set SERVICES=all
set SKIP_BUILD=false
set SKIP_DEPLOY=false
set ENV_FILE=deployment\.env.production

REM Parse command line arguments
:parse_args
if "%~1"=="" goto end_parse
if "%~1"=="-u" (
    set DOCKER_HUB_USERNAME=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--username" (
    set DOCKER_HUB_USERNAME=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-t" (
    set VERSION_TYPE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--type" (
    set VERSION_TYPE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-s" (
    set SERVICES=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--services" (
    set SERVICES=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-e" (
    set ENV_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--env-file" (
    set ENV_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--skip-build" (
    set SKIP_BUILD=true
    shift
    goto parse_args
)
if "%~1"=="--skip-deploy" (
    set SKIP_DEPLOY=true
    shift
    goto parse_args
)
if "%~1"=="--build-only" (
    set SKIP_DEPLOY=true
    shift
    goto parse_args
)
if "%~1"=="-h" goto show_usage
if "%~1"=="--help" goto show_usage

echo [ERROR] Unknown option: %~1
goto show_usage

:end_parse

REM Validate required parameters
if "%DOCKER_HUB_USERNAME%"=="" (
    echo [ERROR] Docker Hub username is required
    echo [WARNING] Use --username option to specify your Docker Hub username
    exit /b 1
)

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    exit /b 1
)

REM Check if required scripts exist
if not exist "deployment\build.bat" (
    echo [ERROR] Build script not found: deployment\build.bat
    exit /b 1
)

if not exist "deployment\deploy.bat" (
    echo [ERROR] Deploy script not found: deployment\deploy.bat
    exit /b 1
)

echo ================================
echo GoID Production Deployment
echo ================================
echo [INFO] Docker Hub Username: %DOCKER_HUB_USERNAME%
echo [INFO] Version Type: %VERSION_TYPE%
echo [INFO] Services: %SERVICES%
echo [INFO] Environment File: %ENV_FILE%
echo [INFO] Skip Build: %SKIP_BUILD%
echo [INFO] Skip Deploy: %SKIP_DEPLOY%

REM Step 1: Build and push images (if not skipped)
if "%SKIP_BUILD%"=="false" (
    echo ================================
    echo Step 1: Building and Pushing Images
    echo ================================
    
    if "%SERVICES%"=="all" (
        echo [INFO] Building all services...
        deployment\build.bat --username %DOCKER_HUB_USERNAME% --type %VERSION_TYPE%
        if errorlevel 1 (
            echo [ERROR] Failed to build services
            exit /b 1
        )
    ) else (
        echo [INFO] Building specific services: %SERVICES%
        for %%s in (%SERVICES%) do (
            echo [INFO] Building service: %%s
            deployment\build.bat --service %%s --username %DOCKER_HUB_USERNAME% --type %VERSION_TYPE%
            if errorlevel 1 (
                echo [ERROR] Failed to build service: %%s
                exit /b 1
            )
        )
    )
    
    echo [SUCCESS] All images built and pushed successfully!
) else (
    echo [WARNING] Skipping build step - using existing images
)

REM Step 2: Generate environment file if it doesn't exist
if not exist "%ENV_FILE%" (
    echo ================================
    echo Step 2: Generating Environment Configuration
    echo ================================
    echo [INFO] Environment file not found, generating template...
    
    deployment\deploy.bat generate --env-file %ENV_FILE%
    if errorlevel 1 (
        echo [ERROR] Failed to generate environment file
        exit /b 1
    )
    
    echo [WARNING] Please edit %ENV_FILE% with your production settings
    echo [WARNING] Especially update: DB_PASSWORD, SECRET_KEY, ALLOWED_HOSTS
    echo [INFO] After editing the file, run this script again to continue deployment
    exit /b 0
)

REM Step 3: Update environment file with Docker Hub username
echo ================================
echo Step 3: Updating Environment Configuration
echo ================================
echo [INFO] Updating Docker Hub username in environment file...

REM Update DOCKER_HUB_USERNAME in env file using PowerShell
powershell -Command "(Get-Content '%ENV_FILE%') -replace 'DOCKER_HUB_USERNAME=.*', 'DOCKER_HUB_USERNAME=%DOCKER_HUB_USERNAME%' | Set-Content '%ENV_FILE%'"

echo [SUCCESS] Environment configuration updated

REM Step 4: Deploy services (if not skipped)
if "%SKIP_DEPLOY%"=="false" (
    echo ================================
    echo Step 4: Deploying Services
    echo ================================
    
    REM Generate docker-compose file
    echo [INFO] Generating docker-compose configuration...
    deployment\deploy.bat generate --env-file %ENV_FILE%
    if errorlevel 1 (
        echo [ERROR] Failed to generate docker-compose file
        exit /b 1
    )
    
    REM Deploy services
    echo [INFO] Deploying services...
    if "%SERVICES%"=="all" (
        deployment\deploy.bat deploy --env-file %ENV_FILE%
    ) else (
        deployment\deploy.bat deploy --env-file %ENV_FILE% --services "%SERVICES%"
    )
    if errorlevel 1 (
        echo [ERROR] Failed to deploy services
        exit /b 1
    )
    
    echo [SUCCESS] Services deployed successfully!
    
    REM Step 5: Show deployment status
    echo ================================
    echo Step 5: Deployment Status
    echo ================================
    echo [INFO] Checking service status...
    deployment\deploy.bat status --env-file %ENV_FILE%
    
    echo [SUCCESS] Deployment completed successfully!
    echo [INFO] Your GoID system is now running in production mode
    echo.
    echo [INFO] Useful commands:
    echo [INFO]   Check status: deployment\deploy.bat status
    echo [INFO]   View logs:    deployment\deploy.bat logs
    echo [INFO]   Stop services: deployment\deploy.bat stop
    
) else (
    echo [WARNING] Skipping deployment step - images built only
    echo [INFO] To deploy later, run: deployment\deploy.bat deploy --env-file %ENV_FILE%
)

echo ================================
echo Deployment Complete
echo ================================
exit /b 0

:show_usage
echo GoID Complete Production Deployment Script
echo.
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   -u, --username USERNAME  Docker Hub username (required^)
echo   -t, --type TYPE         Version increment type (major^|minor^|patch^) [default: patch]
echo   -s, --services SERVICES Services to deploy (backend^|frontend^|biometric-service^|all^) [default: all]
echo   -e, --env-file FILE     Environment file [default: deployment\.env.production]
echo   --skip-build           Skip building images (use existing^)
echo   --skip-deploy          Skip deployment (build only^)
echo   --build-only           Same as --skip-deploy
echo   -h, --help             Show this help message
echo.
echo Examples:
echo   %0 --username myuser                    # Complete deployment with patch increment
echo   %0 -u myuser -t minor                   # Deploy with minor version increment
echo   %0 -u myuser --skip-build               # Deploy using existing images
echo   %0 -u myuser --build-only               # Build images only, don't deploy
echo   %0 -u myuser -s "backend frontend"     # Deploy specific services only
exit /b 0
