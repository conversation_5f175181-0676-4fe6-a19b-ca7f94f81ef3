#!/usr/bin/env python3
"""
Test script to verify that print_id_cards role is correctly filtered based on workflow type.

This script tests:
1. Autonomous kebeles show print_id_cards role
2. Centralized kebeles don't show print_id_cards role
3. Subcity tenants always show print_id_cards role
4. Workflow switching updates role availability
"""

import os
import sys
import django
import requests
import json

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from users.views_groups import GroupManagementViewSet
from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser

User = get_user_model()

class PrintRoleFilteringTester:
    def __init__(self):
        self.factory = RequestFactory()
        self.test_results = []

    def log_test(self, test_name, success, message=""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"   📝 {message}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })

    def test_autonomous_kebele_roles(self):
        """Test that autonomous kebeles show print_id_cards role."""
        print("\n🧪 Testing autonomous kebele roles...")
        
        try:
            # Find or create an autonomous kebele
            kebele = self.get_or_create_test_kebele('autonomous')
            
            # Get roles for this kebele
            roles = self.get_tenant_roles(kebele.id)
            
            expected_roles = ['clerk', 'kebele_leader', 'print_id_cards']
            missing_roles = [role for role in expected_roles if role not in roles]
            
            if missing_roles:
                self.log_test(
                    "Autonomous kebele roles",
                    False,
                    f"Missing roles: {missing_roles}. Got: {roles}"
                )
            else:
                self.log_test(
                    "Autonomous kebele roles",
                    True,
                    f"All expected roles present: {roles}"
                )
                
        except Exception as e:
            self.log_test("Autonomous kebele roles", False, str(e))

    def test_centralized_kebele_roles(self):
        """Test that centralized kebeles don't show print_id_cards role."""
        print("\n🧪 Testing centralized kebele roles...")
        
        try:
            # Find or create a centralized kebele
            kebele = self.get_or_create_test_kebele('centralized')
            
            # Get roles for this kebele
            roles = self.get_tenant_roles(kebele.id)
            
            expected_roles = ['clerk', 'kebele_leader']
            unexpected_roles = [role for role in roles if role not in expected_roles]
            
            if 'print_id_cards' in roles:
                self.log_test(
                    "Centralized kebele roles",
                    False,
                    f"print_id_cards role should not be available for centralized workflow. Got: {roles}"
                )
            elif unexpected_roles:
                self.log_test(
                    "Centralized kebele roles",
                    False,
                    f"Unexpected roles: {unexpected_roles}. Got: {roles}"
                )
            else:
                self.log_test(
                    "Centralized kebele roles",
                    True,
                    f"Correct roles for centralized workflow: {roles}"
                )
                
        except Exception as e:
            self.log_test("Centralized kebele roles", False, str(e))

    def test_subcity_roles(self):
        """Test that subcity tenants always show print_id_cards role."""
        print("\n🧪 Testing subcity roles...")
        
        try:
            # Find a subcity tenant
            subcity = Tenant.objects.filter(type='subcity').first()
            
            if not subcity:
                self.log_test("Subcity roles", False, "No subcity tenant found")
                return
            
            # Get roles for this subcity
            roles = self.get_tenant_roles(subcity.id)
            
            expected_roles = ['subcity_admin', 'subcity_system_admin', 'print_id_cards']
            missing_roles = [role for role in expected_roles if role not in roles]
            
            if missing_roles:
                self.log_test(
                    "Subcity roles",
                    False,
                    f"Missing roles: {missing_roles}. Got: {roles}"
                )
            else:
                self.log_test(
                    "Subcity roles",
                    True,
                    f"All expected roles present: {roles}"
                )
                
        except Exception as e:
            self.log_test("Subcity roles", False, str(e))

    def test_workflow_switching_updates_roles(self):
        """Test that switching workflow updates available roles."""
        print("\n🧪 Testing workflow switching updates roles...")
        
        try:
            # Get a test kebele
            kebele = self.get_or_create_test_kebele('centralized')
            
            # Get initial roles (should not include print_id_cards)
            initial_roles = self.get_tenant_roles(kebele.id)
            
            # Switch to autonomous workflow
            self.switch_kebele_workflow(kebele, 'autonomous')
            
            # Get updated roles (should now include print_id_cards)
            updated_roles = self.get_tenant_roles(kebele.id)
            
            # Check if print_id_cards was added
            if 'print_id_cards' not in initial_roles and 'print_id_cards' in updated_roles:
                self.log_test(
                    "Workflow switching updates roles",
                    True,
                    f"print_id_cards role correctly added after switching to autonomous. Before: {initial_roles}, After: {updated_roles}"
                )
            else:
                self.log_test(
                    "Workflow switching updates roles",
                    False,
                    f"print_id_cards role not properly updated. Before: {initial_roles}, After: {updated_roles}"
                )
                
            # Switch back to centralized
            self.switch_kebele_workflow(kebele, 'centralized')
            
        except Exception as e:
            self.log_test("Workflow switching updates roles", False, str(e))

    def get_or_create_test_kebele(self, workflow_type):
        """Get or create a test kebele with specified workflow type."""
        kebele_name = f"Test Kebele {workflow_type.title()}"
        
        # Try to find existing kebele
        kebele = Tenant.objects.filter(name=kebele_name, type='kebele').first()
        
        if not kebele:
            # Create new kebele
            kebele = Tenant.objects.create(
                name=kebele_name,
                type='kebele',
                schema_name=f"test_kebele_{workflow_type}"
            )
            print(f"   📋 Created test kebele: {kebele_name}")
        
        # Set workflow configuration
        workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
            tenant=kebele,
            defaults={'workflow_type': workflow_type}
        )
        
        if not created and workflow_config.workflow_type != workflow_type:
            workflow_config.workflow_type = workflow_type
            workflow_config.save()
            print(f"   📋 Updated workflow type to: {workflow_type}")
        
        return kebele

    def get_tenant_roles(self, tenant_id):
        """Get available roles for a tenant using the API logic."""
        try:
            # Create a mock request
            request = self.factory.get(f'/api/auth/group-management/tenant_groups/?tenant_id={tenant_id}')
            request.user = self.get_test_user()
            
            # Create viewset instance and call the method
            viewset = GroupManagementViewSet()
            viewset.request = request
            
            response = viewset.tenant_groups(request)
            
            if response.status_code == 200:
                groups = response.data.get('groups', [])
                roles = [group['name'] for group in groups]
                return roles
            else:
                print(f"   ❌ API call failed with status {response.status_code}")
                return []
                
        except Exception as e:
            print(f"   ❌ Error getting tenant roles: {e}")
            return []

    def switch_kebele_workflow(self, kebele, new_workflow_type):
        """Switch kebele workflow type."""
        workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
            tenant=kebele,
            defaults={'workflow_type': new_workflow_type}
        )
        
        workflow_config.workflow_type = new_workflow_type
        workflow_config.save()
        
        print(f"   📋 Switched {kebele.name} to {new_workflow_type} workflow")

    def get_test_user(self):
        """Get or create a test user for API calls."""
        user = User.objects.filter(is_superuser=True).first()
        if not user:
            user = User.objects.create_superuser(
                username='test_admin',
                email='<EMAIL>',
                password='testpass123'
            )
        return user

    def generate_report(self):
        """Generate test report."""
        print("\n" + "="*60)
        print("📊 PRINT ROLE FILTERING TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['test']}: {result['message']}")
        
        print("\n📋 Detailed Results:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['test']}")
            if result['message']:
                print(f"      📝 {result['message']}")

    def run_all_tests(self):
        """Run all print role filtering tests."""
        print("🚀 Starting Print Role Filtering Tests")
        print("="*60)
        
        self.test_autonomous_kebele_roles()
        self.test_centralized_kebele_roles()
        self.test_subcity_roles()
        self.test_workflow_switching_updates_roles()
        
        self.generate_report()

if __name__ == "__main__":
    tester = PrintRoleFilteringTester()
    tester.run_all_tests()
