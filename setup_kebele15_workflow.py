#!/usr/bin/env python3
"""
Setup Kebele15 Workflow Configuration

This script creates the missing workflow configuration for Kebele15
and fixes any ID cards that are in inconsistent states.
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from idcards.models import IDCard, IDCardStatus
from django.utils import timezone

def setup_kebele15_workflow():
    """Setup proper workflow configuration for Kebele15."""
    print("🔧 Setting up Kebele15 Workflow Configuration")
    print("=" * 50)
    
    try:
        with transaction.atomic():
            # Get Kebele15 tenant
            kebele15 = Tenant.objects.get(id=18, name='Kebele15', type='kebele')
            print(f"📍 Found Kebele: {kebele15.name} (ID: {kebele15.id})")
            
            # Create or update workflow configuration
            workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                tenant=kebele15,
                defaults={
                    'workflow_type': 'centralized',
                    'id_card_processing': {
                        'can_print_locally': False,
                        'requires_higher_approval': True,
                        'approval_levels': ['kebele_leader', 'subcity_admin'],
                        'printing_authority': 'subcity',
                        'quality_control': 'centralized'
                    },
                    'approval_workflow': {
                        'final_approval_level': 'subcity_admin',
                        'escalation_required': True,
                        'post_approval_printing': False
                    }
                }
            )
            
            if created:
                print("✅ Created new centralized workflow configuration")
            else:
                print("✅ Workflow configuration already exists")
                # Ensure it's set to centralized
                if workflow_config.workflow_type != 'centralized':
                    workflow_config.workflow_type = 'centralized'
                    workflow_config.id_card_processing = {
                        'can_print_locally': False,
                        'requires_higher_approval': True,
                        'approval_levels': ['kebele_leader', 'subcity_admin'],
                        'printing_authority': 'subcity',
                        'quality_control': 'centralized'
                    }
                    workflow_config.approval_workflow = {
                        'final_approval_level': 'subcity_admin',
                        'escalation_required': True,
                        'post_approval_printing': False
                    }
                    workflow_config.save()
                    print("🔄 Updated workflow to centralized")
            
            print(f"📋 Current workflow type: {workflow_config.workflow_type}")
            print(f"📋 ID card processing: {workflow_config.id_card_processing}")
            
            return workflow_config
            
    except Exception as e:
        print(f"❌ Error setting up workflow: {e}")
        return None

def analyze_id_cards():
    """Analyze ID cards in Kebele15 to identify issues."""
    print("\n📊 Analyzing ID Cards in Kebele15")
    print("=" * 50)
    
    try:
        kebele15 = Tenant.objects.get(id=18, name='Kebele15', type='kebele')
        
        with schema_context(kebele15.schema_name):
            all_cards = IDCard.objects.all()
            print(f"📋 Total ID cards: {all_cards.count()}")
            
            if all_cards.count() == 0:
                print("ℹ️ No ID cards found in Kebele15")
                return
            
            # Analyze by status
            print("\n📈 Status Distribution:")
            for status_choice in IDCardStatus.choices:
                status_code = status_choice[0]
                count = all_cards.filter(status=status_code).count()
                if count > 0:
                    print(f"   {status_choice[1]}: {count}")
            
            # Find problematic cards
            print("\n🔍 Detailed Analysis:")
            
            # Cards approved by kebele but not subcity (should be KEBELE_APPROVED)
            kebele_only_approved = all_cards.filter(
                kebele_approved_by__isnull=False,
                subcity_approved_by__isnull=True,
                status=IDCardStatus.APPROVED
            )
            
            if kebele_only_approved.exists():
                print(f"🚨 Found {kebele_only_approved.count()} cards approved by kebele only but marked as APPROVED")
                for card in kebele_only_approved[:3]:
                    print(f"   - Card #{card.card_number}: {card.citizen.get_full_name()}")
                    print(f"     Kebele approved: {card.kebele_approved_at}")
                    print(f"     Subcity approved: {card.subcity_approved_at}")
            
            # Cards that should be waiting for subcity approval
            waiting_for_subcity = all_cards.filter(status=IDCardStatus.KEBELE_APPROVED)
            print(f"✅ Cards waiting for subcity approval: {waiting_for_subcity.count()}")
            
            # Fully approved cards
            fully_approved = all_cards.filter(
                status=IDCardStatus.APPROVED,
                kebele_approved_by__isnull=False,
                subcity_approved_by__isnull=False
            )
            print(f"✅ Fully approved cards (kebele + subcity): {fully_approved.count()}")
            
            return {
                'total': all_cards.count(),
                'problematic': kebele_only_approved.count(),
                'waiting_subcity': waiting_for_subcity.count(),
                'fully_approved': fully_approved.count()
            }
            
    except Exception as e:
        print(f"❌ Error analyzing ID cards: {e}")
        return None

def fix_problematic_cards():
    """Fix ID cards that are in inconsistent states."""
    print("\n🔧 Fixing Problematic ID Cards")
    print("=" * 50)
    
    try:
        kebele15 = Tenant.objects.get(id=18, name='Kebele15', type='kebele')
        
        with schema_context(kebele15.schema_name):
            # Find cards that are marked as APPROVED but only have kebele approval
            problematic_cards = IDCard.objects.filter(
                kebele_approved_by__isnull=False,
                subcity_approved_by__isnull=True,
                status=IDCardStatus.APPROVED
            )
            
            if not problematic_cards.exists():
                print("✅ No problematic cards found")
                return 0
            
            print(f"🔧 Found {problematic_cards.count()} cards to fix")
            
            fixed_count = 0
            with transaction.atomic():
                for card in problematic_cards:
                    print(f"   Fixing Card #{card.card_number}: {card.citizen.get_full_name()}")
                    
                    # Reset to kebele_approved status (waiting for subcity approval)
                    card.status = IDCardStatus.KEBELE_APPROVED
                    card.has_subcity_pattern = False  # Remove subcity pattern
                    card.submitted_to_subcity_at = timezone.now()  # Mark as submitted to subcity
                    
                    # Clear any printing-related flags that shouldn't be set yet
                    card.printed_at = None
                    card.printed_by = None
                    
                    card.save()
                    
                    print(f"     ✅ Reset to KEBELE_APPROVED status")
                    fixed_count += 1
            
            print(f"✅ Fixed {fixed_count} ID cards")
            return fixed_count
            
    except Exception as e:
        print(f"❌ Error fixing cards: {e}")
        return 0

def clear_cache():
    """Clear any cached workflow data."""
    print("\n🗑️ Clearing Cache")
    print("=" * 50)
    
    try:
        from django.core.cache import cache
        
        cache_keys = [
            'tenant_workflow_18',
            'workflow_config_18',
            'kebele15_workflow',
            'biometric_templates_all'
        ]
        
        for key in cache_keys:
            cache.delete(key)
            print(f"🗑️ Cleared cache key: {key}")
        
        # Clear all workflow-related cache
        cache.delete_many([k for k in cache._cache.keys() if 'workflow' in k.lower()])
        print("🗑️ Cleared all workflow-related cache")
        
    except Exception as e:
        print(f"⚠️ Error clearing cache: {e}")

def verify_setup():
    """Verify that the setup was successful."""
    print("\n✅ Verifying Setup")
    print("=" * 50)
    
    try:
        kebele15 = Tenant.objects.get(id=18, name='Kebele15', type='kebele')
        
        # Check workflow configuration
        try:
            workflow_config = kebele15.workflow_config
            print(f"✅ Workflow type: {workflow_config.workflow_type}")
            print(f"✅ Requires higher approval: {workflow_config.id_card_processing.get('requires_higher_approval', False)}")
            print(f"✅ Approval levels: {workflow_config.id_card_processing.get('approval_levels', [])}")
        except Exception as e:
            print(f"❌ Workflow config issue: {e}")
            return False
        
        # Check ID card states
        with schema_context(kebele15.schema_name):
            all_cards = IDCard.objects.all()
            
            if all_cards.exists():
                problematic_cards = all_cards.filter(
                    status=IDCardStatus.APPROVED,
                    subcity_approved_by__isnull=True
                )
                
                print(f"📊 Total cards: {all_cards.count()}")
                print(f"📊 Problematic cards: {problematic_cards.count()}")
                
                if problematic_cards.count() == 0:
                    print("✅ All ID cards are in correct workflow state!")
                    return True
                else:
                    print("❌ Some problematic cards still exist")
                    return False
            else:
                print("ℹ️ No ID cards to verify")
                return True
                
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Kebele15 Workflow Setup Script")
    print("=" * 50)
    
    # Step 1: Setup workflow configuration
    workflow_config = setup_kebele15_workflow()
    
    if workflow_config:
        # Step 2: Analyze current ID card states
        analysis = analyze_id_cards()
        
        if analysis and analysis['problematic'] > 0:
            # Step 3: Fix problematic cards
            fixed_count = fix_problematic_cards()
            
            # Step 4: Clear cache
            clear_cache()
            
            # Step 5: Verify setup
            success = verify_setup()
            
            if success:
                print("\n🎉 Kebele15 workflow has been successfully configured!")
                print("\nWorkflow Summary:")
                print("✅ Type: Centralized")
                print("✅ Kebele leader approval → Sends to subcity admin")
                print("✅ Subcity admin approval → Enables printing")
                print("✅ Both approvals required for final approval")
                
                if fixed_count > 0:
                    print(f"\n🔧 Fixed {fixed_count} ID cards that were in inconsistent states")
                    print("   These cards now require subcity approval before printing")
            else:
                print("\n⚠️ Setup completed but verification failed")
        else:
            print("\n✅ Workflow configuration completed - no problematic cards found")
    else:
        print("\n❌ Failed to setup workflow configuration")
