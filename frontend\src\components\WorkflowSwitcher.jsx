import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  SwapHoriz as SwitchIcon,
  AccountTree as WorkflowIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  Security as SecurityIcon,
  Group as GroupIcon
} from '@mui/icons-material';
import axios from 'axios';

const WorkflowSwitcher = ({ tenant, onWorkflowChanged }) => {
  const [workflowStatus, setWorkflowStatus] = useState(null);
  const [availableWorkflows, setAvailableWorkflows] = useState([]);
  const [switchDialogOpen, setSwitchDialogOpen] = useState(false);
  const [impactDialogOpen, setImpactDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [switching, setSwitching] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Switch form state
  const [switchForm, setSwitchForm] = useState({
    workflow_type: '',
    reason: ''
  });
  
  // Impact analysis data
  const [impactData, setImpactData] = useState(null);

  useEffect(() => {
    if (tenant) {
      fetchWorkflowStatus();
      fetchAvailableWorkflows();
    }
  }, [tenant]);

  const fetchWorkflowStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/auth/workflow-management/workflow_status/?tenant_id=${tenant.id}`);
      setWorkflowStatus(response.data);
    } catch (error) {
      console.error('Failed to fetch workflow status:', error);
      setError('Failed to load workflow status');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableWorkflows = async () => {
    try {
      const response = await axios.get(`/api/auth/workflow-management/available_workflows/?tenant_id=${tenant.id}`);
      setAvailableWorkflows(response.data.available_workflows || []);
    } catch (error) {
      console.error('Failed to fetch available workflows:', error);
    }
  };

  const analyzeWorkflowImpact = async (targetWorkflow) => {
    try {
      const response = await axios.get(
        `/api/auth/workflow-management/workflow_impact/?tenant_id=${tenant.id}&target_workflow=${targetWorkflow}`
      );
      setImpactData(response.data);
      setImpactDialogOpen(true);
    } catch (error) {
      console.error('Failed to analyze workflow impact:', error);
      setError('Failed to analyze workflow impact');
    }
  };

  const handleSwitchWorkflow = async () => {
    if (!switchForm.workflow_type) {
      setError('Please select a workflow type');
      return;
    }

    setSwitching(true);
    try {
      const response = await axios.post('/api/auth/workflow-management/switch_workflow/', {
        tenant_id: tenant.id,
        workflow_type: switchForm.workflow_type,
        reason: switchForm.reason
      });

      if (response.data.success) {
        setSuccess(`Successfully switched to ${switchForm.workflow_type} workflow`);
        setSwitchDialogOpen(false);
        setSwitchForm({ workflow_type: '', reason: '' });
        await fetchWorkflowStatus();
        
        if (onWorkflowChanged) {
          onWorkflowChanged(response.data);
        }
      } else {
        setError(response.data.message || 'Failed to switch workflow');
      }
    } catch (error) {
      console.error('Failed to switch workflow:', error);
      setError(error.response?.data?.error || 'Failed to switch workflow');
    } finally {
      setSwitching(false);
    }
  };

  const getWorkflowIcon = (workflowType) => {
    switch (workflowType) {
      case 'autonomous':
        return <SecurityIcon color="success" />;
      case 'centralized':
        return <GroupIcon color="primary" />;
      default:
        return <WorkflowIcon />;
    }
  };

  const getWorkflowColor = (workflowType) => {
    switch (workflowType) {
      case 'autonomous':
        return 'success';
      case 'centralized':
        return 'primary';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 2 }}>
            Loading workflow status...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (!workflowStatus) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">
            Failed to load workflow status
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Current Workflow Status */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <WorkflowIcon sx={{ mr: 1 }} />
              Workflow Management
            </Typography>
            <Chip
              icon={getWorkflowIcon(workflowStatus.workflow_type)}
              label={`${workflowStatus.workflow_type.toUpperCase()} WORKFLOW`}
              color={getWorkflowColor(workflowStatus.workflow_type)}
              variant="outlined"
            />
          </Box>

          <Typography variant="body2" color="textSecondary" paragraph>
            {workflowStatus.workflow_description}
          </Typography>

          {/* Workflow Statistics */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="h4" color="primary">
                  {workflowStatus.total_users}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Users
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="h4" color="success.main">
                  {workflowStatus.aligned_users}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Aligned Users
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="h4" color="info.main">
                  {workflowStatus.alignment_percentage}%
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Alignment
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Workflow Actions */}
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<SwitchIcon />}
              onClick={() => setSwitchDialogOpen(true)}
              disabled={availableWorkflows.length <= 1}
            >
              Switch Workflow
            </Button>
            
            {availableWorkflows.map((workflow) => (
              workflow.type !== workflowStatus.workflow_type && (
                <Button
                  key={workflow.type}
                  variant="outlined"
                  startIcon={<InfoIcon />}
                  onClick={() => analyzeWorkflowImpact(workflow.type)}
                >
                  Analyze {workflow.name} Impact
                </Button>
              )
            ))}
          </Box>

          {/* User Alignment Status */}
          {workflowStatus.alignment_percentage < 100 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                {workflowStatus.total_users - workflowStatus.aligned_users} users are not properly aligned with the current workflow. 
                Consider updating their group assignments.
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Switch Workflow Dialog */}
      <Dialog open={switchDialogOpen} onClose={() => setSwitchDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Switch Workflow</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Switching workflow will update all user group assignments and permissions.
          </Alert>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Target Workflow</InputLabel>
                <Select
                  value={switchForm.workflow_type}
                  onChange={(e) => setSwitchForm({ ...switchForm, workflow_type: e.target.value })}
                  label="Target Workflow"
                >
                  {availableWorkflows
                    .filter(workflow => workflow.type !== workflowStatus.workflow_type)
                    .map((workflow) => (
                      <MenuItem key={workflow.type} value={workflow.type}>
                        <Box>
                          <Typography variant="body1">{workflow.name}</Typography>
                          <Typography variant="caption" color="textSecondary">
                            {workflow.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Reason for Switch"
                value={switchForm.reason}
                onChange={(e) => setSwitchForm({ ...switchForm, reason: e.target.value })}
                multiline
                rows={3}
                helperText="Provide a reason for this workflow change (for audit purposes)"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSwitchDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSwitchWorkflow}
            variant="contained"
            disabled={switching || !switchForm.workflow_type}
            startIcon={switching ? <CircularProgress size={20} /> : <SwitchIcon />}
          >
            {switching ? 'Switching...' : 'Switch Workflow'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Impact Analysis Dialog */}
      <Dialog open={impactDialogOpen} onClose={() => setImpactDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Workflow Impact Analysis</DialogTitle>
        <DialogContent>
          {impactData && (
            <Box>
              <Alert 
                severity={impactData.estimated_impact === 'High' ? 'error' : 
                         impactData.estimated_impact === 'Medium' ? 'warning' : 'info'}
                sx={{ mb: 2 }}
              >
                <Typography variant="body2">
                  <strong>Impact Level: {impactData.estimated_impact}</strong>
                  <br />
                  Estimated Timeline: {impactData.estimated_timeline}
                </Typography>
              </Alert>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="h6" gutterBottom>Current State</Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="Pending ID Cards" 
                        secondary={impactData.pending_id_cards}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Approved ID Cards" 
                        secondary={impactData.approved_id_cards}
                      />
                    </ListItem>
                  </List>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="h6" gutterBottom>User Distribution</Typography>
                  <List dense>
                    {Object.entries(impactData.user_count_by_role).map(([role, count]) => (
                      <ListItem key={role}>
                        <ListItemText 
                          primary={role.replace('_', ' ').toUpperCase()} 
                          secondary={`${count} users`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>Changes Needed</Typography>
                  <List>
                    {impactData.changes_needed.map((change, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={change} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>Recommendations</Typography>
                  <List>
                    {impactData.recommendations.map((recommendation, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <InfoIcon color="info" />
                        </ListItemIcon>
                        <ListItemText primary={recommendation} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImpactDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WorkflowSwitcher;
