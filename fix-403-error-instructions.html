<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Workflow 403 Error</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 40px;
        }
        .error-box {
            background: #ffebee;
            border: 2px solid #f44336;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .error-title {
            color: #c62828;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        .error-details {
            color: #666;
            font-family: monospace;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .solution-steps {
            display: grid;
            gap: 20px;
            margin: 30px 0;
        }
        .step {
            background: #f8f9fa;
            border-left: 5px solid #28a745;
            padding: 20px;
            border-radius: 0 10px 10px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #28a745;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #28a745;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .code {
            background: #263238;
            color: #4fc3f7;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .quick-fix {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
        }
        .quick-fix h3 {
            margin-top: 0;
        }
        .fix-button {
            background: white;
            color: #4CAF50;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .diagnostic {
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .diagnostic h3 {
            color: #f57c00;
            margin-top: 0;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .checklist li:before {
            content: "☐";
            margin-right: 10px;
            font-size: 1.2em;
            color: #666;
        }
        .checklist li.checked:before {
            content: "✅";
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 Fix Workflow 403 Error</h1>
            <p>Complete solution for workflow switching permission issues</p>
        </div>
        
        <div class="content">
            <div class="error-box">
                <div class="error-title">❌ Error Detected</div>
                <p>The workflow switching functionality is returning a <strong>403 Forbidden</strong> error when trying to switch kebele workflows.</p>
                
                <div class="error-details">
                    Failed to load resource: the server responded with a status of 403 (Forbidden)
                    /api/tenants/8/workflow/switch/
                    
                    Error switching workflow: Request failed with status code 403
                </div>
            </div>
            
            <div class="quick-fix">
                <h3>⚡ Quick Fix</h3>
                <p>Run this single command to fix all permission issues:</p>
                <div class="code">python fix_workflow_403_error.py</div>
                <p>This will automatically diagnose and fix all workflow permission problems.</p>
            </div>
            
            <div class="solution-steps">
                <div class="step">
                    <h3>
                        <span class="step-number">1</span>
                        Run the Permission Fix Script
                    </h3>
                    <p>Execute the comprehensive fix script that addresses all potential permission issues:</p>
                    <div class="code">
# Navigate to your project directory
cd /path/to/your/project

# Run the fix script
python fix_workflow_403_error.py
                    </div>
                    <p>This script will:</p>
                    <ul>
                        <li>Create missing workflow permissions</li>
                        <li>Assign permissions to subcity and city admins</li>
                        <li>Fix tenant hierarchy issues</li>
                        <li>Test API access for sample users</li>
                    </ul>
                </div>
                
                <div class="step">
                    <h3>
                        <span class="step-number">2</span>
                        Restart Django Server
                    </h3>
                    <p>After running the fix script, restart your Django development server:</p>
                    <div class="code">
# Stop the current server (Ctrl+C)
# Then restart it
python manage.py runserver
                    </div>
                </div>
                
                <div class="step">
                    <h3>
                        <span class="step-number">3</span>
                        Clear Browser Cache
                    </h3>
                    <p>Clear your browser cache and refresh the page:</p>
                    <ul>
                        <li>Press <strong>Ctrl+Shift+R</strong> (hard refresh)</li>
                        <li>Or open Developer Tools (F12) → Network tab → check "Disable cache"</li>
                        <li>Or use incognito/private browsing mode</li>
                    </ul>
                </div>
                
                <div class="step">
                    <h3>
                        <span class="step-number">4</span>
                        Test Workflow Switching
                    </h3>
                    <p>Try the workflow switching functionality again:</p>
                    <ol>
                        <li>Navigate to Kebele User Management</li>
                        <li>Look for the "Workflow Management" section</li>
                        <li>Click "Switch Workflow" on any kebele card</li>
                        <li>Check browser console for debug logs</li>
                    </ol>
                </div>
            </div>
            
            <div class="diagnostic">
                <h3>🔍 Diagnostic Checklist</h3>
                <p>If the issue persists, check these items:</p>
                
                <ul class="checklist">
                    <li>User is logged in as subcity admin or city admin</li>
                    <li>User has proper tenant assignment (subcity or city)</li>
                    <li>Target kebele belongs to user's subcity</li>
                    <li>Django server is running without errors</li>
                    <li>Database migrations are up to date</li>
                    <li>Workflow permissions exist in database</li>
                    <li>User has workflow management permissions</li>
                    <li>API endpoint is accessible</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>
                    <span class="step-number">5</span>
                    Manual Verification
                </h3>
                <p>If the automatic fix doesn't work, manually verify these items:</p>
                
                <div class="code">
# Check user permissions in Django shell
python manage.py shell

# Run these commands:
from django.contrib.auth import get_user_model
User = get_user_model()

# Find your user
user = User.objects.get(email='<EMAIL>')
print(f"Role: {user.role}")
print(f"Tenant: {user.tenant}")
print(f"Has manage_workflows: {user.has_perm('auth.manage_workflows')}")

# Check available kebeles
from tenants.models import Tenant
kebeles = Tenant.objects.filter(type='kebele', parent=user.tenant)
print(f"Child kebeles: {kebeles.count()}")
                </div>
            </div>
            
            <div class="step">
                <h3>
                    <span class="step-number">6</span>
                    Alternative Solutions
                </h3>
                <p>If the issue still persists, try these alternative approaches:</p>
                
                <h4>Option A: Grant Superuser Access (Temporary)</h4>
                <div class="code">
# Make your user a superuser temporarily
python manage.py shell
from django.contrib.auth import get_user_model
User = get_user_model()
user = User.objects.get(email='<EMAIL>')
user.is_superuser = True
user.save()
                </div>
                
                <h4>Option B: Create New Subcity Admin</h4>
                <div class="code">
# Create a new subcity admin with proper permissions
python manage.py setup_workflow_rbac --force
python manage.py createsuperuser
                </div>
                
                <h4>Option C: Check API Endpoint Directly</h4>
                <div class="code">
# Test the API endpoint directly
curl -X POST http://localhost:8000/api/tenants/8/workflow/switch/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"workflow_type": "autonomous", "reason": "Test switch"}'
                </div>
            </div>
            
            <div class="quick-fix">
                <h3>📞 Need Help?</h3>
                <p>If you're still experiencing issues after following these steps:</p>
                <ol>
                    <li>Check the Django server logs for detailed error messages</li>
                    <li>Verify your user account has the correct role and tenant assignment</li>
                    <li>Ensure all database migrations have been applied</li>
                    <li>Try creating a new test user with subcity admin role</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
