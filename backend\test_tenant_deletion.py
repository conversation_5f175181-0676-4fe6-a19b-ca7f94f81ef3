#!/usr/bin/env python3
"""
Test tenant deletion to debug the 500 error.
"""

import os
import sys
import django
from django.db import connection

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

def test_tenant_deletion():
    """Test tenant deletion with detailed error reporting."""
    from tenants.models.tenant import Tenant, Domain
    
    print("🔍 Testing tenant deletion...")
    
    # Find a test tenant to delete (or create one)
    try:
        # Look for a tenant we can safely delete
        test_tenant = Tenant.objects.filter(name__icontains='test').first()
        
        if not test_tenant:
            print("📝 Creating a test tenant for deletion...")
            test_tenant = Tenant.objects.create(
                name='Test Deletion Tenant',
                schema_name='test_deletion',
                type='subcity'
            )
            print(f"✅ Created test tenant: {test_tenant.name}")
        
        print(f"🎯 Testing deletion of tenant: {test_tenant.name} (ID: {test_tenant.id})")
        
        # Check if tenant has children
        if test_tenant.children.exists():
            print(f"⚠️ Tenant has {test_tenant.children.count()} children - cannot delete")
            return
        
        # Store info before deletion
        tenant_id = test_tenant.id
        tenant_name = test_tenant.name
        schema_name = test_tenant.schema_name
        
        print(f"🔍 Tenant details:")
        print(f"   ID: {tenant_id}")
        print(f"   Name: {tenant_name}")
        print(f"   Schema: {schema_name}")
        print(f"   Type: {test_tenant.type}")
        
        # Step 1: Clean up users
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        tenant_users = User.objects.filter(tenant=test_tenant)
        user_count = tenant_users.count()
        print(f"🔍 Found {user_count} users to clean up")
        
        if user_count > 0:
            tenant_users.delete()
            print(f"✅ Deleted {user_count} users")
        
        # Step 2: Drop schema
        try:
            with connection.cursor() as cursor:
                print(f"🗑️ Dropping schema: {schema_name}")
                cursor.execute(f'DROP SCHEMA IF EXISTS "{schema_name}" CASCADE;')
                print(f"✅ Schema dropped successfully")
        except Exception as schema_error:
            print(f"⚠️ Schema drop warning: {schema_error}")
        
        # Step 3: Delete domains
        try:
            domains = Domain.objects.filter(tenant=test_tenant)
            domain_count = domains.count()
            if domain_count > 0:
                domains.delete()
                print(f"✅ Deleted {domain_count} domains")
        except Exception as domain_error:
            print(f"⚠️ Domain deletion warning: {domain_error}")
        
        # Step 4: Delete tenant record
        try:
            print(f"🗑️ Deleting tenant record...")
            test_tenant.delete()
            print(f"✅ Tenant deleted successfully!")
            
        except Exception as tenant_error:
            print(f"❌ Tenant deletion failed: {tenant_error}")
            print(f"🔧 Trying direct SQL deletion...")
            
            try:
                with connection.cursor() as cursor:
                    cursor.execute(
                        "DELETE FROM tenants_tenant WHERE id = %s",
                        [tenant_id]
                    )
                print(f"✅ Tenant deleted using direct SQL")
                
            except Exception as sql_error:
                print(f"❌ Direct SQL deletion also failed: {sql_error}")
                raise sql_error
        
        print(f"🎉 Tenant deletion test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_tenant_deletion()
