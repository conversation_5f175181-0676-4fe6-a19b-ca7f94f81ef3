import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Tabs,
  Tab,
  Grid,
  Paper,
  IconButton,
  CircularProgress,
  Alert,
  InputAdornment,
  Divider
} from '@mui/material';
import {
  Description as FileTextIcon,
  Download as DownloadIcon,
  Add as PlusIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  CalendarToday as CalendarIcon,
  Schedule as ClockIcon,
  Person as UserIcon,
  BarChart as BarChartIcon,
  TableChart as FileSpreadsheetIcon,
  Code as FileJsonIcon,
  Delete as TrashIcon,
  Visibility as EyeIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import reportsService from '../../services/reportsService';

const ReportsDashboard = () => {
  const [reports, setReports] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [formatFilter, setFormatFilter] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to load real data from the API
      try {
        const [reportsData, templatesData] = await Promise.all([
          reportsService.getReports(),
          reportsService.getReportTemplates()
        ]);

        setReports(reportsData.results || reportsData || []);
        setTemplates(templatesData.results || templatesData || []);

      } catch (apiError) {
        console.warn('API not available, using mock data:', apiError);

        // Fallback to mock data if API is not available
        const mockReports = [
          {
            id: '1',
            title: 'Demographic Analysis Report',
            description: 'Comprehensive demographic analysis of registered citizens',
            status: 'completed',
            format: 'pdf',
            generated_at: new Date().toISOString(),
            generated_by: { username: 'admin' }
          },
          {
            id: '2',
            title: 'Registration Trends Report',
            description: 'Monthly registration trends analysis',
            status: 'pending',
            format: 'excel',
            generated_at: new Date().toISOString(),
            generated_by: { username: 'admin' }
          }
        ];

        const mockTemplates = [
          {
            id: '1',
            name: 'Standard Demographic Report',
            description: 'Analyze citizen demographics by age, gender, education',
            report_type: 'demographic'
          },
          {
            id: '2',
            name: 'Registration Trends Analysis',
            description: 'Track registration patterns over time',
            report_type: 'registration_trends'
          },
          {
            id: '3',
            name: 'ID Card Status Overview',
            description: 'Monitor ID card issuance and workflow status',
            report_type: 'id_card_status'
          }
        ];

        setReports(mockReports);
        setTemplates(mockTemplates);
      }
      
    } catch (error) {
      console.error('Error loading reports:', error);
      setError('Failed to load reports data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getFormatIcon = (format) => {
    switch (format) {
      case 'pdf': return <FileTextIcon />;
      case 'excel': return <FileSpreadsheetIcon />;
      case 'csv': return <FileSpreadsheetIcon />;
      case 'json': return <FileJsonIcon />;
      default: return <FileTextIcon />;
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || report.status === statusFilter;
    const matchesFormat = !formatFilter || report.format === formatFilter;
    
    return matchesSearch && matchesStatus && matchesFormat;
  });

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>Loading reports...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Data Reports
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate and manage analytical reports for your organization
          </Typography>
        </Box>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<CalendarIcon />}
            onClick={() => {/* TODO: Open schedule modal */}}
          >
            Schedule Report
          </Button>
          <Button
            variant="contained"
            startIcon={<PlusIcon />}
            onClick={() => {/* TODO: Open generation modal */}}
          >
            Generate Report
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search reports..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <MenuItem value="">All Statuses</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Format</InputLabel>
              <Select
                value={formatFilter}
                label="Format"
                onChange={(e) => setFormatFilter(e.target.value)}
              >
                <MenuItem value="">All Formats</MenuItem>
                <MenuItem value="pdf">PDF</MenuItem>
                <MenuItem value="excel">Excel</MenuItem>
                <MenuItem value="csv">CSV</MenuItem>
                <MenuItem value="json">JSON</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Reports" />
          <Tab label="Templates" />
          <Tab label="Schedules" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {filteredReports.length === 0 ? (
            <Grid item xs={12}>
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <FileTextIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No reports found
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Generate your first report to get started with data analytics.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<PlusIcon />}
                  onClick={() => {/* TODO: Open generation modal */}}
                >
                  Generate Report
                </Button>
              </Paper>
            </Grid>
          ) : (
            filteredReports.map((report) => (
              <Grid item xs={12} md={6} lg={4} key={report.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardHeader
                    avatar={getFormatIcon(report.format)}
                    title={
                      <Typography variant="h6" component="div">
                        {report.title}
                      </Typography>
                    }
                    action={
                      <Chip
                        label={report.status}
                        color={getStatusColor(report.status)}
                        size="small"
                      />
                    }
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {report.description}
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <ClockIcon fontSize="small" color="action" />
                      <Typography variant="caption">
                        {format(new Date(report.generated_at), 'MMM dd, yyyy HH:mm')}
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                      <UserIcon fontSize="small" color="action" />
                      <Typography variant="caption">
                        {report.generated_by?.username || 'System'}
                      </Typography>
                    </Box>
                  </CardContent>
                  <Divider />
                  <Box p={1} display="flex" justifyContent="space-between">
                    <IconButton size="small" onClick={() => {/* TODO: View details */}}>
                      <EyeIcon />
                    </IconButton>
                    <IconButton size="small" onClick={() => {/* TODO: Download */}}>
                      <DownloadIcon />
                    </IconButton>
                    <IconButton size="small" onClick={() => {/* TODO: Delete */}}>
                      <TrashIcon />
                    </IconButton>
                  </Box>
                </Card>
              </Grid>
            ))
          )}
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          {templates.map((template) => (
            <Grid item xs={12} md={6} lg={4} key={template.id}>
              <Card>
                <CardHeader
                  title={template.name}
                  subheader={template.report_type}
                />
                <CardContent>
                  <Typography variant="body2" color="text.secondary">
                    {template.description}
                  </Typography>
                </CardContent>
                <Box p={1}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PlusIcon />}
                    onClick={() => {/* TODO: Generate from template */}}
                  >
                    Generate Report
                  </Button>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {tabValue === 2 && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <CalendarIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Scheduled Reports
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Schedule management coming soon...
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default ReportsDashboard;
