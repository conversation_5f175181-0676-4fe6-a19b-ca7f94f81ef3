# GoID Client Biometric Service Requirements
# Python dependencies for client-side fingerprint capture

# Web framework for HTTP API
Flask==2.3.3
Werkzeug==2.3.7

# HTTP client for server communication
requests==2.31.0
urllib3==2.0.7

# JSON handling and data processing
jsonschema==4.19.2

# Logging and configuration
PyYAML==6.0.1
python-dotenv==1.0.0

# Date and time handling
python-dateutil==2.8.2

# System and process management
psutil==5.9.6

# USB device communication (optional, for advanced device control)
pyusb==1.2.1

# Image processing (for fingerprint image handling)
Pillow==10.1.0

# Base64 and binary data handling
base64io==1.0.3

# Cross-platform file system operations
pathlib2==2.3.7

# Network utilities
netifaces==0.11.0

# Cryptography for secure communication (optional)
cryptography==41.0.7

# Rate limiting
Flask-Limiter==3.5.0

# CORS handling
Flask-CORS==4.0.0

# Configuration management
configparser==6.0.0

# Retry mechanisms for network calls
tenacity==8.2.3

# Health monitoring
py-healthcheck==1.10.1

# Windows desktop application and system tray
pystray==0.19.4
Pillow==10.1.0

# Windows service support
pywin32==306
pywin32-ctypes==0.2.2

# GUI framework for settings dialog
tkinter-tooltip==2.1.0

# Windows notifications
plyer==2.1.0

# Auto-start with Windows
winshell==0.6
