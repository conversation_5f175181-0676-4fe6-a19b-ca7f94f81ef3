import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  LinearProgress,
  Avatar,
  Rating,
  Pagination,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Download as DownloadIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon,
  TrendingUp as TrendingUpIcon,
  Reply as ReplyIcon
} from '@mui/icons-material';
import reportsService from '../../services/reportsService';
import { useAuth } from '../../contexts/AuthContext';

const ReportsAnalyticsDashboard = () => {
  const { user } = useAuth(); // Get user from AuthContext
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [staffFilter, setStaffFilter] = useState('All Kebeles');
  const [actionFilter, setActionFilter] = useState('All Status');
  const [tenantId, setTenantId] = useState(null);
  const [activeTab, setActiveTab] = useState(0); // Tab state for different views
  const [userRole, setUserRole] = useState(null); // Track user role for different data sources

  // Dashboard data state
  const [dashboardData, setDashboardData] = useState({
    totalCitizens: {
      count: 0,
      change: 0,
      trend: 'up'
    },
    totalIdCards: {
      count: 0,
      change: 0,
      trend: 'up'
    },
    completionRate: {
      pending: 0,
      processing: 0,
      completed: 0
    },
    reports: [],
    rawData: null, // Store the raw API response for exports
    anonymizedCitizens: [] // Store anonymized citizen data for research
  });

  // Get tenant ID from multiple sources for reliability
  const getTenantIdFromMultipleSources = () => {
    console.log('🔍 Getting tenant ID for reports...');

    // First try to get from localStorage user object
    try {
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      if (userData.tenant_id) {
        console.log('🔍 From localStorage user:', userData.tenant_id);
        return userData.tenant_id;
      }
      if (userData.tenant?.id) {
        console.log('🔍 From localStorage user.tenant:', userData.tenant.id);
        return userData.tenant.id;
      }
    } catch (e) {
      console.warn('Could not parse stored user data');
    }

    // Fallback: Extract from JWT token
    try {
      const accessToken = localStorage.getItem('accessToken');
      if (accessToken) {
        const base64Url = accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        const tokenData = JSON.parse(jsonPayload);
        if (tokenData.tenant_id) {
          console.log('🔍 From JWT token:', tokenData.tenant_id);
          return tokenData.tenant_id;
        }
      }
    } catch (error) {
      console.error('Error extracting tenant ID from JWT:', error);
    }

    return null;
  };

  useEffect(() => {
    console.log('🚀 ReportsAnalyticsDashboard: Initial useEffect triggered');
    const userTenantId = getTenantIdFromMultipleSources();
    console.log('🔍 Detected tenant ID:', userTenantId);

    // Get user role from AuthContext
    if (user) {
      console.log('🔍 Full user data from AuthContext:', user);
      setUserRole(user.role);
      console.log('🔍 User role detected:', user.role);
    } else {
      console.warn('No user found in AuthContext');
    }

    if (userTenantId) {
      console.log('✅ Setting tenant ID:', userTenantId);
      setTenantId(userTenantId);
    } else {
      console.log('❌ No tenant ID found');
      // Check if user is superadmin (who shouldn't access reports)
      try {
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        if (userData.role === 'superadmin' || userData.is_superuser) {
          setError('Reports are not available for superadmin users. Reports are only accessible to subcity admins and city admins for their respective areas.');
        } else {
          setError('No tenant information found. Please log in again or contact your administrator.');
        }
      } catch (e) {
        setError('No tenant information found. Please log in again or contact your administrator.');
      }
      setLoading(false);
    }
  }, [user]); // Add user as dependency

  useEffect(() => {
    if (tenantId && userRole) {
      loadDashboardData();
      fetchAnonymizedCitizensData();
    } else if (tenantId && !userRole) {
      // If we have tenantId but no userRole yet, keep loading
      console.log('⏳ Waiting for user role...');
    } else if (!tenantId) {
      // If no tenantId, stop loading (error should already be set)
      setLoading(false);
    }
  }, [tenantId, userRole]);

  const transformApiDataToReports = (apiData) => {
    if (!apiData) return [];

    // Handle different data structures based on tenant type
    let reports = [];

    if (apiData.population_by_kebele) {
      // Subcity or City level data
      reports = apiData.population_by_kebele.map((kebele, index) => ({
        id: index + 1,
        kebele: kebele.name || `Kebele ${index + 1}`,
        totalCitizens: kebele.population || 0,  // Backend uses 'population'
        maleCount: kebele.male || 0,            // Backend uses 'male'
        femaleCount: kebele.female || 0,        // Backend uses 'female'
        idCardsIssued: kebele.id_cards_issued || 0,
        pendingCards: kebele.pending_cards || 0,
        completionRate: kebele.completion_rate || 0,
        status: kebele.status || 'active'
      }));
    } else if (apiData.population_by_subcity) {
      // City level data showing subcities
      reports = apiData.population_by_subcity.map((subcity, index) => ({
        id: index + 1,
        kebele: subcity.name || `Subcity ${index + 1}`,
        totalCitizens: subcity.population || 0,  // Backend uses 'population' for subcities
        maleCount: subcity.male || 0,            // Backend uses 'male' for subcities
        femaleCount: subcity.female || 0,        // Backend uses 'female' for subcities
        idCardsIssued: subcity.id_cards_issued || 0,
        pendingCards: subcity.pending_cards || 0,
        completionRate: subcity.completion_rate || 0,
        status: subcity.status || 'active'
      }));
    } else if (apiData.population_by_ketena) {
      // Kebele level data showing ketenas
      reports = apiData.population_by_ketena.map((ketena, index) => ({
        id: index + 1,
        kebele: ketena.name || `Ketena ${index + 1}`,
        totalCitizens: ketena.total_citizens || 0,
        maleCount: ketena.male_count || 0,
        femaleCount: ketena.female_count || 0,
        idCardsIssued: ketena.id_cards_issued || 0,
        pendingCards: ketena.pending_cards || 0,
        completionRate: ketena.completion_rate || 0,
        status: ketena.status || 'active'
      }));
    }

    return reports;
  };

  const loadDashboardData = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Loading dashboard data for tenant:', tenantId, 'with role:', userRole);

      // Fetch demographic data from the appropriate API endpoint based on user role
      let apiData;
      if (userRole === 'city_admin') {
        console.log('🏙️ Fetching city-level data (child subcities)');
        apiData = await reportsService.getCityDemographicData(tenantId);
      } else if (userRole === 'subcity_admin') {
        console.log('🏘️ Fetching subcity-level data (child kebeles)');
        apiData = await reportsService.getSubcityDemographicData(tenantId);
      } else {
        console.log('🔍 Fetching default demographic data');
        apiData = await reportsService.getDemographicData(tenantId);
      }

      if (!apiData || apiData.error) {
        // If no data or error, show placeholder
        setDashboardData({
          totalCitizens: { count: 0, change: 0, trend: 'up' },
          totalIdCards: { count: 0, change: 0, trend: 'up' },
          completionRate: { pending: 0, processing: 0, completed: 0 },
          reports: [],
          rawData: null
        });
        setError(apiData?.error || 'No demographic data available for this tenant. This could be due to:\n• No citizens registered in this tenant\n• Multi-tenant access restrictions\n• Database connectivity issues\n\nPlease contact your system administrator if this issue persists.');
        return;
      }

      // Transform API data to dashboard format
      const reports = transformApiDataToReports(apiData);

      const dashboardMetrics = {
        totalCitizens: {
          count: apiData.total_citizens || apiData.total_citizens_18_plus || 0,  // Handle both field names
          change: apiData.new_registrations_this_month || 0,
          trend: 'up'
        },
        totalIdCards: {
          count: apiData.id_status_summary?.reduce((sum, status) => sum + (status.count || 0), 0) || 0,
          change: Math.round(((apiData.new_registrations_this_month || 0) / Math.max(1, apiData.total_citizens || apiData.total_citizens_18_plus || 1)) * 100),
          trend: 'up'
        },
        completionRate: {
          pending: apiData.id_status_summary?.find(s => s.status === 'PENDING')?.count || 0,
          processing: apiData.id_status_summary?.find(s => s.status === 'PROCESSING')?.count || 0,
          completed: apiData.id_status_summary?.find(s => s.status === 'COMPLETED')?.count || 0
        },
        reports: reports,
        rawData: apiData // Store for exports
      };

      setDashboardData(dashboardMetrics);
    } catch (err) {
      console.error('Dashboard data loading error:', err);

      let errorMessage = 'Failed to load demographic data. Please try again later.';
      if (err.response?.status === 500) {
        errorMessage = 'Server error occurred. This may be due to multi-tenant access restrictions or database issues. Please contact your system administrator.';
      } else if (err.response?.status === 403) {
        errorMessage = 'Access denied. You may not have permission to view this tenant\'s data.';
      } else if (err.response?.status === 404) {
        errorMessage = 'Tenant not found or no data available for this tenant.';
      }

      setError(errorMessage);

      // Set empty state on error
      setDashboardData({
        totalCitizens: { count: 0, change: 0, trend: 'up' },
        totalIdCards: { count: 0, change: 0, trend: 'up' },
        completionRate: { pending: 0, processing: 0, completed: 0 },
        reports: [],
        rawData: null
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch anonymized citizens data for research export
  const fetchAnonymizedCitizensData = async () => {
    console.log('🚀 fetchAnonymizedCitizensData CALLED!');
    console.log('🔍 Current state - tenantId:', tenantId, 'userRole:', userRole);
    try {
      console.log('🔍 Fetching anonymized citizens data for tenant:', tenantId, 'with role:', userRole);

      // Use role-specific endpoint for anonymized data
      let response;
      if (userRole === 'city_admin') {
        console.log('🏙️ Fetching city-level anonymized data (from child subcities)');
        response = await reportsService.getCityAnonymizedCitizensData(tenantId);
      } else if (userRole === 'subcity_admin') {
        console.log('🏘️ Fetching subcity-level anonymized data (from child kebeles)');
        response = await reportsService.getSubcityAnonymizedCitizensData(tenantId);
      } else {
        console.log('🔍 Fetching default anonymized data');
        response = await reportsService.getAnonymizedCitizensData(tenantId);
      }

      if (response && response.length > 0) {
        setDashboardData(prev => ({
          ...prev,
          anonymizedCitizens: response
        }));
        console.log('✅ Anonymized citizens data loaded:', response.length, 'records');
      } else {
        console.log('⚠️ No anonymized citizens data available');
        setDashboardData(prev => ({
          ...prev,
          anonymizedCitizens: []
        }));
      }
    } catch (error) {
      console.error('❌ Error fetching anonymized citizens data:', error);
      setDashboardData(prev => ({
        ...prev,
        anonymizedCitizens: []
      }));
    }
  };

  const generateSampleData = () => {
    const kebeles = [
      'Kebele 01', 'Kebele 02', 'Kebele 03', 'Kebele 04', 'Kebele 05',
      'Kebele 06', 'Kebele 07', 'Kebele 08', 'Kebele 09', 'Kebele 10'
    ];

    const reports = kebeles.map((kebele, index) => ({
      id: index + 1,
      kebele: kebele,
      totalCitizens: Math.floor(Math.random() * 500) + 100,
      maleCount: Math.floor(Math.random() * 250) + 50,
      femaleCount: Math.floor(Math.random() * 250) + 50,
      idCardsIssued: Math.floor(Math.random() * 400) + 80,
      pendingCards: Math.floor(Math.random() * 50) + 5,
      registrationDate: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString(),
      completionRate: Math.floor(Math.random() * 30) + 70, // 70-100%
      status: Math.random() > 0.3 ? 'active' : 'pending'
    }));

    const totalCitizens = reports.reduce((sum, report) => sum + report.totalCitizens, 0);
    const totalIdCards = reports.reduce((sum, report) => sum + report.idCardsIssued, 0);
    const totalPending = reports.reduce((sum, report) => sum + report.pendingCards, 0);

    return {
      totalCitizens: {
        count: totalCitizens,
        change: 12.5,
        trend: 'up'
      },
      totalIdCards: {
        count: totalIdCards,
        change: 8.3,
        trend: 'up'
      },
      completionRate: {
        pending: Math.round((totalPending / totalCitizens) * 100),
        processing: 15,
        completed: Math.round(((totalIdCards - totalPending) / totalCitizens) * 100)
      },
      reports: reports
    };
  };

  const handleExportPDF = async () => {
    if (!dashboardData.rawData || !tenantId) {
      setError('No data available for export');
      return;
    }

    try {
      setLoading(true);
      await reportsService.exportDemographicPDF(tenantId, dashboardData.rawData);
    } catch (err) {
      console.error('PDF export failed:', err);
      setError('Failed to export PDF. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = async () => {
    if (!dashboardData.rawData || !tenantId) {
      setError('No data available for export');
      return;
    }

    try {
      setLoading(true);
      await reportsService.exportDemographicExcel(tenantId, dashboardData.rawData);
    } catch (err) {
      console.error('Excel export failed:', err);
      setError('Failed to export Excel. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Export anonymized citizens data for research
  const handleExportAnonymizedData = async (format) => {
    if (!dashboardData.anonymizedCitizens?.length || !tenantId) {
      setError('No anonymized citizen data available for export');
      return;
    }

    try {
      setLoading(true);
      if (format === 'pdf') {
        await reportsService.exportAnonymizedCitizensPDF(tenantId, dashboardData.anonymizedCitizens);
      } else if (format === 'excel') {
        await reportsService.exportAnonymizedCitizensExcel(tenantId, dashboardData.anonymizedCitizens);
      }
    } catch (err) {
      console.error(`${format.toUpperCase()} export failed:`, err);
      setError(`Failed to export ${format.toUpperCase()}. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const filteredReports = dashboardData.reports.filter(report =>
    report.kebele.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const itemsPerPage = 7;
  const totalPages = Math.ceil(filteredReports.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedReports = filteredReports.slice(startIndex, startIndex + itemsPerPage);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tab Navigation */}
      <Paper sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', mr: 3 }}>
                {userRole === 'city_admin' ? 'City Reports & Analytics' :
                 userRole === 'subcity_admin' ? 'Subcity Reports & Analytics' :
                 'Data Reports & Analytics'}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {[
                  'Overview',
                  'Citizens Demographics',
                  'ID Cards Status',
                  userRole === 'city_admin' ? 'Subcity Distribution' : 'Geographic Distribution'
                ].map((tab, index) => (
                  <Button
                    key={tab}
                    variant={activeTab === index ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setActiveTab(index)}
                    sx={{
                      minWidth: 'auto',
                      fontSize: '0.875rem',
                      ...(activeTab === index && {
                        backgroundColor: '#1976d2',
                        color: 'white'
                      })
                    }}
                  >
                    {tab}
                  </Button>
                ))}
              </Box>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<PdfIcon />}
                onClick={handleExportPDF}
                disabled={loading || !dashboardData.rawData}
                size="small"
              >
                Export PDF
              </Button>
              <Button
                variant="outlined"
                startIcon={<ExcelIcon />}
                onClick={handleExportExcel}
                disabled={loading || !dashboardData.rawData}
                size="small"
              >
                Export Excel
              </Button>
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Tab Content */}
      {activeTab === 0 && (
        <>
          {/* Overview Tab - Top Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Citizens */}
        <Grid item xs={12} md={4}>
          <Card sx={{ p: 2, height: '140px' }}>
            <CardContent sx={{ p: 0 }}>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Total Citizens
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h3" sx={{ fontWeight: 'bold', mr: 2 }}>
                  {dashboardData.totalCitizens.count.toLocaleString()}
                </Typography>
                <Box sx={{ position: 'relative', width: 60, height: 60 }}>
                  <CircularProgress
                    variant="determinate"
                    value={85}
                    size={60}
                    thickness={6}
                    sx={{ color: '#4caf50' }}
                  />
                </Box>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ color: '#4caf50', fontSize: 16, mr: 0.5 }} />
                <Typography variant="body2" color="#4caf50">
                  {dashboardData.totalCitizens.change}% vs last month
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Total ID Cards */}
        <Grid item xs={12} md={4}>
          <Card sx={{ p: 2, height: '140px' }}>
            <CardContent sx={{ p: 0 }}>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                ID Cards Issued
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h3" sx={{ fontWeight: 'bold', mr: 2 }}>
                  {dashboardData.totalIdCards.count.toLocaleString()}
                </Typography>
                <Box sx={{ width: 60, height: 60, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 8, height: 40, backgroundColor: '#2196f3', borderRadius: 1 }} />
                </Box>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ color: '#4caf50', fontSize: 16, mr: 0.5 }} />
                <Typography variant="body2" color="#4caf50">
                  {dashboardData.totalIdCards.change}% vs last month
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Completion Rate */}
        <Grid item xs={12} md={4}>
          <Card sx={{ p: 2, height: '140px' }}>
            <CardContent sx={{ p: 0 }}>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                ID Card Status
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{
                    width: '100%',
                    height: 8,
                    backgroundColor: '#e0e0e0',
                    borderRadius: 4,
                    overflow: 'hidden',
                    display: 'flex'
                  }}>
                    <Box sx={{
                      width: `${dashboardData.completionRate.pending}%`,
                      backgroundColor: '#f44336'
                    }} />
                    <Box sx={{
                      width: `${dashboardData.completionRate.processing}%`,
                      backgroundColor: '#ff9800'
                    }} />
                    <Box sx={{
                      width: `${dashboardData.completionRate.completed}%`,
                      backgroundColor: '#4caf50'
                    }} />
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
                  <Typography variant="caption" color="#f44336">
                    {dashboardData.completionRate.pending}% Pending
                  </Typography>
                  <Typography variant="caption" color="#ff9800">
                    {dashboardData.completionRate.processing}% Processing
                  </Typography>
                  <Typography variant="caption" color="#4caf50">
                    {dashboardData.completionRate.completed}% Completed
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Controls Bar */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={staffFilter}
              onChange={(e) => setStaffFilter(e.target.value)}
              displayEmpty
            >
              <MenuItem value="All Kebeles">All Kebeles</MenuItem>
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Pending">Pending</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={actionFilter}
              onChange={(e) => setActionFilter(e.target.value)}
              displayEmpty
            >
              <MenuItem value="All Status">All Status</MenuItem>
              <MenuItem value="High Performance">High Performance</MenuItem>
              <MenuItem value="Low Performance">Low Performance</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ width: 250 }}
          />


        </Box>
      </Box>

      {/* Reports Table */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell sx={{ fontWeight: 'bold', color: '#666' }}>Kebele/Subcity</TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#666' }}>Total Citizens</TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#666' }}>Male/Female</TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#666' }}>ID Cards Issued</TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#666' }}>Completion Rate</TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#666' }}>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedReports.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                      <Typography variant="h6" color="textSecondary">
                        No Demographic Data Available
                      </Typography>
                      <Typography variant="body2" color="textSecondary" sx={{ maxWidth: 400, textAlign: 'center' }}>
                        {error || 'There is currently no citizen registration or ID card data available for this administrative unit. Data will appear here once citizens are registered and ID cards are processed.'}
                      </Typography>
                      {error && (
                        <Button
                          variant="outlined"
                          onClick={loadDashboardData}
                          disabled={loading}
                          sx={{ mt: 1 }}
                        >
                          Retry Loading Data
                        </Button>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedReports.map((report, index) => (
                <TableRow key={report.id} sx={{ '&:hover': { backgroundColor: '#f9f9f9' } }}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ width: 32, height: 32, fontSize: '14px', backgroundColor: '#e3f2fd' }}>
                        {report.kebele.substring(0, 2).toUpperCase()}
                      </Avatar>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {report.kebele}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {report.totalCitizens.toLocaleString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="body2" color="textSecondary" sx={{ fontSize: '12px' }}>
                        Male: {report.maleCount}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" sx={{ fontSize: '12px' }}>
                        Female: {report.femaleCount}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {report.idCardsIssued}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" sx={{ fontSize: '12px' }}>
                        Pending: {report.pendingCards}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {report.completionRate}%
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={report.completionRate}
                        sx={{
                          width: 60,
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: '#e0e0e0',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: report.completionRate >= 80 ? '#4caf50' : report.completionRate >= 60 ? '#ff9800' : '#f44336'
                          }
                        }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={report.status === 'active' ? 'Active' : 'Pending'}
                        size="small"
                        variant="outlined"
                        color={report.status === 'active' ? 'success' : 'warning'}
                      />
                      <IconButton size="small">
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(event, value) => setCurrentPage(value)}
            color="primary"
            showFirstButton
            showLastButton
          />
        </Box>
      </Paper>
        </>
      )}

      {/* Citizens Demographics Tab */}
      {activeTab === 1 && (
        <Grid container spacing={3}>
          {/* Summary Cards */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Gender Distribution
              </Typography>
              {dashboardData.rawData?.gender_ratio ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="body1">
                      Male: {dashboardData.rawData.gender_ratio.male || 0}
                    </Typography>
                    <Typography variant="body1">
                      Female: {dashboardData.rawData.gender_ratio.female || 0}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={(dashboardData.rawData.gender_ratio.male / (dashboardData.rawData.gender_ratio.male + dashboardData.rawData.gender_ratio.female)) * 100}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No gender data available
                </Typography>
              )}
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Age Groups
              </Typography>
              {dashboardData.rawData?.age_group_distribution ? (
                <Box>
                  {dashboardData.rawData.age_group_distribution.slice(0, 3).map((group, index) => (
                    <Box key={index} sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">{group.age_range}</Typography>
                        <Typography variant="body2">{group.count}</Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No age data available
                </Typography>
              )}
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Migration Summary
              </Typography>
              {dashboardData.rawData?.migration_data ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">In-Migration:</Typography>
                    <Typography variant="body2">{dashboardData.rawData.migration_data.in_migration || 0}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Out-Migration:</Typography>
                    <Typography variant="body2">{dashboardData.rawData.migration_data.out_migration || 0}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Net Migration:</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {(dashboardData.rawData.migration_data.in_migration || 0) - (dashboardData.rawData.migration_data.out_migration || 0)}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No migration data available
                </Typography>
              )}
            </Paper>
          </Grid>

          {/* Anonymized Citizens Data Table */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Anonymized Citizens Data (Research Export)
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<PdfIcon />}
                    onClick={() => handleExportAnonymizedData('pdf')}
                    disabled={loading || !dashboardData.anonymizedCitizens?.length}
                  >
                    Export PDF
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<ExcelIcon />}
                    onClick={() => handleExportAnonymizedData('excel')}
                    disabled={loading || !dashboardData.anonymizedCitizens?.length}
                  >
                    Export Excel
                  </Button>
                </Box>
              </Box>

              {dashboardData.anonymizedCitizens?.length > 0 ? (
                <TableContainer sx={{ maxHeight: 400 }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell>Record ID</TableCell>
                        <TableCell>Age Group</TableCell>
                        <TableCell>Gender</TableCell>
                        <TableCell>Marital Status</TableCell>
                        <TableCell>Employment</TableCell>
                        <TableCell>Blood Group</TableCell>
                        <TableCell>Disability</TableCell>
                        <TableCell>Ketena</TableCell>
                        <TableCell>Kebele</TableCell>
                        <TableCell>ID Card Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboardData.anonymizedCitizens.map((citizen, index) => (
                        <TableRow key={index} hover>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {citizen.record_id || `REC-${String(index + 1).padStart(6, '0')}`}
                            </Typography>
                          </TableCell>
                          <TableCell>{citizen.age_group || 'N/A'}</TableCell>
                          <TableCell>
                            <Chip
                              label={citizen.gender || 'N/A'}
                              size="small"
                              color={citizen.gender === 'male' ? 'primary' : citizen.gender === 'female' ? 'secondary' : 'default'}
                            />
                          </TableCell>
                          <TableCell>{citizen.marital_status || 'N/A'}</TableCell>
                          <TableCell>{citizen.employment_status || 'N/A'}</TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {citizen.blood_group || 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={citizen.disability_status || 'None'}
                              size="small"
                              color={citizen.disability_status === 'None' ? 'default' : 'warning'}
                            />
                          </TableCell>
                          <TableCell>{citizen.ketena || 'N/A'}</TableCell>
                          <TableCell>{citizen.kebele || 'N/A'}</TableCell>
                          <TableCell>
                            <Chip
                              label={citizen.id_card_status || 'Not Issued'}
                              size="small"
                              color={
                                citizen.id_card_status === 'Issued' ? 'success' :
                                citizen.id_card_status === 'Approved' ? 'primary' :
                                citizen.id_card_status === 'Pending Approval' ? 'warning' : 'default'
                              }
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="textSecondary" gutterBottom>
                    No anonymized citizen data available
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    This could be because there are no citizens registered in this area, or the data is still being processed.
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => window.location.reload()}
                    sx={{ mt: 2 }}
                  >
                    Retry Loading Data
                  </Button>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* ID Cards Status Tab */}
      {activeTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                ID Card Status Summary
              </Typography>
              {dashboardData.rawData?.id_status_summary ? (
                <Grid container spacing={2}>
                  {dashboardData.rawData.id_status_summary.map((status, index) => (
                    <Grid item xs={12} md={3} key={index}>
                      <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
                        <Typography variant="h4" color="primary">
                          {status.count || 0}
                        </Typography>
                        <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                          {status.status?.replace('_', ' ') || 'Unknown'}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No ID card status data available
                </Typography>
              )}
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Monthly Trends
              </Typography>
              {dashboardData.rawData?.monthly_trends ? (
                <Box>
                  {dashboardData.rawData.monthly_trends.map((trend, index) => (
                    <Box key={index} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{trend.month}</Typography>
                        <Typography variant="body2">{trend.registrations} registrations</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={(trend.registrations / Math.max(...dashboardData.rawData.monthly_trends.map(t => t.registrations))) * 100}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No monthly trend data available
                </Typography>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Geographic Distribution Tab */}
      {activeTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                {userRole === 'city_admin' ? 'Population by Subcity' : 'Population by Area'}
              </Typography>
              {dashboardData.rawData?.population_by_subcity || dashboardData.rawData?.population_by_ketena || dashboardData.rawData?.population_by_kebele ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>{userRole === 'city_admin' ? 'Subcity Name' : 'Area Name'}</TableCell>
                        <TableCell>Total Citizens</TableCell>
                        <TableCell>Male</TableCell>
                        <TableCell>Female</TableCell>
                        <TableCell>ID Cards Issued</TableCell>
                        <TableCell>Completion Rate</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {(dashboardData.rawData?.population_by_subcity || dashboardData.rawData?.population_by_ketena || dashboardData.rawData?.population_by_kebele || []).map((area, index) => (
                        <TableRow key={index}>
                          <TableCell>{area.name || `Area ${index + 1}`}</TableCell>
                          <TableCell>{area.population || area.total_citizens || 0}</TableCell>
                          <TableCell>{area.male || area.male_count || 0}</TableCell>
                          <TableCell>{area.female || area.female_count || 0}</TableCell>
                          <TableCell>{area.id_cards_issued || 0}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2">
                                {area.completion_rate || 0}%
                              </Typography>
                              <LinearProgress
                                variant="determinate"
                                value={area.completion_rate || 0}
                                sx={{ width: 60, height: 4 }}
                              />
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No geographic distribution data available
                </Typography>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ReportsAnalyticsDashboard;
