#!/usr/bin/env python3
"""
GoID Biometric Service Installer Builder
Creates a distributable installer package for client machines
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
import subprocess

class InstallerBuilder:
    def __init__(self):
        self.source_dir = Path(__file__).parent
        self.build_dir = self.source_dir / "build"
        self.dist_dir = self.source_dir / "dist"
        self.package_name = "GoID-Biometric-Service-Installer"
        
    def clean_build_dirs(self):
        """Clean build and dist directories"""
        for dir_path in [self.build_dir, self.dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def copy_source_files(self):
        """Copy source files to build directory"""
        files_to_copy = [
            'biometric_tray_service.py',
            'enhanced_capture_service.py',
            'install_service.py',
            'config.json',
            'requirements.txt',
            'README.md'
        ]
        
        print("📁 Copying source files...")
        for file in files_to_copy:
            if (self.source_dir / file).exists():
                shutil.copy2(self.source_dir / file, self.build_dir / file)
                print(f"   ✅ {file}")
            else:
                print(f"   ⚠️  {file} not found")
    
    def copy_biometric_sdk(self):
        """Copy biometric SDK files"""
        # Copy from main local-biometric-service directory
        main_biometric_dir = self.source_dir.parent.parent / "local-biometric-service"
        fingerprint_dir = main_biometric_dir / "fingerPrint"
        
        if fingerprint_dir.exists():
            print("📁 Copying biometric SDK files...")
            shutil.copytree(fingerprint_dir, self.build_dir / "fingerPrint", 
                          dirs_exist_ok=True)
            print("   ✅ fingerPrint directory copied")
        else:
            print("   ⚠️  fingerPrint directory not found in main project")
            # Create placeholder
            (self.build_dir / "fingerPrint").mkdir(exist_ok=True)
            with open(self.build_dir / "fingerPrint" / "README.txt", 'w') as f:
                f.write("""
GoID Biometric SDK Files

This directory should contain:
- GonderFingerPrint.jar (main biometric capture JAR)
- Any additional SDK files required for device operation

Please copy these files from your biometric SDK installation.
""")
    
    def create_batch_installer(self):
        """Create Windows batch installer"""
        batch_content = '''@echo off
echo ========================================
echo GoID Biometric Service Installer
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.10 or higher from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

REM Check if Java is installed
java -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java is not installed or not in PATH
    echo Please install Java Runtime Environment (JRE) 8 or higher
    echo Download from: https://java.com/download
    pause
    exit /b 1
)

echo [INFO] Starting installation...
python install_service.py

echo.
echo Installation completed!
pause
'''
        
        with open(self.build_dir / "install.bat", 'w') as f:
            f.write(batch_content)
        print("   ✅ install.bat created")
    
    def create_manual_start_scripts(self):
        """Create manual start scripts"""
        # Windows batch file
        start_bat_content = '''@echo off
echo Starting GoID Biometric Service...
cd /d "%~dp0"
python biometric_tray_service.py
pause
'''
        
        with open(self.build_dir / "start_service_manual.bat", 'w') as f:
            f.write(start_bat_content)
        
        # Python script for cross-platform
        start_py_content = '''#!/usr/bin/env python3
"""
Manual start script for GoID Biometric Service
Use this if the automatic installer doesn't work
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from biometric_tray_service import main
    main()
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install requirements: pip install -r requirements.txt")
except Exception as e:
    print(f"Error starting service: {e}")
    input("Press Enter to exit...")
'''
        
        with open(self.build_dir / "start_service_manual.py", 'w') as f:
            f.write(start_py_content)
        
        print("   ✅ Manual start scripts created")
    
    def create_documentation(self):
        """Create installation and usage documentation"""
        install_guide = '''# GoID Biometric Service Installation Guide

## Quick Installation

1. **Run the installer:**
   - Double-click `install.bat`
   - Follow the installation wizard
   - The service will be installed and configured automatically

2. **Manual installation (if automatic fails):**
   - Install Python 3.10+ from https://python.org
   - Install Java Runtime Environment from https://java.com
   - Run: `pip install -r requirements.txt`
   - Run: `python install_service.py`

## Requirements

### Software
- Windows 10 or later
- Python 3.10 or higher
- Java Runtime Environment (JRE) 8 or higher

### Hardware
- Futronic FS88H fingerprint scanner
- USB port for device connection

## Configuration

Edit `config.json` to configure:
- Server URL (your GoID production server)
- Client information (kebele name, client ID)
- Service settings (port, timeouts)

## Usage

### Automatic Start
- Service starts automatically with Windows
- Look for the fingerprint icon in system tray

### Manual Start
- Double-click desktop shortcut "GoID Biometric Service"
- Or run `start_service_manual.bat`

### System Tray
Right-click the tray icon for options:
- View status
- Configure settings
- Start/stop service
- Open web interface

## Troubleshooting

### Service Won't Start
1. Check Python installation: `python --version`
2. Check Java installation: `java -version`
3. Verify device is connected via USB
4. Check Windows firewall settings

### Device Not Detected
1. Reconnect USB device
2. Install device drivers if prompted
3. Try different USB port
4. Check Device Manager for errors

### Network Issues
1. Verify server URL in config.json
2. Check firewall allows port 8002
3. Test network connectivity to server

## Support

For technical support:
1. Check service logs in installation directory
2. Use "Status" option in system tray menu
3. Contact system administrator with error details

## Uninstallation

Run `python uninstall.py` from installation directory
Or use Windows "Add or Remove Programs"
'''
        
        with open(self.build_dir / "INSTALLATION_GUIDE.md", 'w') as f:
            f.write(install_guide)
        
        print("   ✅ Installation guide created")
    
    def create_zip_package(self):
        """Create ZIP package for distribution"""
        zip_path = self.dist_dir / f"{self.package_name}.zip"
        
        print(f"📦 Creating ZIP package: {zip_path}")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(self.build_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(self.build_dir)
                    zipf.write(file_path, arc_path)
                    print(f"   📄 {arc_path}")
        
        print(f"✅ Package created: {zip_path}")
        return zip_path
    
    def create_executable_installer(self):
        """Create executable installer using PyInstaller (optional)"""
        try:
            print("🔧 Creating executable installer...")
            
            # Create PyInstaller spec file
            spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['install_service.py'],
    pathex=['{self.build_dir}'],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('fingerPrint', 'fingerPrint'),
    ],
    hiddenimports=[
        'tkinter',
        'winshell',
        'pystray',
        'PIL',
        'flask',
        'requests'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GoID-Biometric-Service-Installer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
            
            spec_file = self.build_dir / "installer.spec"
            with open(spec_file, 'w') as f:
                f.write(spec_content)
            
            # Run PyInstaller
            result = subprocess.run([
                sys.executable, '-m', 'PyInstaller',
                '--clean', '--onefile',
                str(spec_file)
            ], cwd=self.build_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Move executable to dist directory
                exe_source = self.build_dir / "dist" / "GoID-Biometric-Service-Installer.exe"
                exe_dest = self.dist_dir / "GoID-Biometric-Service-Installer.exe"
                
                if exe_source.exists():
                    shutil.move(exe_source, exe_dest)
                    print(f"✅ Executable installer created: {exe_dest}")
                    return exe_dest
            else:
                print(f"⚠️  PyInstaller failed: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"⚠️  Could not create executable installer: {e}")
            return None
    
    def build(self):
        """Build the complete installer package"""
        print("🚀 Building GoID Biometric Service Installer")
        print("=" * 50)
        
        try:
            # Clean build directories
            print("🧹 Cleaning build directories...")
            self.clean_build_dirs()
            
            # Copy source files
            self.copy_source_files()
            
            # Copy biometric SDK
            self.copy_biometric_sdk()
            
            # Create installer scripts
            print("📝 Creating installer scripts...")
            self.create_batch_installer()
            self.create_manual_start_scripts()
            
            # Create documentation
            print("📚 Creating documentation...")
            self.create_documentation()
            
            # Create ZIP package
            zip_package = self.create_zip_package()
            
            # Try to create executable installer
            exe_package = self.create_executable_installer()
            
            print("\n" + "=" * 50)
            print("✅ Build completed successfully!")
            print(f"📦 ZIP Package: {zip_package}")
            if exe_package:
                print(f"💿 Executable: {exe_package}")
            
            print("\nDistribution packages are ready in the 'dist' directory")
            
        except Exception as e:
            print(f"❌ Build failed: {e}")
            raise

def main():
    """Main entry point"""
    builder = InstallerBuilder()
    builder.build()

if __name__ == '__main__':
    main()
