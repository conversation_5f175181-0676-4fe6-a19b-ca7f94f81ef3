import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Tabs,
  Tab,
  Grid,
  Paper,
  IconButton,
  CircularProgress,
  Alert,
  InputAdornment
} from '@mui/material';
import {
  Description as FileTextIcon,
  Download as DownloadIcon,
  Add as PlusIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  CalendarToday as CalendarIcon,
  Schedule as ClockIcon,
  Person as UserIcon,
  BarChart as BarChartIcon,
  TableChart as FileSpreadsheetIcon,
  Code as FileJsonIcon,
  Delete as TrashIcon,
  Visibility as EyeIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import reportsService from '../../services/reportsService';

const ReportsDashboard = () => {
  const [reports, setReports] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [schedules, setSchedules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [formatFilter, setFormatFilter] = useState('');
  const [showGenerationModal, setShowGenerationModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [activeTab, setActiveTab] = useState('reports');
  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [reportsData, templatesData, schedulesData] = await Promise.all([
        reportsService.getReports(),
        reportsService.getReportTemplates(),
        reportsService.getReportSchedules()
      ]);
      
      setReports(reportsData.results || reportsData);
      setTemplates(templatesData.results || templatesData);
      setSchedules(schedulesData.results || schedulesData);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load reports data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async (reportData) => {
    try {
      const newReport = await reportsService.generateReport(reportData);
      setReports(prev => [newReport, ...prev]);
      setShowGenerationModal(false);
      
      toast({
        title: "Success",
        description: "Report generation started successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate report",
        variant: "destructive"
      });
    }
  };

  const handleDownloadReport = async (reportId) => {
    try {
      await reportsService.downloadReport(reportId);
      toast({
        title: "Success",
        description: "Report downloaded successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download report",
        variant: "destructive"
      });
    }
  };

  const handleDeleteReport = async (reportId) => {
    if (!window.confirm('Are you sure you want to delete this report?')) {
      return;
    }

    try {
      await reportsService.deleteReport(reportId);
      setReports(prev => prev.filter(report => report.id !== reportId));
      
      toast({
        title: "Success",
        description: "Report deleted successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete report",
        variant: "destructive"
      });
    }
  };

  const handleViewReport = (report) => {
    setSelectedReport(report);
    setShowDetailsModal(true);
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || report.status === statusFilter;
    const matchesType = !typeFilter || report.report_type === typeFilter;
    const matchesFormat = !formatFilter || report.format === formatFilter;
    
    return matchesSearch && matchesStatus && matchesType && matchesFormat;
  });

  const getStatusBadge = (status) => {
    const variants = {
      'pending': 'secondary',
      'processing': 'default',
      'completed': 'success',
      'failed': 'destructive',
      'expired': 'outline'
    };
    
    return (
      <Badge variant={variants[status] || 'outline'}>
        {reportsService.getStatusIcon(status)} {status.toUpperCase()}
      </Badge>
    );
  };

  const getFormatIcon = (format) => {
    const icons = {
      'pdf': <FileText className="h-4 w-4" />,
      'excel': <FileSpreadsheet className="h-4 w-4" />,
      'csv': <BarChart3 className="h-4 w-4" />,
      'json': <FileJson className="h-4 w-4" />
    };
    return icons[format] || <FileText className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading reports...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Data Reports</h1>
          <p className="text-muted-foreground">
            Generate and manage analytical reports for your organization
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowScheduleModal(true)} variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Report
          </Button>
          <Button onClick={() => setShowGenerationModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="schedules">Schedules</TabsTrigger>
        </TabsList>

        <TabsContent value="reports" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search reports..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Report Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    <SelectItem value="demographic">Demographic</SelectItem>
                    <SelectItem value="registration_trends">Registration Trends</SelectItem>
                    <SelectItem value="id_card_status">ID Card Status</SelectItem>
                    <SelectItem value="service_access">Service Access</SelectItem>
                    <SelectItem value="migration_analysis">Migration Analysis</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={formatFilter} onValueChange={setFormatFilter}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Formats</SelectItem>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
                <Button 
                  variant="outline" 
                  onClick={loadData}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Reports List */}
          <div className="grid gap-4">
            {filteredReports.length === 0 ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No reports found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm || statusFilter || typeFilter || formatFilter
                      ? 'No reports match your current filters'
                      : 'Generate your first report to get started'
                    }
                  </p>
                  <Button onClick={() => setShowGenerationModal(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredReports.map((report) => (
                <Card key={report.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          {getFormatIcon(report.format)}
                          <h3 className="text-lg font-semibold">{report.title}</h3>
                          {getStatusBadge(report.status)}
                        </div>
                        
                        {report.description && (
                          <p className="text-muted-foreground mb-3">{report.description}</p>
                        )}
                        
                        <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-4 w-4" />
                            {report.report_type_display}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            {report.generated_by_username}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {format(new Date(report.created_at), 'MMM dd, yyyy')}
                          </div>
                          {report.processing_time_seconds && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {reportsService.formatProcessingTime(report.processing_time_seconds)}
                            </div>
                          )}
                          {report.file_size && (
                            <div className="flex items-center gap-1">
                              <FileText className="h-4 w-4" />
                              {reportsService.formatFileSize(report.file_size)}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewReport(report)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        
                        {report.is_downloadable && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownloadReport(report.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteReport(report.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="templates">
          <div className="text-center py-8">
            <h3 className="text-lg font-semibold mb-2">Report Templates</h3>
            <p className="text-muted-foreground">Template management coming soon...</p>
          </div>
        </TabsContent>

        <TabsContent value="schedules">
          <div className="text-center py-8">
            <h3 className="text-lg font-semibold mb-2">Scheduled Reports</h3>
            <p className="text-muted-foreground">Schedule management coming soon...</p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {showGenerationModal && (
        <ReportGenerationModal
          isOpen={showGenerationModal}
          onClose={() => setShowGenerationModal(false)}
          onGenerate={handleGenerateReport}
          templates={templates}
        />
      )}

      {showDetailsModal && selectedReport && (
        <ReportDetailsModal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          report={selectedReport}
          onDownload={() => handleDownloadReport(selectedReport.id)}
        />
      )}

      {showScheduleModal && (
        <ReportScheduleModal
          isOpen={showScheduleModal}
          onClose={() => setShowScheduleModal(false)}
          templates={templates}
        />
      )}
    </div>
  );
};

export default ReportsDashboard;
