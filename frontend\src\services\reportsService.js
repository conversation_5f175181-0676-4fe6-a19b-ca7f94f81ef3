import axios from '../utils/axios';

class ReportsService {
  constructor() {
    this.baseURL = '/api/reports';
  }

  // Report Templates
  async getReportTemplates() {
    try {
      const response = await axios.get(`${this.baseURL}/templates/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching report templates:', error);
      throw error;
    }
  }

  async createReportTemplate(templateData) {
    try {
      const response = await axios.post(`${this.baseURL}/templates/`, templateData);
      return response.data;
    } catch (error) {
      console.error('Error creating report template:', error);
      throw error;
    }
  }

  async updateReportTemplate(templateId, templateData) {
    try {
      const response = await axios.put(`${this.baseURL}/templates/${templateId}/`, templateData);
      return response.data;
    } catch (error) {
      console.error('Error updating report template:', error);
      throw error;
    }
  }

  async deleteReportTemplate(templateId) {
    try {
      await axios.delete(`${this.baseURL}/templates/${templateId}/`);
    } catch (error) {
      console.error('Error deleting report template:', error);
      throw error;
    }
  }

  // Reports
  async getReports(filters = {}) {
    try {
      const params = new URLSearchParams();
      Object.keys(filters).forEach(key => {
        if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });
      
      const response = await axios.get(`${this.baseURL}/reports/?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching reports:', error);
      throw error;
    }
  }

  async getReport(reportId) {
    try {
      const response = await axios.get(`${this.baseURL}/reports/${reportId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching report:', error);
      throw error;
    }
  }

  async generateReport(reportData) {
    try {
      const response = await axios.post(`${this.baseURL}/reports/generate/`, reportData);
      return response.data;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  async downloadReport(reportId) {
    try {
      const response = await axios.get(`${this.baseURL}/reports/${reportId}/download/`, {
        responseType: 'blob'
      });
      
      // Extract filename from Content-Disposition header
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'report';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true, filename };
    } catch (error) {
      console.error('Error downloading report:', error);
      throw error;
    }
  }

  async deleteReport(reportId) {
    try {
      await axios.delete(`${this.baseURL}/reports/${reportId}/`);
    } catch (error) {
      console.error('Error deleting report:', error);
      throw error;
    }
  }

  // Report Types and Formats
  async getReportTypes() {
    try {
      const response = await axios.get(`${this.baseURL}/reports/types/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching report types:', error);
      throw error;
    }
  }

  async getReportFormats() {
    try {
      const response = await axios.get(`${this.baseURL}/reports/formats/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching report formats:', error);
      throw error;
    }
  }

  // Report Schedules
  async getReportSchedules() {
    try {
      const response = await axios.get(`${this.baseURL}/schedules/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching report schedules:', error);
      throw error;
    }
  }

  async createReportSchedule(scheduleData) {
    try {
      const response = await axios.post(`${this.baseURL}/schedules/`, scheduleData);
      return response.data;
    } catch (error) {
      console.error('Error creating report schedule:', error);
      throw error;
    }
  }

  async updateReportSchedule(scheduleId, scheduleData) {
    try {
      const response = await axios.put(`${this.baseURL}/schedules/${scheduleId}/`, scheduleData);
      return response.data;
    } catch (error) {
      console.error('Error updating report schedule:', error);
      throw error;
    }
  }

  async deleteReportSchedule(scheduleId) {
    try {
      await axios.delete(`${this.baseURL}/schedules/${scheduleId}/`);
    } catch (error) {
      console.error('Error deleting report schedule:', error);
      throw error;
    }
  }

  // Demographic Data Methods
  async getDemographicData(tenantId) {
    try {
      console.log('📊 Fetching demographic data for tenant:', tenantId);
      console.log('🔄 Using the same working dashboard/reports endpoint as main dashboard');

      // Use the exact same endpoint that works in the main dashboard
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/dashboard/reports/`);

      console.log('✅ Demographic data received:', response.data);
      return response.data;
    } catch (error) {
      // If kebele endpoint fails, try subcity endpoint
      if (error.response?.status === 403 || error.response?.status === 404) {
        try {
          const response = await axios.get(`/api/tenants/${tenantId}/citizens/subcity-dashboard/reports/`);
          return response.data;
        } catch (subcityError) {
          // If subcity endpoint fails, try city endpoint
          if (subcityError.response?.status === 403 || subcityError.response?.status === 404) {
            try {
              const response = await axios.get(`/api/tenants/${tenantId}/citizens/city-dashboard/reports/`);
              return response.data;
            } catch (cityError) {
              console.error('All dashboard endpoints failed:', cityError);
              throw cityError;
            }
          }
          throw subcityError;
        }
      }
      throw error;
    }
  }

  async exportDemographicPDF(tenantId, data) {
    try {
      const response = await axios.post(`/api/tenants/${tenantId}/citizens/export-anonymized-pdf/`, {}, {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `anonymized-citizens-${new Date().toISOString().split('T')[0]}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { success: true, filename: `anonymized-citizens-${new Date().toISOString().split('T')[0]}.pdf` };
    } catch (error) {
      console.error('PDF export failed:', error);
      throw error;
    }
  }

  async exportDemographicExcel(tenantId, data) {
    try {
      const response = await axios.post(`/api/tenants/${tenantId}/citizens/export-anonymized-excel/`, {}, {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `anonymized-citizens-${new Date().toISOString().split('T')[0]}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { success: true, filename: `anonymized-citizens-${new Date().toISOString().split('T')[0]}.xlsx` };
    } catch (error) {
      console.error('Excel export failed:', error);
      throw error;
    }
  }

  // Utility methods
  formatFileSize(bytes) {
    if (!bytes) return 'N/A';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  formatProcessingTime(seconds) {
    if (!seconds) return 'N/A';
    
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)}m ${Math.round(seconds % 60)}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }

  getStatusColor(status) {
    const colors = {
      'pending': '#f59e0b',
      'processing': '#3b82f6',
      'completed': '#10b981',
      'failed': '#ef4444',
      'expired': '#6b7280'
    };
    return colors[status] || '#6b7280';
  }

  getStatusIcon(status) {
    const icons = {
      'pending': '⏳',
      'processing': '⚙️',
      'completed': '✅',
      'failed': '❌',
      'expired': '🗑️'
    };
    return icons[status] || '❓';
  }

  getFormatIcon(format) {
    const icons = {
      'pdf': '📄',
      'excel': '📊',
      'csv': '📋',
      'json': '🔧'
    };
    return icons[format] || '📄';
  }

  // Validation helpers
  validateReportData(reportData) {
    const errors = {};

    if (!reportData.title || reportData.title.trim() === '') {
      errors.title = 'Title is required';
    }

    if (!reportData.report_type) {
      errors.report_type = 'Report type is required';
    }

    if (!reportData.format) {
      errors.format = 'Format is required';
    }

    if (reportData.period_start && reportData.period_end) {
      const startDate = new Date(reportData.period_start);
      const endDate = new Date(reportData.period_end);
      
      if (startDate >= endDate) {
        errors.period_end = 'End date must be after start date';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  validateScheduleData(scheduleData) {
    const errors = {};

    if (!scheduleData.name || scheduleData.name.trim() === '') {
      errors.name = 'Name is required';
    }

    if (!scheduleData.template) {
      errors.template = 'Template is required';
    }

    if (!scheduleData.format) {
      errors.format = 'Format is required';
    }

    if (!scheduleData.cron_expression || scheduleData.cron_expression.trim() === '') {
      errors.cron_expression = 'Cron expression is required';
    } else {
      // Basic cron validation
      const cronParts = scheduleData.cron_expression.trim().split(/\s+/);
      if (cronParts.length !== 5) {
        errors.cron_expression = 'Cron expression must have 5 parts (minute hour day month day_of_week)';
      }
    }

    if (scheduleData.email_recipients && scheduleData.email_recipients.length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = scheduleData.email_recipients.filter(email => !emailRegex.test(email));
      if (invalidEmails.length > 0) {
        errors.email_recipients = `Invalid email addresses: ${invalidEmails.join(', ')}`;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // Get city-level demographic data (for city admins - shows child subcities)
  async getCityDemographicData(tenantId) {
    try {
      console.log('🏙️ Fetching city-level demographic data for tenant:', tenantId);
      console.log('🔄 Using city-dashboard/reports endpoint to aggregate from child subcities');

      // Use the city-specific endpoint that aggregates from child subcities
      const cacheBuster = Date.now();
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/city-dashboard/reports/?_=${cacheBuster}`);

      console.log('✅ City demographic data received:', response.data);
      console.log('🔍 Total citizens in response:', response.data?.total_citizens);
      console.log('🔍 Population by subcity:', response.data?.population_by_subcity);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching city demographic data:', error);
      // Fallback to regular endpoint
      try {
        const response = await axios.get(`/api/tenants/${tenantId}/citizens/dashboard/reports/`);
        return response.data;
      } catch (fallbackError) {
        console.error('❌ Fallback also failed:', fallbackError);
        throw error;
      }
    }
  }

  // Get subcity-level demographic data (for subcity admins - shows child kebeles)
  async getSubcityDemographicData(tenantId) {
    try {
      console.log('🏘️ Fetching subcity-level demographic data for tenant:', tenantId);
      console.log('🔄 Using the fixed subcity-dashboard/reports endpoint');

      // Debug authentication
      const token = localStorage.getItem('accessToken');
      console.log('🔐 Access token available:', !!token);
      console.log('🔐 Token length:', token ? token.length : 0);

      // Use the subcity-specific endpoint that we actually fixed
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/subcity-dashboard/reports/`);

      console.log('✅ Subcity demographic data received:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching subcity demographic data:', error);
      console.error('❌ Error response:', error.response);
      console.error('❌ Error status:', error.response?.status);
      console.error('❌ Error data:', error.response?.data);

      if (error.response?.status === 401) {
        console.error('🔐 Authentication error - token may be expired');
      }

      // Fallback to regular endpoint
      try {
        console.log('🔄 Trying fallback endpoint: dashboard/reports');
        const response = await axios.get(`/api/tenants/${tenantId}/citizens/dashboard/reports/`);
        console.log('✅ Fallback endpoint worked:', response.data);
        return response.data;
      } catch (fallbackError) {
        console.error('❌ Fallback also failed:', fallbackError);
        console.error('❌ Fallback error status:', fallbackError.response?.status);
        console.error('❌ Fallback error data:', fallbackError.response?.data);
        throw error;
      }
    }
  }

  // Get city-level anonymized citizens data
  async getCityAnonymizedCitizensData(tenantId) {
    try {
      console.log('🏙️ Fetching city-level anonymized citizens data for tenant:', tenantId);
      const cacheBuster = Date.now();
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/city-anonymized-citizens/?_=${cacheBuster}`);
      console.log('✅ City anonymized citizens response:', response.status, response.data?.length || 0, 'records');
      return response.data || [];
    } catch (error) {
      console.error('❌ Error fetching city-level anonymized citizens data:', error);
      console.error('❌ Error details:', error.response?.status, error.response?.data);
      return [];
    }
  }

  // Get subcity-level anonymized citizens data
  async getSubcityAnonymizedCitizensData(tenantId) {
    try {
      console.log('🏘️ Fetching subcity-level anonymized citizens data for tenant:', tenantId);
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/subcity-anonymized-citizens/`);
      return response.data || [];
    } catch (error) {
      console.error('❌ Error fetching subcity-level anonymized citizens data:', error);
      return [];
    }
  }

  // Get anonymized citizens data for research export
  async getAnonymizedCitizensData(tenantId) {
    try {
      console.log('🔍 Fetching anonymized citizens data for tenant:', tenantId);
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/anonymized-citizens/`);
      return response.data || [];
    } catch (error) {
      console.error('❌ Error fetching anonymized citizens data:', error);
      return [];
    }
  }

  // Helper function to determine age group from age or date of birth
  getAgeGroup(ageOrDob) {
    let age;
    if (typeof ageOrDob === 'number') {
      age = ageOrDob;
    } else if (ageOrDob) {
      const birthDate = new Date(ageOrDob);
      const today = new Date();
      age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    } else {
      return 'Unknown';
    }

    if (age < 18) return '0-17';
    if (age < 25) return '18-24';
    if (age < 35) return '25-34';
    if (age < 45) return '35-44';
    if (age < 55) return '45-54';
    if (age < 65) return '55-64';
    return '65+';
  }

  // Generate sample anonymized data for testing/fallback
  generateSampleAnonymizedData(count = 50) {
    const genders = ['Male', 'Female'];
    const maritalStatuses = ['Single', 'Married', 'Divorced', 'Widowed'];
    const educationLevels = ['Primary', 'Secondary', 'Diploma', 'Degree', 'Masters', 'PhD'];
    const employmentStatuses = ['Employed', 'Unemployed', 'Self-employed', 'Student', 'Retired'];
    const ageGroups = ['18-24', '25-34', '35-44', '45-54', '55-64', '65+'];
    const idCardStatuses = ['PENDING', 'APPROVED', 'ISSUED', 'REJECTED'];
    const areas = ['Kebele 01', 'Kebele 02', 'Kebele 03', 'Kebele 04', 'Kebele 05'];

    return Array.from({ length: count }, (_, index) => ({
      record_id: `REC-${String(index + 1).padStart(6, '0')}`,
      age_group: ageGroups[Math.floor(Math.random() * ageGroups.length)],
      gender: genders[Math.floor(Math.random() * genders.length)],
      marital_status: maritalStatuses[Math.floor(Math.random() * maritalStatuses.length)],
      education_level: educationLevels[Math.floor(Math.random() * educationLevels.length)],
      employment_status: employmentStatuses[Math.floor(Math.random() * employmentStatuses.length)],
      area_name: areas[Math.floor(Math.random() * areas.length)],
      registration_date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString(),
      id_card_status: idCardStatuses[Math.floor(Math.random() * idCardStatuses.length)]
    }));
  }

  // Export anonymized citizens data as PDF
  async exportAnonymizedCitizensPDF(tenantId, anonymizedData) {
    try {
      console.log('📄 Exporting anonymized citizens data as PDF...');

      const exportData = {
        title: 'Anonymized Citizens Data Report',
        subtitle: `Research Export - ${new Date().toLocaleDateString()}`,
        data: anonymizedData,
        tenant_id: tenantId
      };

      const response = await axios.post(`/api/tenants/${tenantId}/citizens/export-anonymized-pdf/`, {}, {
        responseType: 'blob'
      });

      // Create download
      const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `anonymized-citizens-report-${new Date().toISOString().split('T')[0]}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      console.log('✅ PDF export completed successfully');
    } catch (error) {
      console.error('❌ PDF export failed:', error);
      throw error;
    }
  }

  // Export anonymized citizens data as Excel
  async exportAnonymizedCitizensExcel(tenantId, anonymizedData) {
    try {
      console.log('📊 Exporting anonymized citizens data as Excel...');

      const exportData = {
        title: 'Anonymized Citizens Data Report',
        subtitle: `Research Export - ${new Date().toLocaleDateString()}`,
        data: anonymizedData,
        tenant_id: tenantId
      };

      const response = await axios.post(`/api/tenants/${tenantId}/citizens/export-anonymized-excel/`, {}, {
        responseType: 'blob'
      });

      // Create download
      const url = window.URL.createObjectURL(new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `anonymized-citizens-report-${new Date().toISOString().split('T')[0]}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      console.log('✅ Excel export completed successfully');
    } catch (error) {
      console.error('❌ Excel export failed:', error);
      throw error;
    }
  }
}

export default new ReportsService();
